"use strict";(self.webpackChunkapp=self.webpackChunkapp||[]).push([[3316],{32928:(f,M,n)=>{n.d(M,{Y:()=>t});var e=n(44912),d=n(53229);function t(r=0,p=e.E){return r<0&&(r=0),(0,d.O)(r,r,p)}},82115:(f,M,n)=>{n.d(M,{o:()=>p});var e=n(2978),d=n(77575),t=n(77897),r=n(74657);let p=(()=>{class P{constructor(m,g,O){this.router=m,this.platform=g,this.modalCtrl=O}ngOnInit(){this.sabitouLink=this.platform.is("android")?"https://play.google.com/store/apps/details?id=lnd.cimencam.sabitou_construction&hl=en&gl=US":"https://apps.apple.com/us/app/sabitou-construction/id1453574772"}onNoClick(){this.modalCtrl.dismiss()}static{this.\u0275fac=function(g){return new(g||P)(e.rXU(d.Ix),e.rXU(t.OD),e.rXU(t.W3))}}static{this.\u0275cmp=e.VBU({type:P,selectors:[["app-redirect-to-sabitou"]],decls:17,vars:10,consts:[[1,"mat-dialog"],["src","/assets/images/sabitou.png",1,"img-container"],[1,"text-form"],["src","/assets/icons/institutions-logos-cimencam-binastore.svg",1,"lafrageimg"],[1,"mat-dialogButton"],[1,"button","back",3,"click"],[1,"button","download"],[3,"href"]],template:function(g,O){1&g&&(e.j41(0,"div",0)(1,"div"),e.nrm(2,"ion-img",1),e.j41(3,"ion-title"),e.EFF(4,"Sabitou Construction"),e.k0s(),e.j41(5,"ion-label",2),e.EFF(6),e.nI1(7,"translate"),e.k0s(),e.nrm(8,"ion-img",3),e.k0s(),e.j41(9,"div",4)(10,"div",5),e.bIt("click",function(){return O.onNoClick()}),e.EFF(11),e.nI1(12,"translate"),e.k0s(),e.j41(13,"div",6)(14,"a",7),e.EFF(15),e.nI1(16,"translate"),e.k0s()()()()),2&g&&(e.R7$(6),e.SpI(" ",e.bMT(7,4,"redirect-sabitou-modal.text")," "),e.R7$(5),e.SpI(" ",e.bMT(12,6,"button.cancel"),""),e.R7$(3),e.Y8G("href",O.sabitouLink,e.B4B),e.R7$(1),e.JRh(e.bMT(16,8,"button.download")))},dependencies:[t.KW,t.he,t.BC,r.D9],styles:["*[_ngcontent-%COMP%]{margin:0;padding:0}.header-md[_ngcontent-%COMP%]:after{background-image:none!important}.modal[_ngcontent-%COMP%]{--border-radius: calc(60 * var(--res))}.input-group[_ngcontent-%COMP%]{margin-top:calc(75 * var(--res))}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{--padding-start: 0;margin-bottom:calc(50 * var(--res));--placeholder-color: $color-six}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-input[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{font-family:Mont Regular;font-weight:400;font-size:calc(42 * var(--res));color:#1e1e1e}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{padding-top:calc(25 * var(--res));padding-bottom:calc(25 * var(--res))}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]{margin-bottom:calc(50 * var(--res));display:flex;align-items:center;justify-content:space-between;border-bottom-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-250, #c8c7cc)));border-bottom-width:.55px;border-bottom-style:solid}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{width:90%;margin-bottom:0;--border-color: transparent;--inner-border-width: 0px 0px 0px 0px;--border-style: none}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-checkbox[_ngcontent-%COMP%]{margin-top:5%}.bg-primary[_ngcontent-%COMP%]{color:#143c5d}.message[_ngcontent-%COMP%]{margin-bottom:calc(75 * var(--res))}.message[_ngcontent-%COMP%]   ion-title[_ngcontent-%COMP%]{font-weight:800;font-size:calc(70 * var(--res));padding-bottom:calc(25 * var(--res))}.message[_ngcontent-%COMP%]   ion-text[_ngcontent-%COMP%]{font-weight:400;color:#757474;display:block;line-height:1.5em!important}.btn--meduim[_ngcontent-%COMP%]{height:calc(130 * var(--res));font-size:calc(50 * var(--res));font-family:var(--mont-bold)}.btn--meduim[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:var(--mont-bold)}.btn--small[_ngcontent-%COMP%]{height:calc(100 * var(--res))}.btn--round[_ngcontent-%COMP%]{--border-radius: 50%;padding-inline-start:0;padding-inline-end:0;--padding-start: 0;--padding-end: 0}.btn--border-primary[_ngcontent-%COMP%]{--background: transparent;--background-activated: #c4e6d0;--border-color: var(--ion-color-primary);--border-style: solid;--border-width: 2px}.btn--border-secondary[_ngcontent-%COMP%]{--border-width: 2px;--background: transparent;--background-activated: #ddabb2;--border-color: var(--ion-color-secondary);--border-style: solid}.btn--upper[_ngcontent-%COMP%]{text-transform:uppercase}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]{overflow:hidden}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar{width:5px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-track{box-shadow:inset 0 0 5px #546e8d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background:#143c5d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover{background:#143c5d}.bottom-sheet-content[_ngcontent-%COMP%]{background:#fdfdfe;height:100%}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-thumbnail[_ngcontent-%COMP%]{margin-left:calc(31.25 * var(--res));background-color:#1e1e1e;--border-radius: 50%;--size: 23}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:Mont Bold;font-size:calc(45 * var(--res));text-align:center;color:#757474}.bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%], .bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]{padding:calc(75 * var(--res))}.font-size-5[_ngcontent-%COMP%]{font-size:calc(42 * var(--res))}.mat-dialog[_ngcontent-%COMP%]{padding:calc(41 * var(--res))}.mat-dialog[_ngcontent-%COMP%]   .img-container[_ngcontent-%COMP%]{width:100%}.mat-dialog[_ngcontent-%COMP%]   .mat-dialogButton[_ngcontent-%COMP%]{font-family:Mont Regular;display:flex;justify-content:space-around}.mat-dialog[_ngcontent-%COMP%]   .text-form[_ngcontent-%COMP%]{text-align:center;letter-spacing:0,5px;font-family:Mont Regular;margin-bottom:16px}.mat-dialog[_ngcontent-%COMP%]   .lafrageimg[_ngcontent-%COMP%]{margin-bottom:20px;width:69%;margin-left:14%}.mat-dialog[_ngcontent-%COMP%]   .button[_ngcontent-%COMP%]{font-size:14px;padding:12px 19px;font-family:Mont Bold;border-radius:4px}.mat-dialog[_ngcontent-%COMP%]   .back[_ngcontent-%COMP%]{background-color:#143c5d;color:#fff;border:#a3a6a8 1px solid}.mat-dialog[_ngcontent-%COMP%]   .download[_ngcontent-%COMP%]{background-color:#ffc500}.mat-dialog[_ngcontent-%COMP%]   .download[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]{text-decoration:none;color:#fff}"]})}}return P})()},26843:(f,M,n)=>{n.d(M,{v:()=>m});var e=n(73308),d=n(35025),t=n.n(d),r=n(2978),p=n(77897),P=n(14599),h=n(74657);let m=(()=>{class g{constructor(_,c,o){this.platform=_,this.storageSrv=c,this.popOver=o,this.isContentShown=!1}ngOnInit(){this.storeLink=this.platform.is("android")?"https://play.google.com/store/apps/details?id=com.clickcadyst.mobile":"https://apps.apple.com/us/app/clic cadyst/id1467838902"}cancel(){var _=this;return(0,e.A)(function*(){yield _.storageSrv.store("lastDeniedUpdateApp",t()().toDate().getTime()),_.popOver.dismiss()})()}static{this.\u0275fac=function(c){return new(c||g)(r.rXU(p.OD),r.rXU(P.n),r.rXU(p.IE))}}static{this.\u0275cmp=r.VBU({type:g,selectors:[["app-update-app-modal"]],decls:18,vars:10,consts:[[1,"dialog-content"],[1,"phone-logo"],[1,"phone"],[1,"text"],[1,"mcm-btn-navigate-container"],[1,"mc-btn","full-width"],[1,"store-link",3,"href"],[1,"mcm-btn-navigate-container-close"],["color","light",1,"mc-btn","full-width","white-update",3,"click"],[1,"store-link"]],template:function(c,o){1&c&&(r.j41(0,"div",0)(1,"div",1),r.nrm(2,"div",2),r.k0s(),r.j41(3,"h1"),r.EFF(4,"CLICK CADYST"),r.k0s(),r.j41(5,"p",3),r.EFF(6),r.nI1(7,"translate"),r.k0s(),r.j41(8,"div",4)(9,"ion-button",5)(10,"a",6),r.EFF(11),r.nI1(12,"translate"),r.k0s()()(),r.j41(13,"div",7)(14,"ion-button",8),r.bIt("click",function(){return o.cancel()}),r.j41(15,"a",9),r.EFF(16),r.nI1(17,"translate"),r.k0s()()()()),2&c&&(r.R7$(6),r.SpI(" ",r.bMT(7,4,"home-page.update-text")," "),r.R7$(4),r.Y8G("href",o.storeLink,r.B4B),r.R7$(1),r.SpI(" ",r.bMT(12,6,"home-page.uptodate"),""),r.R7$(5),r.SpI(" ",r.bMT(17,8,"home-page.update-cancel")," "))},dependencies:[p.Jm,h.D9],styles:["*[_ngcontent-%COMP%]{margin:0;padding:0}.header-md[_ngcontent-%COMP%]:after{background-image:none!important}.modal[_ngcontent-%COMP%]{--border-radius: calc(60 * var(--res))}.input-group[_ngcontent-%COMP%]{margin-top:calc(75 * var(--res))}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{--padding-start: 0;margin-bottom:calc(50 * var(--res));--placeholder-color: $color-six}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-input[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{font-family:Mont Regular;font-weight:400;font-size:calc(42 * var(--res));color:#1e1e1e}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{padding-top:calc(25 * var(--res));padding-bottom:calc(25 * var(--res))}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]{margin-bottom:calc(50 * var(--res));display:flex;align-items:center;justify-content:space-between;border-bottom-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-250, #c8c7cc)));border-bottom-width:.55px;border-bottom-style:solid}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{width:90%;margin-bottom:0;--border-color: transparent;--inner-border-width: 0px 0px 0px 0px;--border-style: none}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-checkbox[_ngcontent-%COMP%]{margin-top:5%}.bg-primary[_ngcontent-%COMP%]{color:#143c5d}.message[_ngcontent-%COMP%]{margin-bottom:calc(75 * var(--res))}.message[_ngcontent-%COMP%]   ion-title[_ngcontent-%COMP%]{font-weight:800;font-size:calc(70 * var(--res));padding-bottom:calc(25 * var(--res))}.message[_ngcontent-%COMP%]   ion-text[_ngcontent-%COMP%]{font-weight:400;color:#757474;display:block;line-height:1.5em!important}.btn--meduim[_ngcontent-%COMP%]{height:calc(130 * var(--res));font-size:calc(50 * var(--res));font-family:var(--mont-bold)}.btn--meduim[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:var(--mont-bold)}.btn--small[_ngcontent-%COMP%]{height:calc(100 * var(--res))}.btn--round[_ngcontent-%COMP%]{--border-radius: 50%;padding-inline-start:0;padding-inline-end:0;--padding-start: 0;--padding-end: 0}.btn--border-primary[_ngcontent-%COMP%]{--background: transparent;--background-activated: #c4e6d0;--border-color: var(--ion-color-primary);--border-style: solid;--border-width: 2px}.btn--border-secondary[_ngcontent-%COMP%]{--border-width: 2px;--background: transparent;--background-activated: #ddabb2;--border-color: var(--ion-color-secondary);--border-style: solid}.btn--upper[_ngcontent-%COMP%]{text-transform:uppercase}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]{overflow:hidden}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar{width:5px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-track{box-shadow:inset 0 0 5px #546e8d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background:#143c5d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover{background:#143c5d}.bottom-sheet-content[_ngcontent-%COMP%]{background:#fdfdfe;height:100%}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-thumbnail[_ngcontent-%COMP%]{margin-left:calc(31.25 * var(--res));background-color:#1e1e1e;--border-radius: 50%;--size: 23}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:Mont Bold;font-size:calc(45 * var(--res));text-align:center;color:#757474}.bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%], .bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]{padding:calc(75 * var(--res))}.font-size-5[_ngcontent-%COMP%]{font-size:calc(42 * var(--res))}.dialog-content[_ngcontent-%COMP%]{padding:16px}.dialog-content[_ngcontent-%COMP%]   .phone-logo[_ngcontent-%COMP%]{height:200px;width:100%;height:22vh;display:flex;align-items:center;justify-content:center}.dialog-content[_ngcontent-%COMP%]   .phone-logo[_ngcontent-%COMP%]   .phone[_ngcontent-%COMP%]{height:100%;width:100%;background-image:url(updatepasta-icon.52e001d874e40d5e.png);background-repeat:no-repeat;background-position:center;background-size:contain}.dialog-content[_ngcontent-%COMP%]   .text[_ngcontent-%COMP%]{font-family:Mont Regular;font-size:.7em;text-align:center;margin:0}.dialog-content[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]{margin:10px 0 25px;font-family:Mont Bold;font-size:12px;text-align:center}.dialog-content[_ngcontent-%COMP%]   .mcm-btn-navigate-container[_ngcontent-%COMP%]{bottom:16px;position:unset;width:100%;margin:25px 0 0}.dialog-content[_ngcontent-%COMP%]   .mcm-btn-navigate-container[_ngcontent-%COMP%]   .mc-btn[_ngcontent-%COMP%]{width:100%;margin-top:4em}.dialog-content[_ngcontent-%COMP%]   .mcm-btn-navigate-container[_ngcontent-%COMP%]   .store-link[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;text-decoration:none;color:#fff;width:100%;height:100%;text-transform:uppercase;font-family:Mont Regular}.dialog-content[_ngcontent-%COMP%]   .mcm-btn-navigate-container-close[_ngcontent-%COMP%]{bottom:16px;position:unset;width:100%;margin:10px 0 0;color:#ffffffbd}.dialog-content[_ngcontent-%COMP%]   .mcm-btn-navigate-container-close[_ngcontent-%COMP%]   .mc-btn[_ngcontent-%COMP%]{width:100%}.dialog-content[_ngcontent-%COMP%]   .mcm-btn-navigate-container-close[_ngcontent-%COMP%]   .store-link[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;text-decoration:none;color:--ion-color-primary;width:100%;height:100%;text-transform:uppercase;font-family:Mont Regular}.dialog-content[_ngcontent-%COMP%]   .mcm-btn-navigate-container-close[_ngcontent-%COMP%]   .store-link.cancel[_ngcontent-%COMP%]{color:#143c5d}"]})}}return g})()},39316:(f,M,n)=>{n.d(M,{b:()=>g});var e=n(73308),d=n(26409),t=n(94934),r=n(45312),p=n(2978),P=n(82571),h=n(33607),m=n(77897);let g=(()=>{class O{constructor(c,o,s,i){this.http=c,this.commonSrv=o,this.baseUrlService=s,this.toastController=i,this.prices=[],this.currentDataProductScan=[],this.dataQrCode=[],this.url=this.baseUrlService.getOrigin()+r.c.basePath+"products"}getProducts(c){var o=this;return(0,e.A)(function*(){try{let s=new d.Nl;return c?.limit&&(s=s.append("limit",c?.limit)),yield(0,t.s)(o.http.get(o.url,{params:s}))}catch(s){const l={message:o.commonSrv.getError("",s).message,color:"danger"};return yield o.commonSrv.showToast(l),s}})()}getProduct(c){var o=this;return(0,e.A)(function*(){try{return yield(0,t.s)(o.http.get(`${o.url}/${c}`))}catch(s){const l={message:o.commonSrv.getError("",s).message,color:"danger"};return yield o.commonSrv.showToast(l),s}})()}static{this.\u0275fac=function(o){return new(o||O)(p.KVO(d.Qq),p.KVO(P.h),p.KVO(h.K),p.KVO(m.K_))}}static{this.\u0275prov=p.jDH({token:O,factory:O.\u0275fac,providedIn:"root"})}}return O})()},31909:(f,M,n)=>{n.d(M,{B:()=>O});var e=n(73308),d=n(99987),t=n(2978),r=n(77897),p=n(13217),P=n(74657),h=n(82571),m=n(56610);function g(_,c){if(1&_&&(t.j41(0,"span"),t.EFF(1),t.k0s()),2&_){const o=t.XpG();t.R7$(1),t.SpI(" : ",o.sponsorName,"")}}let O=(()=>{class _{constructor(o,s,i,l){this.modalController=o,this.fidelityProgramService=s,this.translateService=i,this.commonSrv=l}ngOnInit(){}closeModal(o){this.modalController.dismiss(o)}acceptSponsor(){var o=this;return(0,e.A)(function*(){(yield o.fidelityProgramService.acceptInvitation(o.invitation._id,o.commonSrv?.user?._id).catch(i=>(console.error("Error accepting sponsor:",i),o.commonSrv.showToast({message:o.translateService.currentLang===d.T.French?"Une erreur est survenue.":"An error occurred.",color:"danger"}),null)))&&(o.commonSrv.showToast({message:o.translateService.currentLang===d.T.French?"Parrainage accept\xe9 avec succ\xe8s!":"Sponsorship successfully accepted!",color:"success"}),o.closeModal({accepted:!0}))})()}static{this.\u0275fac=function(s){return new(s||_)(t.rXU(r.W3),t.rXU(p._),t.rXU(P.c$),t.rXU(h.h))}}static{this.\u0275cmp=t.VBU({type:_,selectors:[["app-modal"]],inputs:{invitation:"invitation",sponsorName:"sponsorName"},decls:13,vars:10,consts:[[1,"sponsorship-container"],[1,"icon-container"],["src","/assets/images/referral.png","alt","Sponsor"],[1,"sponsorship-title"],[1,"sponsorship-text"],[4,"ngIf"],["expand","block",1,"accept-button",3,"click"]],template:function(s,i){1&s&&(t.j41(0,"div",0)(1,"div",1),t.nrm(2,"img",2),t.k0s(),t.j41(3,"h2",3),t.EFF(4),t.nI1(5,"translate"),t.k0s(),t.j41(6,"p",4),t.EFF(7),t.nI1(8,"translate"),t.DNE(9,g,2,1,"span",5),t.k0s(),t.j41(10,"ion-button",6),t.bIt("click",function(){return i.acceptSponsor()}),t.EFF(11),t.nI1(12,"translate"),t.k0s()()),2&s&&(t.R7$(4),t.JRh(t.bMT(5,4,"fidelity-page.sponsor-title")),t.R7$(3),t.SpI("",t.bMT(8,6,"fidelity-page.sponsor-desc")," "),t.R7$(2),t.Y8G("ngIf",i.sponsorName),t.R7$(2),t.SpI(" ",t.bMT(12,8,"button.accept")," "))},dependencies:[r.Jm,m.bT,P.D9],styles:['@charset "UTF-8";.sponsorship-container[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;justify-content:center;padding:2rem;text-align:center;height:100%;background-color:#fff;border-radius:10px;box-shadow:0 2px 10px #0000001a}.icon-container[_ngcontent-%COMP%]{width:80px;height:80px;background-color:#e6f0ff;border-radius:50%;display:flex;align-items:center;justify-content:center;margin-bottom:1rem}.sponsorship-icon[_ngcontent-%COMP%]{font-size:40px;color:var(--clr-secondary-400)}.sponsorship-title[_ngcontent-%COMP%]{color:#333;font-size:24px;margin:0;margin-bottom:.5rem}.sponsorship-text[_ngcontent-%COMP%]{color:#666;font-size:16px;margin:0;margin-bottom:2rem}.sponsorship-text[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{font-family:var(--mont-bold)}.accept-button[_ngcontent-%COMP%]{--background: var(--clr-secondary-400);--border-radius: 8px;--padding-top: 1rem;--padding-bottom: 1rem;--padding-start: 2rem;--padding-end: 2rem;font-weight:500;text-transform:uppercase;width:100%;max-width:300px}ion-content[_ngcontent-%COMP%]{--background: transparent}@media (max-width: 320px){.sponsorship-container[_ngcontent-%COMP%]{padding:1rem}.icon-container[_ngcontent-%COMP%]{width:60px;height:60px}.sponsorship-icon[_ngcontent-%COMP%]{font-size:30px}.sponsorship-title[_ngcontent-%COMP%]{font-size:20px}.sponsorship-text[_ngcontent-%COMP%]{font-size:14px}}']})}}return _})()},80153:(f,M,n)=>{n.d(M,{s:()=>d});var d=function(t){return t[t.PENDING=200]="PENDING",t[t.VALIDATED=300]="VALIDATED",t[t.EXPIRED=400]="EXPIRED",t}(d||{})},76060:(f,M,n)=>{n.d(M,{I:()=>e,a:()=>d});var e=function(t){return t[t.CREATE=100]="CREATE",t[t.READ=200]="READ",t[t.DELETE=300]="DELETE",t}(e||{}),d=function(t){return t[t.FEEDBACK=100]="FEEDBACK",t[t.ORDER=200]="ORDER",t[t.GENERAL=300]="GENERAL",t}(d||{})},13217:(f,M,n)=>{n.d(M,{_:()=>c});var e=n(73308),d=n(26409),t=n(45312),r=n(94934),p=n(79801),P=n(80153),h=n(99987),m=n(2978),g=n(33607),O=n(82571),_=n(62049);let c=(()=>{class o{constructor(i,l,a,u){this.baseUrl=i,this.http=l,this.commonSrv=a,this.translateSrv=u,this.ratioOfPointsByVolume=.2,this.POINTS_MATRIX={AMIGO:{5:1,25:5,50:10},COLOMBE:{5:2,25:8,50:15},PELICAN:{5:5,25:12,50:25}},this.base_url=`${this.baseUrl.getOrigin()}${t.c.basePath}`}calculateTotalPointsOrder(i,l){const a=l?.points?.status||p.Th.AMIGO;return i.reduce((u,C)=>{const v=C?.packaging?.unit?.value||0,b=C?.quantity||0;return u+(this.POINTS_MATRIX[a]?.[v]||0)*b},0)}getPoints(i){var l=this;return(0,e.A)(function*(){let a=new d.Nl;const{companyId:u}=i;return u&&(a=a.append("companyId",u)),yield(0,r.s)(l.http.get(`${l.base_url}loyalty-program/points`,{params:a}))})()}getBenefitRewards(i){var l=this;return(0,e.A)(function*(){let a=new d.Nl;return i?.monthly&&(a=a.append("monthly",JSON.stringify(i.monthly))),i?.statusValue&&(a=a.append("statusValue",JSON.stringify(i?.statusValue))),i?.annual&&(a=a.append("annual",JSON.stringify(i.annual))),i?.punctual&&(a=a.append("punctual",JSON.stringify(i.punctual))),yield(0,r.s)(l.http.get(`${l.base_url}advantages`,{params:a}))})()}acceptInvitation(i,l){var a=this;return(0,e.A)(function*(){return yield(0,r.s)(a.http.patch(`${a.base_url}invitations/${i}/accept`,{referredUserId:l}))})()}getInvitations(i){var l=this;return(0,e.A)(function*(){let a=new d.Nl;const{limit:u,status:C,prospectTel:v}=i;return u&&(a=a.append("limit",u)),v&&(a=a.append("prospectTel",v)),C&&(a=a.append("status",C)),yield(0,r.s)(l.http.get(`${l.base_url}invitations`,{params:a}))})()}sendReferralInvitation(i,l){var a=this;return(0,e.A)(function*(){try{const u={referrerId:i,prospectTel:l};return yield(0,r.s)(a.http.post(`${a.base_url}invitations/${i}/invite-referral`,u))}catch(u){const C=a.translateSrv.currentLang===h.T.French?"Impossible d'envoyer l'invitation de parrainage":"Failed to send referral invitation";return yield a.commonSrv.showToast({message:u?.error?.message??a.commonSrv.getError(C,u).message,color:"danger"}),u}})()}getPendingInvitationForUser(i){var l=this;return(0,e.A)(function*(){let a=new d.Nl;return a=a.set("prospectTel",+i),a=a.append("status",P.s.PENDING),yield(0,r.s)(l.http.get(`${l.base_url}invitations/pending-invitation`,{params:a}))})()}getDisplayedPoints(i,l){return"pending"===l?i?.unValidated||0:i?.totalPoints||0}getDisplayedPointsLabel(i){return"pending"===i?"fidelity-page.waiting":"fidelity-page.total-points"}static{this.\u0275fac=function(l){return new(l||o)(m.KVO(g.K),m.KVO(d.Qq),m.KVO(O.h),m.KVO(_.E))}}static{this.\u0275prov=m.jDH({token:o,factory:o.\u0275fac,providedIn:"root"})}}return o})()},96514:(f,M,n)=>{n.d(M,{I:()=>g});var e=n(73308),d=n(26409),t=n(94934),r=n(35025),p=n.n(r),P=n(45312),h=n(2978),m=n(33607);let g=(()=>{class O{constructor(c,o){this.http=c,this.baseUrlService=o,this.url=this.baseUrlService.getOrigin()+P.c.basePath}getMessages(c){var o=this;return(0,e.A)(function*(){try{let s=new d.Nl;const{email:i,date:l,notifications:a,userId:u,enable:C=!0}=c;return l?.startDate&&l?.endDate&&(s=s.append("startDate",p()(l?.startDate).format("YYYY-MM-DD")),s=s.append("endDate",p()(l?.endDate).format("YYYY-MM-DD"))),i&&(s=s.append("feedback.user.email",i)),u&&(s=s.append("userId",u)),a&&(s=s.append("_id",a)),s=s.append("enable",C),yield(0,t.s)(o.http.get(`${o.url}notifications`,{params:s}))}catch(s){return s}})()}makeRead(c){var o=this;return(0,e.A)(function*(){try{return yield(0,t.s)(o.http.patch(`${o.url}notifications/${c}`,{}))}catch(s){return s}})()}deleteNotifications(c){var o=this;return(0,e.A)(function*(){return(0,t.s)(o.http.patch(`${o.url}notifications/${c[0]}/delete`,{ids:c}))})()}static{this.\u0275fac=function(o){return new(o||O)(h.KVO(d.Qq),h.KVO(m.K))}}static{this.\u0275prov=h.jDH({token:O,factory:O.\u0275fac,providedIn:"root"})}}return O})()},97130:(f,M,n)=>{n.d(M,{I:()=>m});var e=n(73308),d=n(45312),t=n(2978),r=n(77897),p=n(49957),P=n(82571),h=n(14599);let m=(()=>{class g{constructor(_,c,o,s){this.platform=_,this.appVersion=c,this.commonService=o,this.storageService=s}isUpToDate(){var _=this;return(0,e.A)(function*(){let c;try{c=yield _.getAppVersion()}catch(o){"cordova_not_available"===o&&(c=_.platform.is("android")?d.c?.appVersionAndroid:d.c?.appVersionIos)}finally{return _.isLatestVersion(c)}})()}isLatestVersion(_){var c=this;return(0,e.A)(function*(){let o;console.log("appVersion:",_);try{o=yield c.getMinimalVersion()}catch{o=c.platform.is("android")?d.c?.appVersionAndroid:d.c?.appVersionIos}finally{return console.log(o,_),c.isNewerVersion(o,_)}})()}getMinimalVersion(){var _=this;return(0,e.A)(function*(){return _.currentPlatform=_.platform.is("android")?"android":"ios",yield _.commonService.getMinimalAppVersion(_.currentPlatform)})()}getAppVersion(){var _=this;return(0,e.A)(function*(){return yield _.appVersion.getVersionNumber()})()}isNewerVersion(_,c){const o=_?.split("."),s=c?.split("."),i=Math?.max(o?.length,s?.length);for(let l=0;l<i;l++){let a=parseInt(o[l]||"0",10),u=parseInt(s[l]||"0",10);if(u>a)return!0;if(u<a)return!1}return!0}static{this.\u0275fac=function(c){return new(c||g)(t.KVO(r.OD),t.KVO(p.U),t.KVO(P.h),t.KVO(h.n))}}static{this.\u0275prov=t.jDH({token:g,factory:g.\u0275fac,providedIn:"root"})}}return g})()}}]);