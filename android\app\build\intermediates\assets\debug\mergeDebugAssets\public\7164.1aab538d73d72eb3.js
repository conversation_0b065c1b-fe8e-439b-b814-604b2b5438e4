"use strict";(self.webpackChunkapp=self.webpackChunkapp||[]).push([[7164],{39441:(y,M,n)=>{n.r(M),n.d(M,{CataloguePageModule:()=>I});var d=n(56610),_=n(37222),r=n(77897),o=n(77575),p=n(73308),O=n(76060),C=n(26843),t=n(2978),u=n(39316),h=n(82571),c=n(97130),i=n(14599),a=n(96514),s=n(56071);function b(g,A){1&g&&(t.j41(0,"div",12)(1,"div",14)(2,"ion-thumbnail",15),t.nrm(3,"ion-skeleton-text",16),t.k0s()()()),2&g&&(t.R7$(3),t.Y8G("animated",!0))}function P(g,A){if(1&g){const e=t.RV6();t.j41(0,"div",17)(1,"app-product-card",18),t.bIt("click",function(){const v=t.eBV(e).$implicit,x=t.XpG();return t.Njj(x.showDetail(null==v||null==v.product?null:v.product._id))}),t.k0s()()}if(2&g){const e=A.$implicit,l=t.XpG();t.R7$(1),t.Y8G("item",e)("isCatalogue",l.isCatalogue)}}const f=[{path:"",component:(()=>{class g{constructor(e,l,m,v,x,U,T,w,R,L){this.router=e,this.platform=l,this.modalCtrl=m,this.productSrv=v,this.popOver=x,this.commonService=U,this.versionService=T,this.storageService=w,this.notificationService=R,this.location=L,this.listCard=[],this.skeletons=[1,2,3,4],this.loading=!0,this.totalImages=0,this.loadedImages=0,this.isCatalogue=!0,this.listTab=[{id:0,title:"Tous"},{id:1,title:"50 kg"},{id:2,title:"25 kg"}],this.products=[],this.hasNotification=!1}ngOnInit(){var e=this;return(0,p.A)(function*(){e.loading=!0,e.commonService.showNav=!1,e.user=e.storageService.getUserConnected(),yield e.getNotificationsMessages(),yield e.getFirstProduct(),yield e.verifyAppUpToDate(),e.selectedItem=e.listTab[0].title,e.listCard=e.products.map(l=>{let m={};return m.product=l,m.unitPrice=e.randomInteger(8e3,12e3),m.packaging={label:"25 Kg"},m}),setTimeout(()=>{e.loading=!1},2e4)})()}handleClick(e){this.selectedItem=e?.title}trackByFn(e,l){return e}getNotificationsMessages(){var e=this;return(0,p.A)(function*(){const l={email:e.user?.email};e.dataNotification=(yield e.notificationService.getMessages(l)).data,e.hasNotification=!!e.dataNotification.find(m=>m?.status===O.I.CREATE)})()}getFirstProduct(){var e=this;return(0,p.A)(function*(){return e.products=(yield e.productSrv.getProducts({limit:20})).data,e.products})()}verifyAppUpToDate(){var e=this;return(0,p.A)(function*(){if(e.isAppMaintained)return;const l=yield e.versionService.isUpToDate();if(!l){e.isAppMaintained=!0;const m=yield e.popOver.create({component:C.v,cssClass:"updateModal"});m.present(),yield m.onWillDismiss(),e.isAppMaintained=!1}return l})()}back(){this.location.back()}showDetail(e){this.router.navigate(["/order/product-detail/"+e])}randomInteger(e,l){return Math.floor(Math.random()*(l-e+1))+e}static{this.\u0275fac=function(l){return new(l||g)(t.rXU(o.Ix),t.rXU(r.OD),t.rXU(r.W3),t.rXU(u.b),t.rXU(r.IE),t.rXU(h.h),t.rXU(c.I),t.rXU(i.n),t.rXU(a.I),t.rXU(d.aZ))}}static{this.\u0275cmp=t.VBU({type:g,selectors:[["app-catalogue"]],decls:22,vars:2,consts:[[1,"header"],[1,"header_nav"],[1,"header_nav--profil"],[3,"click"],["src","assets/icons/Arrow 1.png","alt",""],[1,"header_nav--icon"],[1,"icons_profil"],["src","assets/icons/bell.svg","alt","","routerLink","/navigation/notifications ",1,"header-img-end"],["src","assets/icons/Message.svg","alt","","routerLink","/navigation/feedback",1,"header-img-end"],["src","assets/icons/Profil2.png","alt","","routerLink","/navigation/account",1,"header-img-end"],["id","container"],["class","grid-product",4,"ngIf"],[1,"grid-product"],["class","image-container",4,"ngFor","ngForOf"],[1,"loading"],[1,"skeleton-cart"],[3,"animated"],[1,"image-container"],[1,"card",3,"item","isCatalogue","click"]],template:function(l,m){1&l&&(t.j41(0,"ion-header")(1,"div",0)(2,"div",1)(3,"div",2)(4,"div",3),t.bIt("click",function(){return m.back()}),t.nrm(5,"img",4),t.k0s(),t.j41(6,"div")(7,"span"),t.EFF(8,"Catalogue"),t.k0s()()(),t.j41(9,"div",5)(10,"div",6)(11,"div"),t.nrm(12,"img",7),t.k0s(),t.j41(13,"div"),t.nrm(14,"img",8),t.k0s(),t.j41(15,"div"),t.nrm(16,"img",9),t.k0s()()()()()(),t.j41(17,"ion-content")(18,"div",10),t.DNE(19,b,4,1,"div",11),t.j41(20,"div",12),t.DNE(21,P,2,2,"div",13),t.k0s()()()),2&l&&(t.R7$(19),t.Y8G("ngIf",m.loading),t.R7$(2),t.Y8G("ngForOf",m.listCard))},dependencies:[d.Sq,d.bT,r.W9,r.eU,r.ds,r.Zx,r.N7,o.Wk,s.V],styles:["*[_ngcontent-%COMP%]{margin:0;padding:0}.header-md[_ngcontent-%COMP%]:after{background-image:none!important}.modal[_ngcontent-%COMP%]{--border-radius: calc(60 * var(--res))}.input-group[_ngcontent-%COMP%]{margin-top:calc(75 * var(--res))}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{--padding-start: 0;margin-bottom:calc(50 * var(--res));--placeholder-color: $color-six}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-input[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{font-family:Mont Regular;font-weight:400;font-size:calc(42 * var(--res));color:#1e1e1e}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{padding-top:calc(25 * var(--res));padding-bottom:calc(25 * var(--res))}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]{margin-bottom:calc(50 * var(--res));display:flex;align-items:center;justify-content:space-between;border-bottom-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-250, #c8c7cc)));border-bottom-width:.55px;border-bottom-style:solid}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{width:90%;margin-bottom:0;--border-color: transparent;--inner-border-width: 0px 0px 0px 0px;--border-style: none}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-checkbox[_ngcontent-%COMP%]{margin-top:5%}.bg-primary[_ngcontent-%COMP%]{color:#143c5d}.message[_ngcontent-%COMP%]{margin-bottom:calc(75 * var(--res))}.message[_ngcontent-%COMP%]   ion-title[_ngcontent-%COMP%]{font-weight:800;font-size:calc(70 * var(--res));padding-bottom:calc(25 * var(--res))}.message[_ngcontent-%COMP%]   ion-text[_ngcontent-%COMP%]{font-weight:400;color:#757474;display:block;line-height:1.5em!important}.btn--meduim[_ngcontent-%COMP%]{height:calc(130 * var(--res));font-size:calc(50 * var(--res));font-family:var(--mont-bold)}.btn--meduim[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:var(--mont-bold)}.btn--small[_ngcontent-%COMP%]{height:calc(100 * var(--res))}.btn--round[_ngcontent-%COMP%]{--border-radius: 50%;padding-inline-start:0;padding-inline-end:0;--padding-start: 0;--padding-end: 0}.btn--border-primary[_ngcontent-%COMP%]{--background: transparent;--background-activated: #c4e6d0;--border-color: var(--ion-color-primary);--border-style: solid;--border-width: 2px}.btn--border-secondary[_ngcontent-%COMP%]{--border-width: 2px;--background: transparent;--background-activated: #ddabb2;--border-color: var(--ion-color-secondary);--border-style: solid}.btn--upper[_ngcontent-%COMP%]{text-transform:uppercase}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]{overflow:hidden}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar{width:5px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-track{box-shadow:inset 0 0 5px #546e8d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background:#143c5d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover{background:#143c5d}.bottom-sheet-content[_ngcontent-%COMP%]{background:#fdfdfe;height:100%}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-thumbnail[_ngcontent-%COMP%]{margin-left:calc(31.25 * var(--res));background-color:#1e1e1e;--border-radius: 50%;--size: 23}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:Mont Bold;font-size:calc(45 * var(--res));text-align:center;color:#757474}.bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%], .bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]{padding:calc(75 * var(--res))}.font-size-5[_ngcontent-%COMP%]{font-size:calc(42 * var(--res))}ion-header[_ngcontent-%COMP%]{--background: transparent }ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]{--padding-start: var(--space-5);--padding-end: var(--space-5);--padding-top: var(--space-5);--border-color: transparent}#container[_ngcontent-%COMP%]{background-color:#fff;display:flex;align-items:center;justify-content:center;width:100%}#container[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%]{font-family:Mont Regular;font-weight:100;z-index:1}#container[_ngcontent-%COMP%]   .grid-product[_ngcontent-%COMP%]{display:flex;flex-wrap:wrap;gap:2rem}#container[_ngcontent-%COMP%]   .image-container[_ngcontent-%COMP%]{position:relative}#container[_ngcontent-%COMP%]   .skeleton-cart[_ngcontent-%COMP%]{position:absolute;top:0;left:0;width:100%;height:100%;display:flex;align-items:center;justify-content:center;z-index:1}#container[_ngcontent-%COMP%]   .card[_ngcontent-%COMP%]   ion-img[_ngcontent-%COMP%]{display:none;width:100px;height:100px}#container[_ngcontent-%COMP%]   .card[_ngcontent-%COMP%]   ion-img.loaded[_ngcontent-%COMP%]{display:block}.suggestion[_ngcontent-%COMP%], .tendance[_ngcontent-%COMP%]{margin-top:10px}.header[_ngcontent-%COMP%]{position:relative;height:100px;width:100%;border-radius:0 0 15px 15px;background:url(bg.084da384672e9d5b.png) center no-repeat;background-position:center;background-repeat:no-repeat;background-size:cover;display:flex;flex-direction:column;align-items:center;justify-content:flex-start;gap:25px}.header[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]:nth-child(1){position:absolute;top:30px;height:40px;width:100%}.header_nav[_ngcontent-%COMP%]{padding:1rem;display:flex;align-items:center;justify-content:space-between}.img_profil[_ngcontent-%COMP%]{height:43px;width:43px;border-radius:50%;overflow:hidden}.img_profil[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{object-fit:cover}.header_nav--profil[_ngcontent-%COMP%]{display:flex;align-items:center;gap:9px}.header_nav--profil[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]:nth-child(2)   span[_ngcontent-%COMP%]{font-size:1.25rem;font-weight:600;font-weight:1.598rem;color:#fff}.icons_profil[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:flex-end;gap:10px;width:100px}ion-header[_ngcontent-%COMP%]{border-radius:0 0 15px 15px!important;--background-color: #fff}.header-img-end[_ngcontent-%COMP%]{color:#fff;height:28px;width:28px}"]})}}return g})()}];let D=(()=>{class g{static{this.\u0275fac=function(l){return new(l||g)}}static{this.\u0275mod=t.$C({type:g})}static{this.\u0275inj=t.G2t({imports:[o.iI.forChild(f),o.iI]})}}return g})();var k=n(93887);let I=(()=>{class g{static{this.\u0275fac=function(l){return new(l||g)}}static{this.\u0275mod=t.$C({type:g})}static{this.\u0275inj=t.G2t({imports:[d.MD,_.YN,r.bv,D,k.G]})}}return g})()},26843:(y,M,n)=>{n.d(M,{v:()=>t});var d=n(73308),_=n(35025),r=n.n(_),o=n(2978),p=n(77897),O=n(14599),C=n(74657);let t=(()=>{class u{constructor(c,i,a){this.platform=c,this.storageSrv=i,this.popOver=a,this.isContentShown=!1}ngOnInit(){this.storeLink=this.platform.is("android")?"https://play.google.com/store/apps/details?id=com.clickcadyst.mobile":"https://apps.apple.com/us/app/clic cadyst/id1467838902"}cancel(){var c=this;return(0,d.A)(function*(){yield c.storageSrv.store("lastDeniedUpdateApp",r()().toDate().getTime()),c.popOver.dismiss()})()}static{this.\u0275fac=function(i){return new(i||u)(o.rXU(p.OD),o.rXU(O.n),o.rXU(p.IE))}}static{this.\u0275cmp=o.VBU({type:u,selectors:[["app-update-app-modal"]],decls:18,vars:10,consts:[[1,"dialog-content"],[1,"phone-logo"],[1,"phone"],[1,"text"],[1,"mcm-btn-navigate-container"],[1,"mc-btn","full-width"],[1,"store-link",3,"href"],[1,"mcm-btn-navigate-container-close"],["color","light",1,"mc-btn","full-width","white-update",3,"click"],[1,"store-link"]],template:function(i,a){1&i&&(o.j41(0,"div",0)(1,"div",1),o.nrm(2,"div",2),o.k0s(),o.j41(3,"h1"),o.EFF(4,"CLICK CADYST"),o.k0s(),o.j41(5,"p",3),o.EFF(6),o.nI1(7,"translate"),o.k0s(),o.j41(8,"div",4)(9,"ion-button",5)(10,"a",6),o.EFF(11),o.nI1(12,"translate"),o.k0s()()(),o.j41(13,"div",7)(14,"ion-button",8),o.bIt("click",function(){return a.cancel()}),o.j41(15,"a",9),o.EFF(16),o.nI1(17,"translate"),o.k0s()()()()),2&i&&(o.R7$(6),o.SpI(" ",o.bMT(7,4,"home-page.update-text")," "),o.R7$(4),o.Y8G("href",a.storeLink,o.B4B),o.R7$(1),o.SpI(" ",o.bMT(12,6,"home-page.uptodate"),""),o.R7$(5),o.SpI(" ",o.bMT(17,8,"home-page.update-cancel")," "))},dependencies:[p.Jm,C.D9],styles:["*[_ngcontent-%COMP%]{margin:0;padding:0}.header-md[_ngcontent-%COMP%]:after{background-image:none!important}.modal[_ngcontent-%COMP%]{--border-radius: calc(60 * var(--res))}.input-group[_ngcontent-%COMP%]{margin-top:calc(75 * var(--res))}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{--padding-start: 0;margin-bottom:calc(50 * var(--res));--placeholder-color: $color-six}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-input[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{font-family:Mont Regular;font-weight:400;font-size:calc(42 * var(--res));color:#1e1e1e}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{padding-top:calc(25 * var(--res));padding-bottom:calc(25 * var(--res))}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]{margin-bottom:calc(50 * var(--res));display:flex;align-items:center;justify-content:space-between;border-bottom-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-250, #c8c7cc)));border-bottom-width:.55px;border-bottom-style:solid}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{width:90%;margin-bottom:0;--border-color: transparent;--inner-border-width: 0px 0px 0px 0px;--border-style: none}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-checkbox[_ngcontent-%COMP%]{margin-top:5%}.bg-primary[_ngcontent-%COMP%]{color:#143c5d}.message[_ngcontent-%COMP%]{margin-bottom:calc(75 * var(--res))}.message[_ngcontent-%COMP%]   ion-title[_ngcontent-%COMP%]{font-weight:800;font-size:calc(70 * var(--res));padding-bottom:calc(25 * var(--res))}.message[_ngcontent-%COMP%]   ion-text[_ngcontent-%COMP%]{font-weight:400;color:#757474;display:block;line-height:1.5em!important}.btn--meduim[_ngcontent-%COMP%]{height:calc(130 * var(--res));font-size:calc(50 * var(--res));font-family:var(--mont-bold)}.btn--meduim[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:var(--mont-bold)}.btn--small[_ngcontent-%COMP%]{height:calc(100 * var(--res))}.btn--round[_ngcontent-%COMP%]{--border-radius: 50%;padding-inline-start:0;padding-inline-end:0;--padding-start: 0;--padding-end: 0}.btn--border-primary[_ngcontent-%COMP%]{--background: transparent;--background-activated: #c4e6d0;--border-color: var(--ion-color-primary);--border-style: solid;--border-width: 2px}.btn--border-secondary[_ngcontent-%COMP%]{--border-width: 2px;--background: transparent;--background-activated: #ddabb2;--border-color: var(--ion-color-secondary);--border-style: solid}.btn--upper[_ngcontent-%COMP%]{text-transform:uppercase}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]{overflow:hidden}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar{width:5px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-track{box-shadow:inset 0 0 5px #546e8d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background:#143c5d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover{background:#143c5d}.bottom-sheet-content[_ngcontent-%COMP%]{background:#fdfdfe;height:100%}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-thumbnail[_ngcontent-%COMP%]{margin-left:calc(31.25 * var(--res));background-color:#1e1e1e;--border-radius: 50%;--size: 23}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:Mont Bold;font-size:calc(45 * var(--res));text-align:center;color:#757474}.bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%], .bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]{padding:calc(75 * var(--res))}.font-size-5[_ngcontent-%COMP%]{font-size:calc(42 * var(--res))}.dialog-content[_ngcontent-%COMP%]{padding:16px}.dialog-content[_ngcontent-%COMP%]   .phone-logo[_ngcontent-%COMP%]{height:200px;width:100%;height:22vh;display:flex;align-items:center;justify-content:center}.dialog-content[_ngcontent-%COMP%]   .phone-logo[_ngcontent-%COMP%]   .phone[_ngcontent-%COMP%]{height:100%;width:100%;background-image:url(updatepasta-icon.52e001d874e40d5e.png);background-repeat:no-repeat;background-position:center;background-size:contain}.dialog-content[_ngcontent-%COMP%]   .text[_ngcontent-%COMP%]{font-family:Mont Regular;font-size:.7em;text-align:center;margin:0}.dialog-content[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]{margin:10px 0 25px;font-family:Mont Bold;font-size:12px;text-align:center}.dialog-content[_ngcontent-%COMP%]   .mcm-btn-navigate-container[_ngcontent-%COMP%]{bottom:16px;position:unset;width:100%;margin:25px 0 0}.dialog-content[_ngcontent-%COMP%]   .mcm-btn-navigate-container[_ngcontent-%COMP%]   .mc-btn[_ngcontent-%COMP%]{width:100%;margin-top:4em}.dialog-content[_ngcontent-%COMP%]   .mcm-btn-navigate-container[_ngcontent-%COMP%]   .store-link[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;text-decoration:none;color:#fff;width:100%;height:100%;text-transform:uppercase;font-family:Mont Regular}.dialog-content[_ngcontent-%COMP%]   .mcm-btn-navigate-container-close[_ngcontent-%COMP%]{bottom:16px;position:unset;width:100%;margin:10px 0 0;color:#ffffffbd}.dialog-content[_ngcontent-%COMP%]   .mcm-btn-navigate-container-close[_ngcontent-%COMP%]   .mc-btn[_ngcontent-%COMP%]{width:100%}.dialog-content[_ngcontent-%COMP%]   .mcm-btn-navigate-container-close[_ngcontent-%COMP%]   .store-link[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;text-decoration:none;color:--ion-color-primary;width:100%;height:100%;text-transform:uppercase;font-family:Mont Regular}.dialog-content[_ngcontent-%COMP%]   .mcm-btn-navigate-container-close[_ngcontent-%COMP%]   .store-link.cancel[_ngcontent-%COMP%]{color:#143c5d}"]})}}return u})()},76060:(y,M,n)=>{n.d(M,{I:()=>d,a:()=>_});var d=function(r){return r[r.CREATE=100]="CREATE",r[r.READ=200]="READ",r[r.DELETE=300]="DELETE",r}(d||{}),_=function(r){return r[r.FEEDBACK=100]="FEEDBACK",r[r.ORDER=200]="ORDER",r[r.GENERAL=300]="GENERAL",r}(_||{})},11244:(y,M,n)=>{n.d(M,{F:()=>_});var d=n(2978);let _=(()=>{class r{transform(p){return console.log(),`${p?.slice(0,1)?.toLocaleUpperCase()+p?.slice(1)?.toLocaleLowerCase()}`}static{this.\u0275fac=function(O){return new(O||r)}}static{this.\u0275pipe=d.EJ8({name:"capitalize",type:r,pure:!0})}}return r})()},96514:(y,M,n)=>{n.d(M,{I:()=>u});var d=n(73308),_=n(26409),r=n(94934),o=n(35025),p=n.n(o),O=n(45312),C=n(2978),t=n(33607);let u=(()=>{class h{constructor(i,a){this.http=i,this.baseUrlService=a,this.url=this.baseUrlService.getOrigin()+O.c.basePath}getMessages(i){var a=this;return(0,d.A)(function*(){try{let s=new _.Nl;const{email:b,date:P,notifications:E,userId:f,enable:D=!0}=i;return P?.startDate&&P?.endDate&&(s=s.append("startDate",p()(P?.startDate).format("YYYY-MM-DD")),s=s.append("endDate",p()(P?.endDate).format("YYYY-MM-DD"))),b&&(s=s.append("feedback.user.email",b)),f&&(s=s.append("userId",f)),E&&(s=s.append("_id",E)),s=s.append("enable",D),yield(0,r.s)(a.http.get(`${a.url}notifications`,{params:s}))}catch(s){return s}})()}makeRead(i){var a=this;return(0,d.A)(function*(){try{return yield(0,r.s)(a.http.patch(`${a.url}notifications/${i}`,{}))}catch(s){return s}})()}deleteNotifications(i){var a=this;return(0,d.A)(function*(){return(0,r.s)(a.http.patch(`${a.url}notifications/${i[0]}/delete`,{ids:i}))})()}static{this.\u0275fac=function(a){return new(a||h)(C.KVO(_.Qq),C.KVO(t.K))}}static{this.\u0275prov=C.jDH({token:h,factory:h.\u0275fac,providedIn:"root"})}}return h})()},97130:(y,M,n)=>{n.d(M,{I:()=>t});var d=n(73308),_=n(45312),r=n(2978),o=n(77897),p=n(49957),O=n(82571),C=n(14599);let t=(()=>{class u{constructor(c,i,a,s){this.platform=c,this.appVersion=i,this.commonService=a,this.storageService=s}isUpToDate(){var c=this;return(0,d.A)(function*(){let i;try{i=yield c.getAppVersion()}catch(a){"cordova_not_available"===a&&(i=c.platform.is("android")?_.c?.appVersionAndroid:_.c?.appVersionIos)}finally{return c.isLatestVersion(i)}})()}isLatestVersion(c){var i=this;return(0,d.A)(function*(){let a;console.log("appVersion:",c);try{a=yield i.getMinimalVersion()}catch{a=i.platform.is("android")?_.c?.appVersionAndroid:_.c?.appVersionIos}finally{return console.log(a,c),i.isNewerVersion(a,c)}})()}getMinimalVersion(){var c=this;return(0,d.A)(function*(){return c.currentPlatform=c.platform.is("android")?"android":"ios",yield c.commonService.getMinimalAppVersion(c.currentPlatform)})()}getAppVersion(){var c=this;return(0,d.A)(function*(){return yield c.appVersion.getVersionNumber()})()}isNewerVersion(c,i){const a=c?.split("."),s=i?.split("."),b=Math?.max(a?.length,s?.length);for(let P=0;P<b;P++){let E=parseInt(a[P]||"0",10),f=parseInt(s[P]||"0",10);if(f>E)return!0;if(f<E)return!1}return!0}static{this.\u0275fac=function(i){return new(i||u)(r.KVO(o.OD),r.KVO(p.U),r.KVO(O.h),r.KVO(C.n))}}static{this.\u0275prov=r.jDH({token:u,factory:u.\u0275fac,providedIn:"root"})}}return u})()}}]);