<app-header-connect></app-header-connect>

<ion-content [scrollEvents]="true" [fullscreen]="true" [scrollY]="true" class="scroll">

  <div id="container" *ngIf="!isOtpGenerate" [formGroup]="loginOtpForm">
    <span class="login">{{'signin-page.title' | translate}}</span>

    <div class="ion-text-center message login-message">
      <ion-text class="ion-padding-vertical">
        {{'signin-page.description' | translate}}
      </ion-text>
      <ion-img class="password-key" src="../assets/logos/Password.svg"></ion-img>
      <!-- <div class="key-otp-bloc">
        <ion-img class="password-key" [src]="'../assets/logos/key-otp.svg'"></ion-img>
      </div> -->
      <ion-text class="text-message">
        {{'signin-page.text-message' | translate}}
      </ion-text>
      <div [hidden]="tabOption !== 'phone'" class="phone-input-container">
        <div class="phone-input">
          <!-- <ion-input type="tel" placeholder="691 73 99 16" formControlName="phone" clearInput
            [readonly]="isLoading" class="custom-input"></ion-input> -->
          <input type="tel" appPhoneFormat placeholder="650 00 00 00" formControlName="phone" clearInput
            [readonly]="isLoading" class="custom-input">
        </div>
        <div class="change-language" (click)="changeLanguage()">
          {{'signin-page.change-language' | translate}}
        </div>

      </div>
    </div>
  </div>

  <div id="container" *ngIf="isOtpGenerate" [formGroup]="otpForm">
    <span class="login">{{'signin-page.modal-otp-description.title' | translate}}</span>

    <div class="ion-text-center message login-message">
      <!-- <ion-text class="ion-padding-vertical">
        {{'signin-page.modal-otp-description.instruction' | translate}}
      </ion-text> -->
      <ion-img class="password-key" [src]="'../assets/logos/otp.svg'"></ion-img>
      <ion-text class="text-message">
        {{'signin-page.modal-otp-description.label' | translate}}
      </ion-text>

      <!-- Indicateur de lecture automatique -->
      <div *ngIf="isListening" class="auto-detection-indicator">
        <ion-spinner name="circles" color="primary"></ion-spinner>
        <ion-text color="primary">
          <small>Détection automatique en cours...</small>
        </ion-text>
      </div>

      <div [hidden]="tabOption !== 'phone'" class="phone-input-container">
        <div class="phone-input">
          <input type="number" placeholder="1 2 3 4" formControlName="code" clearInput [readonly]="isLoading"
            class="custom-input" maxlength="7">
        </div>

        <!-- Boutons de contrôle auto-fill (développement uniquement) -->
        <div class="otp-controls" style="margin-top: 10px; text-align: center;">
          <ion-button *ngIf="!isListening" fill="outline" size="small" (click)="testOtpSimulation()">
            <ion-label>Test OTP</ion-label>
          </ion-button>
          <ion-button fill="clear" size="small" (click)="toggleOtpAutoFill()">
            <ion-label>{{ otpAutoFillEnabled ? 'Désactiver' : 'Activer' }} Auto-fill</ion-label>
          </ion-button>
        </div>
      </div>
    </div>
    <div class="otp-actions">

      <div class="no-otp">
        {{'signin-page.modal-otp-description.resend-otp' | translate}}
        <div class="resend-otp" (click)="resendCode()">
          <a>{{'signin-page.modal-otp-description.resend' | translate}}</a>
        </div>
      </div>
      <div class="bloc-elt">
        <div class="change-phone">
          <a class="change-phone-action"
            (click)="goBackToPhoneInput()">{{'signin-page.modal-otp-description.change-phone' | translate}}</a>
        </div>
      </div>

    </div>
  </div>
</ion-content>

<ion-footer>
  <div class="btn btn-primary">
    <ion-button *ngIf="!isOtpGenerate" expand="block" (click)="login()"
      disabled="{{loginOtpForm.status === 'INVALID'}}">
      {{ 'signin-page.modal-otp-description.button-confirm' | translate}}
      <ion-spinner *ngIf="isLoading" name="bubbles"></ion-spinner>
    </ion-button>

    <ion-button *ngIf="isOtpGenerate" expand="block" disabled="{{otpForm?.status === 'INVALID'}}" (click)="verifyOtp()">
      {{ 'signin-page.modal-otp-description.button-login' | translate}}
      <ion-spinner *ngIf="isLoading" name="bubbles"></ion-spinner>
    </ion-button>
  </div>
</ion-footer>