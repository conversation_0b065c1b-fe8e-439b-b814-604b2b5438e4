"use strict";(self.webpackChunkapp=self.webpackChunkapp||[]).push([[8686],{57024:(N,v,i)=>{i.r(v),i.d(v,{FirstStepPageRoutingModule:()=>l});var b=i(77575),O=i(73308),d=i(58133),c=i(37222),t=i(99987),e=i(2978),y=i(82571),_=i(14599),n=i(62049),h=i(77897),F=i(511),m=i(74657);let S=(()=>{class f{constructor(g,C,M,R,w){this.router=g,this.route=C,this.commonSrv=M,this.storageService=R,this.translateService=w,this.signUpForm=new c.gE({firstName:new c.MJ("",[c.k0.required]),lastName:new c.MJ("",[c.k0.required]),cni:new c.MJ("",[c.k0.required]),nui:new c.MJ("",[c.k0.required]),tel:new c.MJ("",[c.k0.required,c.k0.min(8)]),email:new c.MJ("",[c.k0.required,c.k0.min(3),c.k0.email])}),this.userCategorie=d.s,this.userType=d.s.Particular,this.buttonText="",this.tab={retailer:"Cr\xe9er un compte revendeur",companyUser:"Cr\xe9er un compte compagnie",particular:"Cr\xe9er un compte particulier",employeecimencam:"Cr\xe9er un compte employ\xe9"},this.tabForEnglish={retailer:"Create a retailer account",companyUser:"Create a company account",particular:"Create an individual account",employeecimencam:"Create an employee account"}}ngOnInit(){this.route.snapshot.params.userType&&(this.userType=this.route.snapshot.params.userType|d.s.Particular);const g=this.commonSrv.getUserType(this.userType);this.tab=t.T.French===this.translateService.currentLang?this.tab:this.tabForEnglish,this.buttonText=$localize`${this.tab[g]}`,this.signUpForm.reset()}nextStep(){var g=this;return(0,O.A)(function*(){if(g.userType===d.s.EmployeeLapasta&&g.signUpForm.get("email").value.indexOf("cadyst")<=-1)return yield g.commonSrv.showToast({message:g.translateService.currentLang===t.T.French?"Veuillez renseigner un email valide (<EMAIL>)":"Please enter a valid email address (<EMAIL>)",color:"danger"});g.storageService.store("user",JSON.stringify({...g.signUpForm.value})),g.router.navigate(["/authentication/signup/second-step/",g.userType])})()}static{this.\u0275fac=function(C){return new(C||f)(e.rXU(b.Ix),e.rXU(b.nX),e.rXU(y.h),e.rXU(_.n),e.rXU(n.E))}}static{this.\u0275cmp=e.VBU({type:f,selectors:[["app-first-step"]],decls:48,vars:28,consts:[[3,"viewLogo","buttonText"],[1,"scroll",3,"scrollEvents","fullscreen","scrollY"],["id","container"],[1,"ion-text-center"],["color","primary"],[3,"formGroup","ngSubmit"],[1,"input-group"],["position","floating"],["formControlName","lastName","clearInput","","required","lastName"],["clearInput","","formControlName","firstName"],["formControlName","tel","type","tel","clearInput",""],["formControlName","cni","clearInput",""],["formControlName","nui","clearInput",""],["type","email","formControlName","email","clearInput",""],["type","submit","hidden","true"],["submit",""],["slot","fixed","horizontal","center","vertical","bottom"],["color","primary",3,"disabled","click"],["src","assets/icons/next-white.svg",1,"ion-text-center"]],template:function(C,M){if(1&C){const R=e.RV6();e.nrm(0,"app-header-connect",0),e.j41(1,"ion-content",1)(2,"div",2)(3,"div",3)(4,"ion-text"),e.EFF(5),e.nI1(6,"translate"),e.j41(7,"ion-label",4),e.EFF(8,"6 54 90 00 00"),e.k0s()()(),e.j41(9,"form",5),e.bIt("ngSubmit",function(){return M.nextStep()}),e.j41(10,"div",6)(11,"ion-item")(12,"ion-label",7),e.EFF(13),e.nI1(14,"translate"),e.k0s(),e.nrm(15,"ion-input",8),e.k0s(),e.j41(16,"ion-item")(17,"ion-label",7),e.EFF(18),e.nI1(19,"translate"),e.k0s(),e.nrm(20,"ion-input",9),e.k0s(),e.j41(21,"ion-item")(22,"ion-label",7),e.EFF(23),e.nI1(24,"translate"),e.k0s(),e.nrm(25,"ion-input",10),e.k0s(),e.j41(26,"ion-item")(27,"ion-label",7),e.EFF(28),e.nI1(29,"translate"),e.k0s(),e.nrm(30,"ion-input",11),e.k0s(),e.j41(31,"ion-item")(32,"ion-label",7),e.EFF(33),e.nI1(34,"translate"),e.k0s(),e.nrm(35,"ion-input",12),e.k0s(),e.j41(36,"ion-item")(37,"ion-label",7),e.EFF(38,"Email *"),e.k0s(),e.nrm(39,"ion-input",13),e.k0s()(),e.nrm(40,"button",14,15),e.k0s()(),e.j41(42,"ion-fab",16)(43,"ion-fab-button",17),e.bIt("click",function(){e.eBV(R);const $=e.sdS(41);return e.Njj($.click())}),e.nrm(44,"ion-img",18),e.k0s(),e.j41(45,"ion-label"),e.EFF(46),e.nI1(47,"translate"),e.k0s()()()}2&C&&(e.Y8G("viewLogo",!1)("buttonText",M.buttonText),e.R7$(1),e.Y8G("scrollEvents",!0)("fullscreen",!0)("scrollY",!0),e.R7$(4),e.SpI(" ",e.bMT(6,14,"signup-page.first-step.description")," "),e.R7$(4),e.Y8G("formGroup",M.signUpForm),e.R7$(4),e.SpI("",e.bMT(14,16,M.userType===M.userCategorie.Retailer?"signup-page.first-step.sponsor":"signup-page.first-step.lastName")," *"),e.R7$(5),e.SpI("",e.bMT(19,18,"signup-page.first-step.firstName")," *"),e.R7$(5),e.SpI("",e.bMT(24,20,"signup-page.first-step.phone")," *"),e.R7$(5),e.SpI("",e.bMT(29,22,"signup-page.first-step.numberCni")," *"),e.R7$(5),e.SpI("",e.bMT(34,24,"signup-page.first-step.numberNui")," *"),e.R7$(10),e.Y8G("disabled",M.signUpForm.invalid),e.R7$(3),e.SpI(" ",e.bMT(47,26,"signup-page.first-step.step-label")," 1/2 "))},dependencies:[c.qT,c.BC,c.cb,c.YS,h.W9,h.Q8,h.YW,h.KW,h.$w,h.uz,h.he,h.IO,h.Gw,c.j4,c.JD,F.f,m.D9],styles:["*[_ngcontent-%COMP%]{margin:0;padding:0}.header-md[_ngcontent-%COMP%]:after{background-image:none!important}.modal[_ngcontent-%COMP%]{--border-radius: calc(60 * var(--res))}.input-group[_ngcontent-%COMP%]{margin-top:calc(75 * var(--res))}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{--padding-start: 0;margin-bottom:calc(50 * var(--res));--placeholder-color: $color-six}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-input[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{font-family:Mont Regular;font-weight:400;font-size:calc(42 * var(--res));color:#1e1e1e}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{padding-top:calc(25 * var(--res));padding-bottom:calc(25 * var(--res))}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]{margin-bottom:calc(50 * var(--res));display:flex;align-items:center;justify-content:space-between;border-bottom-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-250, #c8c7cc)));border-bottom-width:.55px;border-bottom-style:solid}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{width:90%;margin-bottom:0;--border-color: transparent;--inner-border-width: 0px 0px 0px 0px;--border-style: none}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-checkbox[_ngcontent-%COMP%]{margin-top:5%}.bg-primary[_ngcontent-%COMP%]{color:#143c5d}.message[_ngcontent-%COMP%]{margin-bottom:calc(75 * var(--res))}.message[_ngcontent-%COMP%]   ion-title[_ngcontent-%COMP%]{font-weight:800;font-size:calc(70 * var(--res));padding-bottom:calc(25 * var(--res))}.message[_ngcontent-%COMP%]   ion-text[_ngcontent-%COMP%]{font-weight:400;color:#757474;display:block;line-height:1.5em!important}.btn--meduim[_ngcontent-%COMP%]{height:calc(130 * var(--res));font-size:calc(50 * var(--res));font-family:var(--mont-bold)}.btn--meduim[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:var(--mont-bold)}.btn--small[_ngcontent-%COMP%]{height:calc(100 * var(--res))}.btn--round[_ngcontent-%COMP%]{--border-radius: 50%;padding-inline-start:0;padding-inline-end:0;--padding-start: 0;--padding-end: 0}.btn--border-primary[_ngcontent-%COMP%]{--background: transparent;--background-activated: #c4e6d0;--border-color: var(--ion-color-primary);--border-style: solid;--border-width: 2px}.btn--border-secondary[_ngcontent-%COMP%]{--border-width: 2px;--background: transparent;--background-activated: #ddabb2;--border-color: var(--ion-color-secondary);--border-style: solid}.btn--upper[_ngcontent-%COMP%]{text-transform:uppercase}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]{overflow:hidden}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar{width:5px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-track{box-shadow:inset 0 0 5px #546e8d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background:#143c5d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover{background:#143c5d}.bottom-sheet-content[_ngcontent-%COMP%]{background:#fdfdfe;height:100%}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-thumbnail[_ngcontent-%COMP%]{margin-left:calc(31.25 * var(--res));background-color:#1e1e1e;--border-radius: 50%;--size: 23}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:Mont Bold;font-size:calc(45 * var(--res));text-align:center;color:#757474}.bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%], .bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]{padding:calc(75 * var(--res))}.font-size-5[_ngcontent-%COMP%]{font-size:calc(42 * var(--res))}ion-content[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]{margin-bottom:3.625rem}ion-content[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   ion-text[_ngcontent-%COMP%]{font-family:Mont Regular;font-weight:400;text-align:justify;color:#1e1e1e;display:block}ion-content[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   ion-text[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:Mont Bold}ion-content[_ngcontent-%COMP%]   ion-fab[_ngcontent-%COMP%]{margin-left:-25px}ion-content[_ngcontent-%COMP%]   ion-fab[_ngcontent-%COMP%]   ion-fab-button[_ngcontent-%COMP%]{width:50px;height:50px}ion-content[_ngcontent-%COMP%]   ion-fab[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{margin-top:calc(75 * var(--res));font-family:Mont Light;font-weight:600;font-size:.625rem;--color: black;color:#000}"]})}}return f})();const T=[{path:"",component:S},{path:":userType",component:S}];let l=(()=>{class f{static{this.\u0275fac=function(C){return new(C||f)}}static{this.\u0275mod=e.$C({type:f})}static{this.\u0275inj=e.G2t({imports:[b.iI.forChild(T),b.iI]})}}return f})()},78686:(N,v,i)=>{i.r(v),i.d(v,{FormSignupAccountPageModule:()=>T});var b=i(56610),O=i(77575),d=i(2978);const c=[{path:"first-step",loadChildren:()=>Promise.resolve().then(i.bind(i,57024)).then(l=>l.FirstStepPageRoutingModule)},{path:"second-step",loadChildren:()=>Promise.resolve().then(i.bind(i,43540)).then(l=>l.SecondStepPageRoutingModule)},{path:"",redirectTo:"first-step",pathMatch:"full"}];let t=(()=>{class l{static{this.\u0275fac=function(g){return new(g||l)}}static{this.\u0275mod=d.$C({type:l})}static{this.\u0275inj=d.G2t({imports:[O.iI.forChild(c),O.iI]})}}return l})();var e=i(37222),y=i(77897),_=i(57024),n=i(93887),h=i(74657);let F=(()=>{class l{static{this.\u0275fac=function(g){return new(g||l)}}static{this.\u0275mod=d.$C({type:l})}static{this.\u0275inj=d.G2t({imports:[b.MD,e.YN,y.bv,_.FirstStepPageRoutingModule,e.X1,n.G,h.h]})}}return l})();var m=i(43540);let S=(()=>{class l{static{this.\u0275fac=function(g){return new(g||l)}}static{this.\u0275mod=d.$C({type:l})}static{this.\u0275inj=d.G2t({imports:[b.MD,e.YN,y.bv,m.SecondStepPageRoutingModule,e.X1,n.G,h.h]})}}return l})(),T=(()=>{class l{static{this.\u0275fac=function(g){return new(g||l)}}static{this.\u0275mod=d.$C({type:l})}static{this.\u0275inj=d.G2t({imports:[b.MD,t,F,S]})}}return l})()},43540:(N,v,i)=>{i.r(v),i.d(v,{SecondStepPageRoutingModule:()=>W});var b=i(77575),O=i(73308),d=i(44444),c=i(99987),t=i(37222),e=i(58133);class y{constructor(){}static onlyChar(){return u=>""==u.value||new RegExp("^[a-zA-Z ]*$").test(u.value)?null:{onlyChar:!0}}static mustMatch(u,o){return s=>{const p=s.controls[o];if(!p.errors||p.errors.mustMatch)return p.setErrors(s.controls[u].value!==p.value?{mustMatch:!0}:null),null}}}var _=i(39963),n=i(2978),h=i(56610),F=i(23985),m=i(77897),S=i(82571),T=i(14599),l=i(93860),f=i(62049),k=i(511),g=i(74657);function C(r,u){1&r&&(n.j41(0,"ion-item")(1,"ion-label",7),n.EFF(2),n.nI1(3,"translate"),n.k0s(),n.nrm(4,"ion-input",29),n.nI1(5,"translate"),n.k0s()),2&r&&(n.R7$(2),n.JRh(n.bMT(3,2,"signup-page.second-step.input-profession-label")),n.R7$(2),n.FS9("placeholder",n.bMT(5,4,"signup-page.second-step.input-profession-placeholder")))}function M(r,u){1&r&&(n.j41(0,"ion-item")(1,"ion-label",7),n.EFF(2),n.nI1(3,"translate"),n.k0s(),n.nrm(4,"ion-input",30),n.nI1(5,"translate"),n.k0s()),2&r&&(n.R7$(2),n.JRh(n.bMT(3,2,"signup-page.second-step.input-social-reason-label")),n.R7$(2),n.FS9("placeholder",n.bMT(5,4,"signup-page.second-step.input-social-reason-placeholder")))}function R(r,u){1&r&&(n.j41(0,"ion-item")(1,"ion-label",7),n.EFF(2),n.nI1(3,"translate"),n.k0s(),n.j41(4,"ion-select",31)(5,"ion-select-option",32),n.EFF(6),n.nI1(7,"translate"),n.k0s(),n.j41(8,"ion-select-option",32),n.EFF(9),n.nI1(10,"translate"),n.k0s()()()),2&r&&(n.R7$(2),n.JRh(n.bMT(3,5,"signup-page.second-step.select-status-employee")),n.R7$(3),n.Y8G("value",!1),n.R7$(1),n.JRh(n.bMT(7,7,"signup-page.second-step.not-retired")),n.R7$(2),n.Y8G("value",!0),n.R7$(1),n.JRh(n.bMT(10,9,"signup-page.second-step.retired")))}function w(r,u){if(1&r&&(n.j41(0,"ion-select-option",32),n.EFF(1),n.k0s()),2&r){const o=u.$implicit;n.Y8G("value",o.LIBELLE),n.R7$(1),n.JRh(o.LIBELLE)}}function $(r,u){if(1&r){const o=n.RV6();n.j41(0,"ion-item")(1,"ion-label",7),n.EFF(2),n.nI1(3,"translate"),n.k0s(),n.j41(4,"ion-select",33),n.bIt("ionChange",function(){n.eBV(o);const a=n.XpG();let p;return n.Njj(a.getServices(null==a.signUpForm||null==(p=a.signUpForm.get("direction"))?null:p.value))}),n.DNE(5,w,2,2,"ion-select-option",11),n.k0s()()}if(2&r){const o=n.XpG();n.R7$(2),n.JRh(n.bMT(3,2,"signup-page.second-step.select-direction-label")),n.R7$(3),n.Y8G("ngForOf",o.directions)}}function G(r,u){if(1&r&&(n.j41(0,"ion-select-option",32),n.EFF(1),n.k0s()),2&r){const o=u.$implicit;n.Y8G("value",o.SERVICES),n.R7$(1),n.SpI(" ",o.SERVICES," ")}}function Y(r,u){if(1&r){const o=n.RV6();n.j41(0,"ion-item")(1,"ion-label",7),n.EFF(2),n.nI1(3,"translate"),n.k0s(),n.j41(4,"ion-select",34),n.bIt("ionChange",function(){n.eBV(o);const a=n.XpG();let p;return n.Njj(a.getPost(null==a.signUpForm||null==(p=a.signUpForm.get("service"))?null:p.value))}),n.DNE(5,G,2,2,"ion-select-option",11),n.k0s()()}if(2&r){const o=n.XpG();n.R7$(2),n.JRh(n.bMT(3,2,"signup-page.second-step.select-service-label")),n.R7$(3),n.Y8G("ngForOf",o.services)}}function L(r,u){if(1&r&&(n.j41(0,"ion-select-option",32),n.EFF(1),n.k0s()),2&r){const o=u.$implicit;n.Y8G("value",o.FONCTIONS),n.R7$(1),n.JRh(o.FONCTIONS)}}function z(r,u){if(1&r&&(n.j41(0,"ion-item")(1,"ion-label",7),n.EFF(2),n.nI1(3,"translate"),n.k0s(),n.j41(4,"ion-select",35),n.DNE(5,L,2,2,"ion-select-option",11),n.k0s()()),2&r){const o=n.XpG();n.R7$(2),n.JRh(n.bMT(3,2,"signup-page.second-step.select-position-label")),n.R7$(3),n.Y8G("ngForOf",o.posts)}}function D(r,u){1&r&&(n.j41(0,"ion-item")(1,"ion-label",7),n.EFF(2),n.nI1(3,"translate"),n.k0s(),n.nrm(4,"ion-input",36),n.nI1(5,"translate"),n.k0s()),2&r&&(n.R7$(2),n.JRh(n.bMT(3,2,"signup-page.second-step.input-serial-number-label")),n.R7$(2),n.FS9("placeholder",n.bMT(5,4,"signup-page.second-step.input-serial-number-placeholder")))}function B(r,u){if(1&r&&(n.j41(0,"ion-select-option",32),n.EFF(1),n.k0s()),2&r){const o=u.$implicit;n.FS9("value",o),n.R7$(1),n.SpI("",o," ")}}function X(r,u){if(1&r&&(n.j41(0,"ion-select-option",32),n.EFF(1),n.k0s()),2&r){const o=u.$implicit;n.FS9("value",o),n.R7$(1),n.SpI("",o," ")}}function V(r,u){1&r&&n.nrm(0,"ion-spinner",37)}let U=(()=>{class r{constructor(o,s,a,p,P,j,I,x,E){this.router=o,this.location=s,this.route=a,this.userService=p,this.modalCtrl=P,this.commonService=j,this.storageService=I,this.authService=x,this.translateService=E,this.employeeType=d.PB,this.userCategorie=e.s,this.signUpEmployeCimencamForm=new t.gE({password:new t.MJ("",[t.k0.required,t.k0.min(3)]),confirmPassword:new t.MJ("",[t.k0.required,t.k0.min(3)]),isRetired:new t.MJ("",[t.k0.required]),direction:new t.MJ("",[t.k0.required]),service:new t.MJ("",[t.k0.required]),position:new t.MJ("",[t.k0.required]),matricule:new t.MJ("",[t.k0.required]),employeeType:new t.MJ(d.PB.NORMAL,[t.k0.required]),address:new t.gE({city:new t.MJ("",[t.k0.required]),district:new t.MJ("",[t.k0.required]),region:new t.MJ("",[t.k0.required]),commercialRegion:new t.MJ("",[t.k0.required])})},[y.mustMatch("password","confirmPassword")]),this.signUpRetailerForm=new t.gE({password:new t.MJ("",[t.k0.required,t.k0.min(5),t.k0.minLength(5)]),confirmPassword:new t.MJ("",[t.k0.required,t.k0.min(5),t.k0.minLength(5)]),socialReason:new t.MJ("",[t.k0.required]),address:new t.gE({city:new t.MJ("",[t.k0.required]),district:new t.MJ("",[t.k0.required]),region:new t.MJ("",[t.k0.required]),commercialRegion:new t.MJ("",[t.k0.required])})},[y.mustMatch("password","confirmPassword")]),this.signUpParticularForm=new t.gE({password:new t.MJ("",[t.k0.required,t.k0.min(3)]),confirmPassword:new t.MJ("",[t.k0.required,t.k0.min(3)]),profession:new t.MJ("",[t.k0.required]),address:new t.gE({city:new t.MJ("",[t.k0.required]),district:new t.MJ("",[t.k0.required]),region:new t.MJ("",[t.k0.required]),commercialRegion:new t.MJ("",[t.k0.required])})},[y.mustMatch("password","confirmPassword")]),this.validateOperation=c.T.French===this.translateService.currentLang?"Valider l'op\xe9ration":"Validate the operation",this.userCategory=e.s,this.userType=e.s.Particular,this.buttonText="",this.typeOfConfirmPassword="password",this.typeOfPassword="password",this.isLoading=!1,this.tab={retailer:{value:"Cr\xe9er un compte revendeur",form:this.signUpRetailerForm},particular:{value:"Cr\xe9er un compte particulier",form:this.signUpParticularForm},employeecimencam:{value:"Cr\xe9er un compte employ\xe9",form:this.signUpEmployeCimencamForm}},this.tabForEnglish={retailer:{value:"Create a reseller account",form:this.signUpRetailerForm},particular:{value:"Create an individual account",form:this.signUpParticularForm},employeecimencam:{value:"Create an employee account",form:this.signUpEmployeCimencamForm}}}ngOnInit(){var o=this;return(0,O.A)(function*(){o.route.snapshot.params.userType&&(o.userType=o.route.snapshot.params.userType|e.s.Particular);const s=o.commonService.getUserType(o.userType);o.tab=c.T.French===o.translateService.currentLang?o.tab:o.tabForEnglish,o.buttonText=$localize`${o.tab[s].value}`,o.signUpForm=o.tab[s].form,o.userType===e.s.EmployeeLapasta&&o.getDirection()})()}regionChange(o){const s=Object.entries(this.commonService.commercialRegions).find(([a,p])=>p.includes(o.detail.value))?.[0];return this.commercialRegion=s,this.commercialRegion}getDirection(){var o=this;return(0,O.A)(function*(){o.directions=yield o.userService.getDirection()})()}getServices(o){this.services=this.directions?.find(s=>s.LIBELLE===o)?.services}getPost(o){this.posts=this.services.find(s=>s.SERVICES===o).fonctions}back(){this.location.back()}cancel(){this.router.navigateByUrl("/")}showBottomSheet(o){var s=this;return(0,O.A)(function*(){if(s.isLoading=!0,"cancel"!=o){let a=JSON.parse(s.storageService.load("user")),p=s.signUpForm.value;delete p?.confirmPassword,s.user={...a,...p,category:s.userType};const P="cancel"===o?"Annuler l'op\xe9ration":s.validateOperation,j="cancel"===o?"Vous \xeates sur le point d'annuler la cr\xe9ation de votre compte":"Vous \xeates sur le point de valider la cr\xe9ation de votre compte",I=yield s.modalCtrl.create({component:_.t,initialBreakpoint:.9,breakpoints:[0,.9,1],cssClass:["modal","bottom-sheet-container"],mode:"ios",componentProps:{label:$localize`${P}`,description:$localize`${j}`,user:s.user,userType:s.userType}});I.present();const{data:x,role:E}=yield I.onWillDismiss();if("cancel"===E&&s.cancel(),"validate"===E){let J=yield s.authService.signup({...x.user});!(J instanceof Error)&&J.status>=200&&J.status<=300&&s.router.navigateByUrl("authentication/signin")}}s.isLoading=!1})()}changeInputPasswordType(o,s){"confirm"!==s?this.typeOfPassword=o.detail.checked?"text":"password":this.typeOfConfirmPassword=o.detail.checked?"text":"password"}static{this.\u0275fac=function(s){return new(s||r)(n.rXU(b.Ix),n.rXU(h.aZ),n.rXU(b.nX),n.rXU(F.D),n.rXU(m.W3),n.rXU(S.h),n.rXU(T.n),n.rXU(l.k),n.rXU(f.E))}}static{this.\u0275cmp=n.VBU({type:r,selectors:[["app-second-step"]],decls:68,vars:47,consts:[[3,"viewLogo","buttonText"],[1,"scroll",3,"scrollEvents","fullscreen","scrollY"],["id","container"],[3,"formGroup","ngSubmit"],[1,"input-group"],[4,"ngIf"],["formGroupName","address"],["position","floating"],["formControlName","commercialRegion","clearInput","",3,"value"],["mode","ios","formControlName","region","interface","action-sheet","cancelText","Annuler",3,"ionChange"],["region",""],[3,"value",4,"ngFor","ngForOf"],["mode","ios","formControlName","city","cancelText","Annuler","interface","action-sheet"],["formControlName","district","clearInput","","placeholder","Entrer votre quartier"],[1,"password-input"],["formControlName","password","clearInput","","id","newPassword","placeholder","Nouveau mot de passe",3,"type"],["newPassword",""],["mode","md",3,"ionChange"],["formControlName","confirmPassword","clearInput","","id","confirmPassword",3,"type"],["confirmPassword",""],[1,"ion-justify-content-start"],["size-md","6","size-sm","12"],["expand","block",1,"cancel",3,"click"],["type","submit","color","primary","expand","block",1,"validate","ion-margin-start",3,"disabled"],[1,"ion-text-start"],[3,"click"],["src","assets/icons/previus.svg"],[1,"ion-margin-start"],["name","bubbles",4,"ngIf"],["formControlName","profession","clearInput","",3,"placeholder"],["formControlName","socialReason","clearInput","",3,"placeholder"],["mode","ios","formControlName","isRetired","interface","action-sheet","cancelText","Annuler"],[3,"value"],["mode","ios","formControlName","direction","interface","action-sheet","cancelText","Annuler",3,"ionChange"],["mode","ios","formControlName","service","interface","action-sheet","cancelText","Annuler",3,"ionChange"],["mode","ios","formControlName","position","interface","action-sheet","cancelText","Annuler"],["formControlName","matricule","clearInput","",3,"placeholder"],["name","bubbles"]],template:function(s,a){if(1&s&&(n.nrm(0,"app-header-connect",0),n.j41(1,"ion-content",1)(2,"div",2)(3,"form",3),n.bIt("ngSubmit",function(){return a.showBottomSheet("validate")}),n.j41(4,"div",4),n.DNE(5,C,6,6,"ion-item",5),n.DNE(6,M,6,6,"ion-item",5),n.DNE(7,R,11,11,"ion-item",5),n.DNE(8,$,6,4,"ion-item",5),n.DNE(9,Y,6,4,"ion-item",5),n.DNE(10,z,6,4,"ion-item",5),n.DNE(11,D,6,6,"ion-item",5),n.j41(12,"ion-item",6)(13,"ion-label",7),n.EFF(14),n.nI1(15,"translate"),n.k0s(),n.nrm(16,"ion-input",8),n.k0s(),n.j41(17,"ion-item",6)(18,"ion-label",7),n.EFF(19),n.nI1(20,"translate"),n.k0s(),n.j41(21,"ion-select",9,10),n.bIt("ionChange",function(P){return a.regionChange(P)}),n.DNE(23,B,2,2,"ion-select-option",11),n.k0s()(),n.j41(24,"ion-item",6)(25,"ion-label",7),n.EFF(26),n.nI1(27,"translate"),n.k0s(),n.j41(28,"ion-select",12),n.DNE(29,X,2,2,"ion-select-option",11),n.k0s()(),n.j41(30,"ion-item",6)(31,"ion-label",7),n.EFF(32),n.nI1(33,"translate"),n.k0s(),n.nrm(34,"ion-input",13),n.k0s(),n.j41(35,"div",14)(36,"ion-item")(37,"ion-label",7),n.EFF(38),n.nI1(39,"translate"),n.k0s(),n.nrm(40,"ion-input",15,16),n.k0s(),n.j41(42,"ion-checkbox",17),n.bIt("ionChange",function(P){return a.changeInputPasswordType(P,"password")}),n.k0s()(),n.j41(43,"div",14)(44,"ion-item")(45,"ion-label",7),n.EFF(46),n.nI1(47,"translate"),n.k0s(),n.nrm(48,"ion-input",18,19),n.k0s(),n.j41(50,"ion-checkbox",17),n.bIt("ionChange",function(P){return a.changeInputPasswordType(P,"confirm")}),n.k0s()()(),n.j41(51,"ion-grid")(52,"ion-row",20)(53,"ion-col",21)(54,"ion-button",22),n.bIt("click",function(){return a.showBottomSheet("cancel")}),n.EFF(55),n.nI1(56,"translate"),n.k0s()(),n.j41(57,"ion-col",21)(58,"ion-button",23),n.EFF(59),n.nI1(60,"translate"),n.k0s()()()(),n.j41(61,"div",24)(62,"ion-chip",25),n.bIt("click",function(){return a.back()}),n.nrm(63,"ion-img",26),n.j41(64,"ion-label",27),n.EFF(65),n.nI1(66,"translate"),n.k0s(),n.DNE(67,V,1,0,"ion-spinner",28),n.k0s()()()()()),2&s){const p=n.sdS(22);n.Y8G("viewLogo",!1)("buttonText",a.buttonText),n.R7$(1),n.Y8G("scrollEvents",!0)("fullscreen",!0)("scrollY",!0),n.R7$(2),n.Y8G("formGroup",a.signUpForm),n.R7$(2),n.Y8G("ngIf","particular"===a.commonService.getUserType(a.userType)),n.R7$(1),n.Y8G("ngIf","retailer"===a.commonService.getUserType(a.userType)),n.R7$(1),n.Y8G("ngIf",a.userType===a.userCategory.EmployeeLapasta),n.R7$(1),n.Y8G("ngIf",a.userType===a.userCategory.EmployeeLapasta),n.R7$(1),n.Y8G("ngIf",a.userType===a.userCategory.EmployeeLapasta),n.R7$(1),n.Y8G("ngIf",a.userType===a.userCategory.EmployeeLapasta),n.R7$(1),n.Y8G("ngIf",a.userType===a.userCategory.EmployeeLapasta),n.R7$(3),n.JRh(n.bMT(15,29,"signup-page.second-step.select-commercialRegion-label")),n.R7$(2),n.FS9("value",a.commercialRegion),n.R7$(3),n.JRh(n.bMT(20,31,"signup-page.second-step.select-region-label")),n.R7$(4),n.Y8G("ngForOf",a.commonService.getRegions()),n.R7$(3),n.JRh(n.bMT(27,33,"signup-page.second-step.select-city-label")),n.R7$(3),n.Y8G("ngForOf",a.commonService.getCities(p.value)),n.R7$(3),n.JRh(n.bMT(33,35,"signup-page.second-step.input-district-label")),n.R7$(6),n.JRh(n.bMT(39,37,"signup-page.second-step.input-password-label")),n.R7$(2),n.FS9("type",a.typeOfPassword),n.R7$(6),n.SpI("",n.bMT(47,39,"signup-page.second-step.input-confirm-password-label")," "),n.R7$(2),n.FS9("type",a.typeOfConfirmPassword),n.R7$(7),n.SpI(" ",n.bMT(56,41,"signup-page.second-step.cancel-button-label"),""),n.R7$(3),n.Y8G("disabled",a.signUpForm.invalid),n.R7$(1),n.SpI("",n.bMT(60,43,"signup-page.second-step.validate-button-label")," "),n.R7$(6),n.JRh(n.bMT(66,45,"signup-page.second-step.back-link-label")),n.R7$(2),n.Y8G("ngIf",a.isLoading)}},dependencies:[h.Sq,h.bT,t.qT,t.BC,t.cb,m.Jm,m.eY,m.ZB,m.hU,m.W9,m.lO,m.KW,m.$w,m.uz,m.he,m.ln,m.Nm,m.Ip,m.w2,m.hB,m.Je,m.Gw,t.j4,t.JD,t.$R,k.f,g.D9],styles:["*[_ngcontent-%COMP%]{margin:0;padding:0}.header-md[_ngcontent-%COMP%]:after{background-image:none!important}.modal[_ngcontent-%COMP%]{--border-radius: calc(60 * var(--res))}.input-group[_ngcontent-%COMP%]{margin-top:calc(75 * var(--res))}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{--padding-start: 0;margin-bottom:calc(50 * var(--res));--placeholder-color: $color-six}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-input[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{font-family:Mont Regular;font-weight:400;font-size:calc(42 * var(--res));color:#1e1e1e}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{padding-top:calc(25 * var(--res));padding-bottom:calc(25 * var(--res))}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]{margin-bottom:calc(50 * var(--res));display:flex;align-items:center;justify-content:space-between;border-bottom-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-250, #c8c7cc)));border-bottom-width:.55px;border-bottom-style:solid}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{width:90%;margin-bottom:0;--border-color: transparent;--inner-border-width: 0px 0px 0px 0px;--border-style: none}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-checkbox[_ngcontent-%COMP%]{margin-top:5%}.bg-primary[_ngcontent-%COMP%]{color:#143c5d}.message[_ngcontent-%COMP%]{margin-bottom:calc(75 * var(--res))}.message[_ngcontent-%COMP%]   ion-title[_ngcontent-%COMP%]{font-weight:800;font-size:calc(70 * var(--res));padding-bottom:calc(25 * var(--res))}.message[_ngcontent-%COMP%]   ion-text[_ngcontent-%COMP%]{font-weight:400;color:#757474;display:block;line-height:1.5em!important}.btn--meduim[_ngcontent-%COMP%]{height:calc(130 * var(--res));font-size:calc(50 * var(--res));font-family:var(--mont-bold)}.btn--meduim[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:var(--mont-bold)}.btn--small[_ngcontent-%COMP%]{height:calc(100 * var(--res))}.btn--round[_ngcontent-%COMP%]{--border-radius: 50%;padding-inline-start:0;padding-inline-end:0;--padding-start: 0;--padding-end: 0}.btn--border-primary[_ngcontent-%COMP%]{--background: transparent;--background-activated: #c4e6d0;--border-color: var(--ion-color-primary);--border-style: solid;--border-width: 2px}.btn--border-secondary[_ngcontent-%COMP%]{--border-width: 2px;--background: transparent;--background-activated: #ddabb2;--border-color: var(--ion-color-secondary);--border-style: solid}.btn--upper[_ngcontent-%COMP%]{text-transform:uppercase}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]{overflow:hidden}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar{width:5px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-track{box-shadow:inset 0 0 5px #546e8d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background:#143c5d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover{background:#143c5d}.bottom-sheet-content[_ngcontent-%COMP%]{background:#fdfdfe;height:100%}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-thumbnail[_ngcontent-%COMP%]{margin-left:calc(31.25 * var(--res));background-color:#1e1e1e;--border-radius: 50%;--size: 23}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:Mont Bold;font-size:calc(45 * var(--res));text-align:center;color:#757474}.bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%], .bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]{padding:calc(75 * var(--res))}.font-size-5[_ngcontent-%COMP%]{font-size:calc(42 * var(--res))}ion-content[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%]{margin-top:calc(112.5 * var(--res));--border-radius: 6px}ion-content[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:Mont Bold;font-weight:700;font-size:calc(42 * var(--res));text-align:center}ion-content[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]{margin-top:0}ion-content[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .cancel[_ngcontent-%COMP%]{--background: var(--ion-color-grey);margin-right:calc(50 * var(--res))}ion-content[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .cancel[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{color:#1e1e1e}ion-content[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   ion-chip[_ngcontent-%COMP%]{margin-top:calc(112.5 * var(--res))}ion-content[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   ion-chip[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{padding-left:calc(25 * var(--res));color:#1e1e1e}"]})}}return r})();const A=[{path:"",component:U},{path:":userType",component:U}];let W=(()=>{class r{static{this.\u0275fac=function(s){return new(s||r)}}static{this.\u0275mod=n.$C({type:r})}static{this.\u0275inj=n.G2t({imports:[b.iI.forChild(A),b.iI]})}}return r})()}}]);