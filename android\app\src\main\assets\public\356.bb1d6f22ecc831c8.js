"use strict";(self.webpackChunkapp=self.webpackChunkapp||[]).push([[356],{90356:(D,M,a)=>{a.r(M),a.d(M,{CreateIndirectAccountPageModule:()=>A});var f=a(56610),g=a(37222),F=a(24608),c=a(77897),P=a(77575),p=a(73308),e=a(2978),E=a(23985),k=a(44444),S=a(43556),U=a(82571),C=a(63037),x=a(93860),m=a(58133),i=a(5141),t=a(74657);function d(s,h){1&s&&e.nrm(0,"app-progress-spinner")}function u(s,h){if(1&s&&(e.j41(0,"ion-select-option",28),e.<PERSON>FF(1),e.k0s()),2&s){const n=h.$implicit;e.Y8G("value",n),e.R7$(1),e.SpI(" ",n,"")}}const _=function(s,h){return{id:s,name:h}};function v(s,h){if(1&s&&(e.j41(0,"ion-select-option",28),e.EFF(1),e.k0s()),2&s){const n=h.$implicit;e.Y8G("value",e.l_i(2,_,null==n?null:n._id,null==n?null:n.name)),e.R7$(1),e.SpI(" ",null==n?null:n.name," ")}}function O(s,h){1&s&&e.nrm(0,"ion-spinner",29)}const y=function(){return["/navigation/user-list"]},T=[{path:"",component:(()=>{class s{constructor(n,o){this.fb=n,this.imageCompress=o,this.companySrv=(0,e.WQX)(S.B),this.commonService=(0,e.WQX)(U.h),this.location=(0,e.WQX)(f.aZ),this.userServ=(0,e.WQX)(E.D),this.authenticationService=(0,e.WQX)(x.k),this.LIMIT_SIZE=15e5,this.hasDescription=!1,this.companies=[],this.userToUpdate=null,this.attachment={file:"",name:"",contentType:""}}ngOnInit(){var n=this;return(0,p.A)(function*(){n.userToUpdate=n.userServ.currentUserParticular,n.initForm(),n.regions=n.commonService.getRegions(),n.companies=(yield n.companySrv.getCompanies({projection:"name,erpSoldToId"}))?.data,n.userToUpdate&&n.patchForm(n.userToUpdate)})()}getRegion(n){this.cities=this.commonService.getCities(n.detail.value)}initForm(){this.form=this.fb.group({firstName:["",g.k0.required],social:[""],tel:["",[g.k0.required]],region:["",g.k0.required],district:[""],associatedCompanies:[[]]})}patchForm(n){this.form.patchValue({firstName:n.firstName,tel:n.tel,social:n.socialReason,region:n.address?.region||"",district:n.address?.district||"",associatedCompanies:n.associatedCompanies?.map(o=>o._id)||[]})}onSubmit(){var n=this;return(0,p.A)(function*(){if(n.isLoading=!0,n.form.invalid)return n.markFormGroupTouched(n.form),n.commonService.showToast({color:"warning",message:"An error occurred"}),void(n.isLoading=!1);const o=n.form.value,r=new k.cs(0);r.firstName=o.firstName,r.socialReason=o.social,r.category=m.s.Particular,r.tel=o.tel,r.address={region:n.getCommercialRegion(o.region),district:o.district},r.associatedCompanies=o.associatedCompanies,r.profilePicture=n.attachment.file;try{if(n.userToUpdate){const l=n.form.value;n.userToUpdate.firstName=l.firstName||n.userToUpdate.firstName,n.userToUpdate.socialReason=l.social||n.userToUpdate.socialReason,n.userToUpdate.category=m.s.Particular,n.userToUpdate.tel=l.tel||n.userToUpdate.tel,n.userToUpdate.address={region:l.region?n.getCommercialRegion(l.region):n.userToUpdate.address?.region,district:l.district||n.userToUpdate.address?.district},yield n.userServ.updateUserParticular(n.userToUpdate),n.location.back(),n.isLoading=!1,n.userServ.currentUserParticular=null,n.form.reset()}else 201==(yield n.authenticationService.signupParticular(r))?.status&&(n.location.back(),n.isLoading=!1,n.form.reset())}catch{}finally{n.isLoading=!1}})()}getCommercialRegion(n){const o=i.MJ,r=n.trim().toLowerCase();console.log("Normalized region:",r);for(const[l,b]of Object.entries(o))if(console.log("Checking key:",l,"with regions:",b),b.some(w=>w.trim().toLowerCase()===r))return console.log("Match found:",l),l;return console.log("No match found"),null}markFormGroupTouched(n){Object.values(n.controls).forEach(o=>{o.markAsTouched(),o instanceof g.gE&&this.markFormGroupTouched(o)})}setAttachment(n){var o=this;return(0,p.A)(function*(){try{o.file=o.getFileFromDataSet(n),o.attachment.file=yield o.getFileDataUrl(o.file);let r=yield o.getFileSize(o.file,o.attachment.file);o.validateFileSize(r,o.LIMIT_SIZE),[o.attachment.name,o.attachment.contentType]=[o.file.name,o.file.type]}catch(r){o.handleError(r)}})()}getFileFromDataSet(n){const o=n.target.files[0];return this.validate(o),o}getFileDataUrl(n){var o=this;return(0,p.A)(function*(){const r=yield o.convertFileToDataUrl(n);return o.validate(r),r})()}convertFileToDataUrl(n){return new Promise((o,r)=>{const l=new FileReader;l.readAsDataURL(n),l.onload=()=>o(l.result),l.onerror=b=>r(b)})}validate(n){if(!n)throw new Error("Une erreur est survenue, veuillez ressayer SVP !")}getFileSize(n,o){var r=this;return(0,p.A)(function*(){let l=n.size;return"application/pdf"!=n.type&&(o=yield r.imageCompress.compressFile(o,C._k.Up),l=r.imageCompress.byteCount(o)),l})()}validateFileSize(n,o){if(n>o)throw new Error("Veuillez choisir un fichier de moins de 1,5 MB SVP !")}handleError(n){this.file=null,this.commonService.showToast({color:"danger",message:`${n.message}`})}static{this.\u0275fac=function(o){return new(o||s)(e.rXU(g.ok),e.rXU(C.ep))}}static{this.\u0275cmp=e.VBU({type:s,selectors:[["app-create-indirect-account"]],decls:57,vars:23,consts:[[4,"ngIf"],[3,"translucent"],[1,"header"],["slot","start","src","/assets/icons/arrow-back.svg",1,"img",3,"routerLink"],[1,"buttons"],[1,"transparent"],["name","search"],["name","funnel-outline"],[3,"fullscreen"],[3,"formGroup","ngSubmit"],["position","floating"],["formControlName","firstName","clearInput",""],["formControlName","social","clearInput",""],["type","number","clearInput","","formControlName","tel"],["type","text","clearInput","","formControlName","district"],["mode","ios","formControlName","region","interface","action-sheet",3,"cancelText","ionChange"],["stores",""],[3,"value",4,"ngFor","ngForOf"],["formControlName","associatedCompanies","multiple","true","mode","ios","interface","action-sheet"],[1,"iconImage"],[1,"image"],[1,"attachment-btn"],["type","file","id","file","accept","image/*,.pdf","multiple","",2,"display","none",3,"change"],["src","/assets/icons/imageIcon.svg","alt","Upload","slot","end","onclick","document.getElementById('file').click()",2,"cursor","pointer","width","40px","height","40px"],[1,"image","localisation"],[1,"btn-validate"],["type","submit","color","primary","expand","block",1,"btn--meduim","btn--upper",3,"disabled"],["name","bubbles",4,"ngIf"],[3,"value"],["name","bubbles"]],template:function(o,r){1&o&&(e.DNE(0,d,1,0,"app-progress-spinner",0),e.j41(1,"ion-header",1)(2,"div",2),e.nrm(3,"ion-img",3),e.j41(4,"ion-title"),e.EFF(5," Clients indirects "),e.k0s()(),e.j41(6,"div",4)(7,"ion-button",5),e.nrm(8,"ion-icon",6),e.k0s(),e.j41(9,"ion-button",5),e.nrm(10,"ion-icon",7),e.k0s()()(),e.j41(11,"ion-content",8)(12,"form",9),e.bIt("ngSubmit",function(){return r.onSubmit()}),e.j41(13,"ion-item")(14,"ion-label",10),e.EFF(15," Nom + Pr\xe9nom "),e.k0s(),e.nrm(16,"ion-input",11),e.k0s(),e.j41(17,"ion-item")(18,"ion-label",10),e.EFF(19," Appellation commerciale"),e.k0s(),e.nrm(20,"ion-input",12),e.k0s(),e.j41(21,"ion-item")(22,"ion-label",10),e.EFF(23," Num\xe9ro de T\xe9l\xe9phone"),e.k0s(),e.nrm(24,"ion-input",13),e.k0s(),e.j41(25,"ion-item")(26,"ion-label",10),e.EFF(27,"Localisation"),e.k0s(),e.nrm(28,"ion-input",14),e.k0s(),e.j41(29,"ion-item")(30,"ion-label",10),e.EFF(31),e.nI1(32,"translate"),e.k0s(),e.j41(33,"ion-select",15,16),e.bIt("ionChange",function(b){return r.getRegion(b)}),e.nI1(35,"translate"),e.DNE(36,u,2,2,"ion-select-option",17),e.k0s()(),e.j41(37,"ion-item")(38,"ion-label",10),e.EFF(39),e.nI1(40,"translate"),e.k0s(),e.j41(41,"ion-select",18),e.DNE(42,v,2,5,"ion-select-option",17),e.k0s()(),e.j41(43,"div",19)(44,"div",20)(45,"div",21)(46,"input",22),e.bIt("change",function(b){return r.setAttachment(b)}),e.k0s(),e.nrm(47,"img",23),e.k0s()(),e.j41(48,"div",24)(49,"ion-label",10),e.EFF(50),e.k0s()()(),e.j41(51,"div",25)(52,"ion-button",26)(53,"ion-label"),e.EFF(54),e.nI1(55,"translate"),e.k0s(),e.DNE(56,O,1,0,"ion-spinner",27),e.k0s()()()()),2&o&&(e.Y8G("ngIf",r.isLoading),e.R7$(1),e.Y8G("translucent",!0),e.R7$(2),e.Y8G("routerLink",e.lJ4(22,y)),e.R7$(8),e.Y8G("fullscreen",!0),e.R7$(1),e.Y8G("formGroup",r.form),e.R7$(19),e.SpI(" ",e.bMT(32,14,"reseller-new-page.first-step.select-region-label")," "),e.R7$(2),e.FS9("cancelText",e.bMT(35,16,"button.cancel")),e.R7$(3),e.Y8G("ngForOf",r.regions),e.R7$(3),e.SpI(" ",e.bMT(40,18,"indirect-clients.vendors")," "),e.R7$(3),e.Y8G("ngForOf",r.companies),e.R7$(8),e.SpI("Images : ",null==r.file?null:r.file.name," "),e.R7$(2),e.Y8G("disabled",!r.form.valid),e.R7$(2),e.SpI(" ",e.bMT(55,20,"order-new-page.planning-modal.save-button-label")," "),e.R7$(2),e.Y8G("ngIf",r.isLoading))},dependencies:[f.Sq,f.bT,g.qT,g.BC,g.cb,g.j4,g.JD,c.Jm,c.W9,c.eU,c.iq,c.KW,c.$w,c.uz,c.he,c.Nm,c.Ip,c.w2,c.BC,c.su,c.Je,c.Gw,c.N7,P.Wk,t.D9],styles:["*[_ngcontent-%COMP%]{margin:0;padding:0}.header-md[_ngcontent-%COMP%]:after{background-image:none!important}.modal[_ngcontent-%COMP%]{--border-radius: calc(60 * var(--res))}.input-group[_ngcontent-%COMP%]{margin-top:calc(75 * var(--res))}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{--padding-start: 0;margin-bottom:calc(50 * var(--res));--placeholder-color: $color-six}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-input[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{font-family:Mont Regular;font-weight:400;font-size:calc(42 * var(--res));color:#1e1e1e}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{padding-top:calc(25 * var(--res));padding-bottom:calc(25 * var(--res))}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]{margin-bottom:calc(50 * var(--res));display:flex;align-items:center;justify-content:space-between;border-bottom-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-250, #c8c7cc)));border-bottom-width:.55px;border-bottom-style:solid}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{width:90%;margin-bottom:0;--border-color: transparent;--inner-border-width: 0px 0px 0px 0px;--border-style: none}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-checkbox[_ngcontent-%COMP%]{margin-top:5%}.bg-primary[_ngcontent-%COMP%]{color:#143c5d}.message[_ngcontent-%COMP%]{margin-bottom:calc(75 * var(--res))}.message[_ngcontent-%COMP%]   ion-title[_ngcontent-%COMP%]{font-weight:800;font-size:calc(70 * var(--res));padding-bottom:calc(25 * var(--res))}.message[_ngcontent-%COMP%]   ion-text[_ngcontent-%COMP%]{font-weight:400;color:#757474;display:block;line-height:1.5em!important}.btn--meduim[_ngcontent-%COMP%]{height:calc(130 * var(--res));font-size:calc(50 * var(--res));font-family:var(--mont-bold)}.btn--meduim[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:var(--mont-bold)}.btn--small[_ngcontent-%COMP%]{height:calc(100 * var(--res))}.btn--round[_ngcontent-%COMP%]{--border-radius: 50%;padding-inline-start:0;padding-inline-end:0;--padding-start: 0;--padding-end: 0}.btn--border-primary[_ngcontent-%COMP%]{--background: transparent;--background-activated: #c4e6d0;--border-color: var(--ion-color-primary);--border-style: solid;--border-width: 2px}.btn--border-secondary[_ngcontent-%COMP%]{--border-width: 2px;--background: transparent;--background-activated: #ddabb2;--border-color: var(--ion-color-secondary);--border-style: solid}.btn--upper[_ngcontent-%COMP%]{text-transform:uppercase}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]{overflow:hidden}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar{width:5px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-track{box-shadow:inset 0 0 5px #546e8d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background:#143c5d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover{background:#143c5d}.bottom-sheet-content[_ngcontent-%COMP%]{background:#fdfdfe;height:100%}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-thumbnail[_ngcontent-%COMP%]{margin-left:calc(31.25 * var(--res));background-color:#1e1e1e;--border-radius: 50%;--size: 23}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:Mont Bold;font-size:calc(45 * var(--res));text-align:center;color:#757474}.bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%], .bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]{padding:calc(75 * var(--res))}.font-size-5[_ngcontent-%COMP%]{font-size:calc(42 * var(--res))}ion-header[_ngcontent-%COMP%]{display:flex;padding-top:13px;align-items:center}ion-title[_ngcontent-%COMP%]{color:#0b305c;font-size:calc(55 * var(--res));text-align:start;font-family:Mont Regular;font-weight:700!important;margin:auto}ion-img[_ngcontent-%COMP%]{width:calc(50 * var(--res));margin-right:calc(25 * var(--res));color:#0b305c}.header[_ngcontent-%COMP%]{--background: #F1F2F4;width:100%;margin-left:13px;margin-top:auto;margin-bottom:auto;padding:auto 0px;display:flex}.header[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%]{--background: transparent}.buttons[_ngcontent-%COMP%]{display:flex;justify-content:space-between}.buttons[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%]{--background: transparent !important;--box-shadow: none;color:#0b305c;border:none;--padding: auto}.buttons[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{color:#419cfb}ion-content[_ngcontent-%COMP%]{--background: #F1F2F4}ion-content[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]{width:calc(100% - 48px);margin:2em auto auto;padding:auto;--background: #F1F2F4}ion-content[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-size:.75rem;color:#0b305c}ion-content[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{--padding-start: 0px;margin-bottom:calc(41 * var(--res));--background: $color-nineteen;--ripple-color: transparent;--background-activated: transparent;--background-activated-opacity: transparent;--background-focused: transparent;--background-focused-opacity: transparent;--background-hover: transparent;--background-hover-opacity: transparent}ion-content[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .btn-validate[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{color:#fff}ion-content[_ngcontent-%COMP%]   ion-text[_ngcontent-%COMP%]{font-size:.75rem}ion-content[_ngcontent-%COMP%]   .submit[_ngcontent-%COMP%]{width:calc(100% - 48px);margin-left:24px}ion-content[_ngcontent-%COMP%]   .add[_ngcontent-%COMP%]{font-size:.6rem;font-weight:700;--color: #0B305C;--background: #419CFB2B;--padding: 5px 10px;--border-radius: 30px;margin:1rem auto}ion-content[_ngcontent-%COMP%]   .iconImage[_ngcontent-%COMP%]{display:flex;width:calc(100% - 48px);margin-bottom:1rem}ion-content[_ngcontent-%COMP%]   .iconImage[_ngcontent-%COMP%]   .image[_ngcontent-%COMP%]{border:1.12px solid #D5DFEB;border-radius:6px;padding:.5rem}ion-content[_ngcontent-%COMP%]   .iconImage[_ngcontent-%COMP%]   .localisation[_ngcontent-%COMP%]{margin-left:.2rem}"]})}}return s})()}];let I=(()=>{class s{static{this.\u0275fac=function(o){return new(o||s)}}static{this.\u0275mod=e.$C({type:s})}static{this.\u0275inj=e.G2t({imports:[P.iI.forChild(T),P.iI]})}}return s})(),A=(()=>{class s{static{this.\u0275fac=function(o){return new(o||s)}}static{this.\u0275mod=e.$C({type:s})}static{this.\u0275inj=e.G2t({imports:[f.MD,g.YN,g.X1,c.bv,F.vj,t.h,I]})}}return s})()},43556:(D,M,a)=>{a.d(M,{B:()=>U});var f=a(73308),g=a(94934),F=a(45312),c=a(26409),p=(a(99987),a(2978)),e=a(33607),E=a(82571),k=a(14599),S=a(74657);let U=(()=>{class C{constructor(m,i,t,d,u){this.baseUrl=m,this.http=i,this.commonSrv=t,this.storageSrv=d,this.translateService=u,this.base_url=`${this.baseUrl.getOrigin()}${F.c.basePath}`,this.base_url+="companies"}create(m){var i=this;return(0,f.A)(function*(){try{return delete m._id,yield(0,g.s)(i.http.post(i.base_url,m))}catch(t){return i.commonSrv.getError("Echec de cr\xe9ation de la compagnie",t)}})()}getCompanies(m){var i=this;return(0,f.A)(function*(){try{let t=new c.Nl;const{category:d,city:u,limit:_,name:v,regionCom:O,solToId:y,tel:R,users:T,offset:I,enable:A=!0,projection:s,isLoyaltyProgDistributor:h}=m;return void 0!==d&&(t=t.append("category",d)),u&&(t=t.append("address.city",u)),v&&(t=t.append("name",v)),y&&(t=t.append("erpSoldToId",y)),R&&(t=t.append("tel",`${R}`)),s&&(t=t.append("projection",`${s}`)),T&&(t=t.append("users",`${T}`)),O&&(t=t.append("address.commercialRegion",O)),h&&(t=t.append("isLoyaltyProgDistributor",h)),void 0!==_&&(t=t.append("limit",_)),void 0!==I&&(t=t.append("offset",I)),t=t.set("enable",A),yield(0,g.s)(i.http.get(i.base_url,{params:t}))}catch(t){const u={message:i.commonSrv.getError("",t).message,color:"danger"};return yield i.commonSrv.showToast(u),t}})()}getParticularCompanies(m){var i=this;return(0,f.A)(function*(){let t=new c.Nl;const{limit:d,offset:u,enable:_=!0,commercialRegion:v}=m;return void 0!==d&&(t=t.append("limit",d)),void 0!==u&&(t=t.append("offset",u)),v&&(t=t.append("address.commercialRegion",v)),t=t.set("enable",_),yield(0,g.s)(i.http.get(i.base_url+"/particular-suppliers",{params:t}))})()}find(m){var i=this;return(0,f.A)(function*(){try{return yield(0,g.s)(i.http.get(i.base_url+"/"+m))}catch{return i.commonSrv.initCompany()}})()}getBalance(m){var i=this;return(0,f.A)(function*(){try{let t=new c.Nl;const{company:d}=i.storageSrv.getUserConnected();return t=t.set("_id",d?d?._id:m?.companyId),yield(0,g.s)(i.http.get(`${i.base_url}/balance`,{params:t}))}catch(t){return yield i.commonSrv.showToast({message:"Une erreur est survenue lors de la r\xe9cup\xe9ration de votre solde",color:"danger"}),t}})()}getUsersCompany(m,i){var t=this;return(0,f.A)(function*(){try{let d=new c.Nl;const{email:u,enable:_=!0}=i;return u&&(d=d.append("email",u)),d=d.append("enable",_),yield(0,g.s)(t.http.get(`${t.base_url}/${m}/users`,{params:d}))}catch(d){return yield t.commonSrv.showToast({message:"Une erreur est survenue lors de la r\xe9cup\xe9ration de vos informations",color:"danger"}),d}})()}static{this.\u0275fac=function(i){return new(i||C)(p.KVO(e.K),p.KVO(c.Qq),p.KVO(E.h),p.KVO(k.n),p.KVO(S.c$))}}static{this.\u0275prov=p.jDH({token:C,factory:C.\u0275fac,providedIn:"root"})}}return C})()}}]);