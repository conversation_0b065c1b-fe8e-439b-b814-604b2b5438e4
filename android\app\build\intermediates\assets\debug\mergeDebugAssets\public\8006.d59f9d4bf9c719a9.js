"use strict";(self.webpackChunkapp=self.webpackChunkapp||[]).push([[8006],{6239:(x,M,e)=>{e.r(M),e.d(M,{RecapScanPageModule:()=>w});var p=e(56610),C=e(37222),a=e(77897),O=e(77575),c=e(73308),u=e(39316),f=e(14599),h=e(81559),_=e(82571),m=e(99987),n=e(2978),d=e(26409),i=e(68896),s=e(62049),v=e(13217),P=e(44444),R=e(17709),b=e(58133),F=e(54648),T=e(71333),E=e(74657);function k(o,L){1&o&&n.nrm(0,"app-progress-spinner")}function I(o,L){if(1&o&&(n.j41(0,"div",14)(1,"p"),n.EFF(2),n.nI1(3,"translate"),n.k0s(),n.j41(4,"div",15)(5,"label",16),n.EFF(6),n.nI1(7,"translate"),n.k0s(),n.j41(8,"div",17),n.EFF(9),n.k0s()(),n.j41(10,"div",15)(11,"label",16),n.EFF(12,"Type de client"),n.k0s(),n.j41(13,"div",17),n.EFF(14),n.k0s()(),n.j41(15,"div",15)(16,"label",16),n.EFF(17),n.nI1(18,"translate"),n.k0s(),n.j41(19,"div",17),n.EFF(20),n.k0s()(),n.j41(21,"div",15)(22,"label",16),n.EFF(23),n.nI1(24,"translate"),n.k0s(),n.j41(25,"div",17),n.EFF(26),n.k0s()(),n.j41(27,"div",15)(28,"label",16),n.EFF(29),n.nI1(30,"translate"),n.k0s(),n.j41(31,"div",17),n.EFF(32),n.k0s()(),n.j41(33,"div",15)(34,"label",16),n.EFF(35,"Quartier"),n.k0s(),n.j41(36,"div",17),n.EFF(37),n.k0s()()()),2&o){const t=n.XpG();n.R7$(2),n.SpI(" ",n.bMT(3,11,"qr-orders.user-info")," "),n.R7$(4),n.JRh(n.bMT(7,13,"user-info.full-name")),n.R7$(3),n.SpI(" ",null==t.user?null:t.user.firstName,""),n.R7$(5),n.JRh(t.getCategory(null==t.user?null:t.user.categoryType)),n.R7$(3),n.JRh(n.bMT(18,15,"user-info.phone")),n.R7$(3),n.JRh(null==t.user?null:t.user.tel),n.R7$(3),n.JRh(n.bMT(24,17,"user-info.region")),n.R7$(3),n.JRh(null==t.user?null:t.user.address.region),n.R7$(3),n.JRh(n.bMT(30,19,"indirect-clients.ville")),n.R7$(3),n.JRh(null==t.user?null:t.user.address.city),n.R7$(5),n.JRh(null==t.user||null==t.user.address?null:t.user.address.neighborhood)}}const j=[{path:"",component:(()=>{class o{constructor(){this.orderSrv=(0,n.WQX)(h.Q),this.scannerSrv=(0,n.WQX)(i.I),this.qrcodeSrv=(0,n.WQX)(R.Q),this.storageService=(0,n.WQX)(f.n),this.productSrv=(0,n.WQX)(u.b),this.commonSrv=(0,n.WQX)(_.h),this.translateService=(0,n.WQX)(s.E),this.fidelitySrv=(0,n.WQX)(v._),this.location=(0,n.WQX)(p.aZ),this.route=(0,n.WQX)(O.Ix),this.isFrench=this.translateService.currentLang===m.T.French,this.storageService.getUserConnected(),this.user=this.qrcodeSrv.currenUser}ngOnInit(){var t=this;return(0,c.A)(function*(){t.isLoading=!0,t.cart={...JSON.parse(t.storageService.load("cart")),items:JSON.parse(t.storageService.load("items"))},console.log("[RECAP-SCAN] Cart items:",t.cart?.items);const r=yield t.fidelitySrv.getPoints({});console.log("[RECAP-SCAN] User entity:",r),t.totalPoints=t.fidelitySrv.calculateTotalPointsOrder(t.cart?.items,r),console.log("[RECAP-SCAN] Total points calculated:",t.totalPoints),t.supplier=JSON.parse(t.storageService.load("supplier")),t.isLoading=!1})()}ionViewWillEnter(){return(0,c.A)(function*(){})()}validatePoint(){var t=this;return(0,c.A)(function*(){try{t.isLoading=!0;let r=null;const l=t.storageService.load("USER_INFO");if(l)try{r=JSON.parse(l)}catch(g){console.error("Error parsing USER_INFO:",g)}if(!r)return t.isLoading=!1,void t.commonSrv?.showToast?.({color:"danger",message:"User information not found"});const y=r?.category===b.s.Commercial?t.user:r;if(!t.supplier)return t.isLoading=!1,void t.commonSrv?.showToast?.({color:"danger",message:"Supplier information not found"});if(!t.cart)return t.isLoading=!1,void t.commonSrv?.showToast?.({color:"danger",message:"Cart information not found"});const A={user:y,supplier:t.supplier,cart:{...t.cart},qrCodeData:JSON.parse(t.storageService.load("qrCodeData"))};let S;try{S=yield t.scannerSrv.validateScanData(A)}catch(g){return console.error("Error validating scan data:",g),t.isLoading=!1,void t.commonSrv?.showToast?.({color:"danger",message:"Error validating scan data"})}if(t.isLoading=!1,S instanceof d.yz){const g=S?.error?.message||"Operation failed";t.commonSrv?.showToast?.({color:"danger",message:g})}else{try{t.storageService.remove("items"),t.storageService.remove("cart"),t.storageService.remove("suppliers"),t.storageService.remove("qrCodeData"),t.productSrv.dataQrCode=[],t.productSrv.currentDataProductScan=[]}catch(g){console.error("Error removing storage items:",g)}[b.s.Commercial,b.s.DonutAnimator].includes(r?.category)?(t.qrcodeSrv.currenUser=null,t.user=null,t.route.navigate(["order/history/list-order"]).catch(g=>console.error("Navigation error:",g))):t.route.navigate(["navigation/fidelity-program"]).catch(g=>console.error("Navigation error:",g))}}catch(r){console.error("Unexpected error in validatePoint:",r),t.isLoading=!1,t.commonSrv?.showToast?.({color:"danger",message:"An unexpected error occurred"})}finally{t.isLoading=!1}})()}back(){this.location.back()}getCategory(t){switch(t){case P.iL.BHB:return"BHB";case P.iL.BS:return"BS";case P.iL.BPI:return"BPI";default:return"Unknown Category"}}static{this.\u0275fac=function(r){return new(r||o)}}static{this.\u0275cmp=n.VBU({type:o,selectors:[["app-recap-scan"]],decls:24,vars:15,consts:[[1,"header"],["slot","start","src","/assets/icons/arrow-back.svg",3,"click"],[4,"ngIf"],[1,"scroll-container"],[3,"cart","isPoint","points"],[1,"profile-container"],[1,"avatar"],["name","person-outline"],[1,"info"],[1,"title"],[1,"name"],["class","user-info-container",4,"ngIf"],[1,"btn-validate"],["color","primary","expand","block",1,"btn--meduim","btn--upper",3,"click"],[1,"user-info-container"],[1,"user-info-item"],[1,"user-info-label"],[1,"user-info-value"]],template:function(r,l){1&r&&(n.j41(0,"ion-header")(1,"div",0)(2,"ion-img",1),n.bIt("click",function(){return l.back()}),n.k0s(),n.j41(3,"ion-title"),n.EFF(4),n.nI1(5,"translate"),n.k0s()()(),n.DNE(6,k,1,0,"app-progress-spinner",2),n.j41(7,"section",3),n.nrm(8,"app-purchase-summary",4),n.j41(9,"div",5)(10,"div",6),n.nrm(11,"ion-icon",7),n.k0s(),n.j41(12,"div",8)(13,"div",9),n.EFF(14),n.nI1(15,"translate"),n.k0s(),n.j41(16,"div",10),n.EFF(17),n.k0s()()(),n.DNE(18,I,38,21,"div",11),n.k0s(),n.j41(19,"div",12)(20,"ion-button",13),n.bIt("click",function(){return l.validatePoint()}),n.j41(21,"ion-label"),n.EFF(22),n.nI1(23,"translate"),n.k0s()()()),2&r&&(n.R7$(4),n.SpI(" ",n.bMT(5,9,"recap-scan.title")," "),n.R7$(2),n.Y8G("ngIf",l.isLoading),n.R7$(2),n.Y8G("cart",l.cart)("isPoint",!0)("points",l.totalPoints),n.R7$(6),n.SpI(" ",n.bMT(15,11,"recap-scan.supplier")," "),n.R7$(3),n.JRh(null==l.supplier?null:l.supplier.name),n.R7$(1),n.Y8G("ngIf",l.user),n.R7$(4),n.SpI(" ",n.bMT(23,13,"recap-scan.validate")," "))},dependencies:[p.bT,a.Jm,a.eU,a.iq,a.KW,a.he,a.BC,F.N,T._,E.D9],styles:["*[_ngcontent-%COMP%]{margin:0;padding:0}.header-md[_ngcontent-%COMP%]:after{background-image:none!important}.modal[_ngcontent-%COMP%]{--border-radius: calc(60 * var(--res))}.input-group[_ngcontent-%COMP%]{margin-top:calc(75 * var(--res))}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{--padding-start: 0;margin-bottom:calc(50 * var(--res));--placeholder-color: $color-six}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-input[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{font-family:Mont Regular;font-weight:400;font-size:calc(42 * var(--res));color:#1e1e1e}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{padding-top:calc(25 * var(--res));padding-bottom:calc(25 * var(--res))}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]{margin-bottom:calc(50 * var(--res));display:flex;align-items:center;justify-content:space-between;border-bottom-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-250, #c8c7cc)));border-bottom-width:.55px;border-bottom-style:solid}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{width:90%;margin-bottom:0;--border-color: transparent;--inner-border-width: 0px 0px 0px 0px;--border-style: none}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-checkbox[_ngcontent-%COMP%]{margin-top:5%}.bg-primary[_ngcontent-%COMP%]{color:#143c5d}.message[_ngcontent-%COMP%]{margin-bottom:calc(75 * var(--res))}.message[_ngcontent-%COMP%]   ion-title[_ngcontent-%COMP%]{font-weight:800;font-size:calc(70 * var(--res));padding-bottom:calc(25 * var(--res))}.message[_ngcontent-%COMP%]   ion-text[_ngcontent-%COMP%]{font-weight:400;color:#757474;display:block;line-height:1.5em!important}.btn--meduim[_ngcontent-%COMP%]{height:calc(130 * var(--res));font-size:calc(50 * var(--res));font-family:var(--mont-bold)}.btn--meduim[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:var(--mont-bold)}.btn--small[_ngcontent-%COMP%]{height:calc(100 * var(--res))}.btn--round[_ngcontent-%COMP%]{--border-radius: 50%;padding-inline-start:0;padding-inline-end:0;--padding-start: 0;--padding-end: 0}.btn--border-primary[_ngcontent-%COMP%]{--background: transparent;--background-activated: #c4e6d0;--border-color: var(--ion-color-primary);--border-style: solid;--border-width: 2px}.btn--border-secondary[_ngcontent-%COMP%]{--border-width: 2px;--background: transparent;--background-activated: #ddabb2;--border-color: var(--ion-color-secondary);--border-style: solid}.btn--upper[_ngcontent-%COMP%]{text-transform:uppercase}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]{overflow:hidden}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar{width:5px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-track{box-shadow:inset 0 0 5px #546e8d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background:#143c5d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover{background:#143c5d}.bottom-sheet-content[_ngcontent-%COMP%]{background:#fdfdfe;height:100%}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-thumbnail[_ngcontent-%COMP%]{margin-left:calc(31.25 * var(--res));background-color:#1e1e1e;--border-radius: 50%;--size: 23}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:Mont Bold;font-size:calc(45 * var(--res));text-align:center;color:#757474}.bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%], .bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]{padding:calc(75 * var(--res))}.font-size-5[_ngcontent-%COMP%]{font-size:calc(42 * var(--res))}.scroll-container[_ngcontent-%COMP%]{height:100%;overflow-y:auto;scroll-behavior:smooth}.scroll-container[_ngcontent-%COMP%]   .recap-container[_ngcontent-%COMP%]{padding:calc(41 * var(--res)) 0;height:80%;margin:0 calc(41 * var(--res));background-color:transparent;overflow-x:hidden;overflow-y:auto}.scroll-container[_ngcontent-%COMP%]   .recap-container[_ngcontent-%COMP%]   .fbold[_ngcontent-%COMP%]{font-family:Mont Bold}.scroll-container[_ngcontent-%COMP%]   .recap-container[_ngcontent-%COMP%]   .fextrabold[_ngcontent-%COMP%]{font-family:Mont Bold;font-weight:700;font-size:calc(40.7 * var(--res))}.scroll-container[_ngcontent-%COMP%]   .recap-container[_ngcontent-%COMP%]   .fMeduim[_ngcontent-%COMP%]{font-family:Mont Light}.scroll-container[_ngcontent-%COMP%]   .recap-container[_ngcontent-%COMP%]   .primary[_ngcontent-%COMP%]{color:#143c5d!important}.scroll-container[_ngcontent-%COMP%]   .recap-container[_ngcontent-%COMP%]   .recap-title[_ngcontent-%COMP%]{margin-bottom:calc(50 * var(--res))}.scroll-container[_ngcontent-%COMP%]   .recap-container[_ngcontent-%COMP%]   .totalTtc[_ngcontent-%COMP%]{margin-bottom:calc(37.5 * var(--res))}.scroll-container[_ngcontent-%COMP%]   .recap-container[_ngcontent-%COMP%]   .totalTtc[_ngcontent-%COMP%]   div[_ngcontent-%COMP%]{padding:0 calc(37.5 * var(--res));height:calc(120 * var(--res));display:flex;background-color:#c62f45e0;color:#fff;align-items:center;justify-content:space-between;border-radius:calc(20 * var(--res))}.scroll-container[_ngcontent-%COMP%]   .recap-container[_ngcontent-%COMP%]   .type-truck[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;margin:1em 0;padding:0 calc(41 * var(--res))}.scroll-container[_ngcontent-%COMP%]   .recap-container[_ngcontent-%COMP%]   .type-truck[_ngcontent-%COMP%]   ion-toggle[_ngcontent-%COMP%]{color:#0d7d3d;--background-checked: #0d7d3d}.scroll-container[_ngcontent-%COMP%]   .recap-container[_ngcontent-%COMP%]   .padding-horizontal[_ngcontent-%COMP%]{padding:0 calc(41 * var(--res))}.scroll-container[_ngcontent-%COMP%]   .recap-container[_ngcontent-%COMP%]   .padding-horizontal[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{display:block}.scroll-container[_ngcontent-%COMP%]   .recap-container[_ngcontent-%COMP%]   .padding-horizontal[_ngcontent-%COMP%]   ion-text[_ngcontent-%COMP%]{font-size:calc(40 * var(--res))}.scroll-container[_ngcontent-%COMP%]   .recap-container[_ngcontent-%COMP%]   .discount-details[_ngcontent-%COMP%]{padding-top:.5em;padding-bottom:1em;border-top:1px solid rgba(128,128,128,.448)}.scroll-container[_ngcontent-%COMP%]   .recap-container[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%]{font-family:Mont Bold}.scroll-container[_ngcontent-%COMP%]   .recap-container[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{font-family:Mont Bold;display:block;font-size:calc(45 * var(--res))}.scroll-container[_ngcontent-%COMP%]   .recap-container[_ngcontent-%COMP%]   .payment-container[_ngcontent-%COMP%]   ion-slide[_ngcontent-%COMP%]{height:calc(12.25 * var(--resH));box-shadow:0 3.08499px 10.7975px #00000016;border:.771248px solid rgba(218,218,218,.47);border-radius:3.85624px;--background: #ffffff}.scroll-container[_ngcontent-%COMP%]   .recap-container[_ngcontent-%COMP%]   .payment-container[_ngcontent-%COMP%]   ion-slide[_ngcontent-%COMP%]   .active[_ngcontent-%COMP%]{--background: var(--ion-color-primary)}.scroll-container[_ngcontent-%COMP%]   .recap-container[_ngcontent-%COMP%]   .payment-container[_ngcontent-%COMP%]   ion-slide[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center}.scroll-container[_ngcontent-%COMP%]   .recap-container[_ngcontent-%COMP%]   .cancel-discount[_ngcontent-%COMP%]{display:inline-block;padding:.6em;color:#fff;background-color:var(--ion-color-secondary);border-radius:5px;margin:0 0 1em}.scroll-container[_ngcontent-%COMP%]   .user-info-container[_ngcontent-%COMP%]{display:flex;flex-direction:column;width:100%;align-items:center;gap:5px;margin-bottom:20%}.scroll-container[_ngcontent-%COMP%]   .user-info-container[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{text-align:center;color:#419cfb;font-family:Mont Bold;font-size:16px;line-height:1.4;max-width:280px;margin:0 auto}.scroll-container[_ngcontent-%COMP%]   .user-info-container[_ngcontent-%COMP%]   .user-info-item[_ngcontent-%COMP%]{display:flex;flex-direction:row;justify-content:space-between;border-bottom:.6px solid #419cfb;border-radius:3px;width:83%;font-weight:400;height:2em;padding:16px}.scroll-container[_ngcontent-%COMP%]   .user-info-container[_ngcontent-%COMP%]   .user-info-item[_ngcontent-%COMP%]   label[_ngcontent-%COMP%], .scroll-container[_ngcontent-%COMP%]   .user-info-container[_ngcontent-%COMP%]   .user-info-item[_ngcontent-%COMP%]   .user-info-value[_ngcontent-%COMP%]{font-weight:600;color:#0b305c;font-size:15px}.profile-container[_ngcontent-%COMP%]{display:flex;align-items:center;background:#E4EBF3;padding:12px;border-radius:12px;margin:24px calc(41 * var(--res))}.avatar[_ngcontent-%COMP%]{width:40px;height:40px;background:#e1e1e1;border-radius:50%;display:flex;align-items:center;justify-content:center;margin-right:12px}.avatar[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:24px;color:#666}.info[_ngcontent-%COMP%]{display:flex;flex-direction:column}.title[_ngcontent-%COMP%]{font-size:12px;color:#666;margin-bottom:2px}.name[_ngcontent-%COMP%]{font-size:16px;color:#333;font-family:Mont SemiBold}.btn-validate[_ngcontent-%COMP%]{padding:0 calc(25 * var(--res));position:fixed;bottom:0;width:calc(100% - 10 * var(--res))}ion-button[_ngcontent-%COMP%]{margin:1em 0}ion-title[_ngcontent-%COMP%]{color:#0b305c;font-size:calc(55 * var(--res));text-align:start;font-family:Mont SemiBold;margin:1em 0}.header[_ngcontent-%COMP%]{--background: #F1F2F4;width:100%;margin-left:13px;margin-top:auto;margin-bottom:auto;padding:auto 0px;display:flex}.header[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%]{--background: transparent}.header[_ngcontent-%COMP%]   ion-img[_ngcontent-%COMP%]{width:calc(50 * var(--res));margin-right:calc(25 * var(--res))}"]})}}return o})()}];let D=(()=>{class o{static{this.\u0275fac=function(r){return new(r||o)}}static{this.\u0275mod=n.$C({type:o})}static{this.\u0275inj=n.G2t({imports:[O.iI.forChild(j),O.iI]})}}return o})();var B=e(93887);let w=(()=>{class o{static{this.\u0275fac=function(r){return new(r||o)}}static{this.\u0275mod=n.$C({type:o})}static{this.\u0275inj=n.G2t({imports:[p.MD,C.YN,a.bv,B.G,E.h,D]})}}return o})()},39316:(x,M,e)=>{e.d(M,{b:()=>_});var p=e(73308),C=e(26409),a=e(94934),O=e(45312),c=e(2978),u=e(82571),f=e(33607),h=e(77897);let _=(()=>{class m{constructor(d,i,s,v){this.http=d,this.commonSrv=i,this.baseUrlService=s,this.toastController=v,this.prices=[],this.currentDataProductScan=[],this.dataQrCode=[],this.url=this.baseUrlService.getOrigin()+O.c.basePath+"products"}getProducts(d){var i=this;return(0,p.A)(function*(){try{let s=new C.Nl;return d?.limit&&(s=s.append("limit",d?.limit)),yield(0,a.s)(i.http.get(i.url,{params:s}))}catch(s){const P={message:i.commonSrv.getError("",s).message,color:"danger"};return yield i.commonSrv.showToast(P),s}})()}getProduct(d){var i=this;return(0,p.A)(function*(){try{return yield(0,a.s)(i.http.get(`${i.url}/${d}`))}catch(s){const P={message:i.commonSrv.getError("",s).message,color:"danger"};return yield i.commonSrv.showToast(P),s}})()}static{this.\u0275fac=function(i){return new(i||m)(c.KVO(C.Qq),c.KVO(u.h),c.KVO(f.K),c.KVO(h.K_))}}static{this.\u0275prov=c.jDH({token:m,factory:m.\u0275fac,providedIn:"root"})}}return m})()},94440:(x,M,e)=>{e.d(M,{c:()=>C});var p=e(2978);let C=(()=>{class a{transform(c,...u){return c?c.length>u[0]?`${c.substring(0,u[0]-3)}...`:c:""}static{this.\u0275fac=function(u){return new(u||a)}}static{this.\u0275pipe=p.EJ8({name:"truncateString",type:a,pure:!0})}}return a})()}}]);