import { HttpClient, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { environment } from 'src/environments/environment';
import { BaseUrlService } from './base-url.service';
import { lastValueFrom } from 'rxjs';
import { Points, Benefits, FidelityStatus, UserEntity, Advantages } from '../models/fidelity-program.model';
import { Invitation, InvitationStatus } from '../models/invitation.model';
import { CommonService } from './common.service';
import { Language } from '../enum/language.enum';
import { TranslateConfigService } from './translate-config.service';
import { CartItem } from '../models/cart.model';

@Injectable({
  providedIn: 'root'
})
export class FidelityProgramService {
  base_url: string;

  ratioOfPointsByVolume = 0.2;
  private readonly POINTS_MATRIX = {
    AMIGO:   { 5: 1, 25: 5, 50: 10 },
    COLOMBE: { 5: 2, 25: 6, 50: 11 },
    PELICAN: { 5: 2, 25: 7, 50: 12 }
  };

  constructor(
    private baseUrl: BaseUrlService,
    private http: HttpClient,
    private commonSrv: CommonService,
    private translateSrv: TranslateConfigService
  ) {
    this.base_url = `${this.baseUrl.getOrigin()}${environment.basePath}`;
  }

  calculateTotalPointsOrder(items: CartItem[], entity: UserEntity): number {
    const status = entity?.points?.status || FidelityStatus.AMIGO;

    return items.reduce((total, item) => {
      const unit = item?.packaging?.unit?.value || 0; // ex: 5, 25, 50
      const quantity = item?.quantity || 0;

      const pointsPerUnit = this.POINTS_MATRIX[status]?.[unit] || 0;
      return total + (pointsPerUnit * quantity);
    }, 0);
  }

  async getPoints(param: any): Promise<UserEntity> {
    let params = new HttpParams();

    const { companyId } = param;

    if (companyId) params = params.append('companyId', companyId);

    return await lastValueFrom(
      this.http.get<UserEntity>(`${this.base_url}loyalty-program/points`, { params })
    );
  }

  async getBenefitRewards(param: any): Promise<{ data: Advantages[]; count: number }> {
    let params = new HttpParams();

    if (param?.monthly) {
      params = params.append('monthly', JSON.stringify(param.monthly));
    }
    if (param?.statusValue) {
      params = params.append('statusValue', JSON.stringify(param?.statusValue));
    }
    if (param?.annual) {
      params = params.append('annual', JSON.stringify(param.annual));
    }
    if (param?.punctual) {
      params = params.append('punctual', JSON.stringify(param.punctual));
    }

    return await lastValueFrom(
      this.http.get<{ data: Advantages[]; count: number }>(`${this.base_url}advantages`, { params })
    );
  }

  async acceptInvitation(invitationId: string, referredUserId: string): Promise<Invitation> {
    return await lastValueFrom(
      this.http.patch<Invitation>(`${this.base_url}invitations/${invitationId}/accept`, {
        referredUserId
      })
    );
  }

  async getInvitations(param?: any): Promise<Invitation> {
    let params = new HttpParams();

    const { limit, status, prospectTel } = param;

    if (limit) params = params.append('limit', limit);
    if (prospectTel) params = params.append('prospectTel', prospectTel);
    if (status) params = params.append('status', status);

    return await lastValueFrom(
      this.http.get<Invitation>(`${this.base_url}invitations`, { params })
    );

  }

  async sendReferralInvitation(userId: string, phoneNumber: number): Promise<Invitation> {
    try {

      const sendInvitation = {
        referrerId: userId,
        prospectTel: phoneNumber
      };

      const invitation = await lastValueFrom(
        this.http.post<Invitation>(`${this.base_url}invitations/${userId}/invite-referral`, sendInvitation)
      );

      return invitation;
    } catch (error) {
      const message = this.translateSrv.currentLang === Language.French
        ? 'Impossible d\'envoyer l\'invitation de parrainage'
        : 'Failed to send referral invitation';

      await this.commonSrv.showToast({
        message: error?.error?.message ?? this.commonSrv.getError(message, error).message,
        color: 'danger'
      });
      return error;
    }
  }

  async getPendingInvitationForUser(prospectTel: number): Promise<Invitation> {
    let params = new HttpParams();

    params = params.set('prospectTel', +prospectTel);
    params = params.append('status', InvitationStatus.PENDING);

    return await lastValueFrom(
      this.http.get<Invitation>(`${this.base_url}invitations/pending-invitation`, { params })
    );
  }

  /**
   * Calculate the displayed points based on the selected view
   * @param points - The points object containing totalPoints and unValidated
   * @param view - The view type ('pending' or 'total')
   * @returns The calculated points value
   */
  getDisplayedPoints(points: Points, view: 'pending' | 'total'): number {
    return view === 'pending' ? (points?.unValidated || 0) : (points?.totalPoints || 0);
  }

  /**
   * Get the appropriate label key for the selected points view
   * @param view - The view type ('pending' or 'total')
   * @returns The translation key for the label
   */
  getDisplayedPointsLabel(view: 'pending' | 'total'): string {
    return view === 'pending' ? 'fidelity-page.waiting' : 'fidelity-page.total-points';
  }
}
