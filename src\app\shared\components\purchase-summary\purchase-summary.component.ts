import { PromoCodeModalComponent } from 'src/app/menu-order/new-order/third-step/promo-code-modal/promo-code-modal.component';
import { ListOfItemsComponent } from 'src/app/menu-order/new-order/third-step/list-of-items/list-of-items.component';
import { TranslateConfigService } from 'src/app/shared/services/translate-config.service';
import { Cart, CartItem, OrderPrice, ShippingInfo } from 'src/app/shared/models/cart.model';
import { StorageService } from 'src/app/shared/services/storage.service';
import { PromoCodeAction } from 'src/app/shared/models/promo-code.model';
import { CommonService } from 'src/app/shared/services/common.service';
import { Component, Input, OnInit, inject } from '@angular/core';
import { Language } from 'src/app/shared/enum/language.enum';
import { Shipping } from 'src/app/shared/models/shipping';
import { CompanyCategory } from 'src/app/shared/models/company.model';
import { RenderType } from 'src/app/shared/enum/renderType.enum';
import { UserCategory } from 'src/app/shared/enum/user-category.enum';
import { Items, MarketOrderPrice, MarketPlaceCart } from '../../models/order-market-place';
import { FidelityProgramService } from '../../services/fidelity-program.service';
import { Carrier } from '../../models/order';
import { FidelityStatus, UserEntity } from '../../models/fidelity-program.model';

@Component({
  selector: 'app-purchase-summary',
  templateUrl: './purchase-summary.component.html',
  styleUrls: ['./purchase-summary.component.scss'],
})
export class PurchaseSummaryComponent implements OnInit {

  @Input() cart!: Cart;
  @Input() isLoading!: boolean;
  @Input() orderPrice!: OrderPrice;
  @Input() itemsLimited: CartItem[] = [];
  @Input() itemsMarket: Items;
  @Input() shippingInfo: ShippingInfo;
  @Input() isPoint: boolean;
  @Input() shipping: Partial<Shipping>;
  @Input() points: string | number;
  @Input() carrierInformation: Carrier;
  @Input() carrierOrderInformation: Carrier;

  isDiscount: boolean;
  CompanyCategory = CompanyCategory;
  userCategory = UserCategory;
  bagDefaultValue = 50;
  choiceDelivery: boolean;
  choiceDeliveryLocation: boolean;
  customerDeliveryDestination: string;
  user: UserEntity;


  isCodePromoActive: Boolean;
  promoCodeAction = PromoCodeAction;
  renderType = RenderType;

  storageService = inject(StorageService);
  protected commonSrv = inject(CommonService);
  public loyaltyProgramSrv = inject(FidelityProgramService);

  isFrench: boolean = inject(TranslateConfigService).currentLang === Language.French;
  isCompanyCategoryBaker: boolean = false;

  private readonly POINTS_MATRIX = {
    AMIGO:   { 5: 1, 25: 5, 50: 10 },
    COLOMBE: { 5: 2, 25: 6, 50: 11 },
    PELICAN: { 5: 2, 25: 7, 50: 12 }
  };

  ngOnInit() {
    this.isCompanyCategoryBaker = this.commonSrv?.user?.company?.category === CompanyCategory.Baker;
    const carrierInfo = this.storageService.load('carrierInformation');
    this.carrierInformation = carrierInfo ? JSON.parse(carrierInfo) : null;

    if (this.carrierInformation) {
      if (this.carrierInformation?.vehicleCategory === 'Autre Catégorie' && this.carrierInformation?.otherCategory) {
        this.carrierInformation['vehicleCategory'] = this.carrierInformation?.otherCategory;
      }
    }

    this.choiceDelivery = this.storageService.load('choiceDelivery') === 'true';
    this.choiceDeliveryLocation = this.storageService.load('choiceDeliveryLocation') === 'true';
    this.customerDeliveryDestination = this.storageService.load('customerDeliveryDestination');

    // Si on est en mode points et qu'aucun total n'est fourni, le calculer
    if (this.isPoint && (!this.points || this.points === 0)) {
      this.points = this.calculateTotalPoints();
    }

    console.log('[PURCHASE-SUMMARY] Mode points:', this.isPoint, 'Total points:', this.points);
  }

  async openListOfItems() {
    const modal = await this.commonSrv.modalCtrl.create({
      component: ListOfItemsComponent,
      mode: 'ios',
      cssClass: 'modalClass',
      componentProps: { items: this.cart.items },
    });
    await modal.present();

  }

  getTotalQuantityOrder(items: CartItem[]): number {
    const totalQuantity = items?.reduce((total, cartElement) => total += (cartElement?.quantity * cartElement?.packaging?.unit?.value), 0);
    return (totalQuantity / this.bagDefaultValue);
  }

  calculateTotalPoints(): number {
    if (!this.cart?.items || this.cart.items.length === 0) {
      return 0;
    }

    return this.cart.items.reduce((total, item) => {
      return total + this.getPointsForItem(item);
    }, 0);
  }

  private getListOfItemsColumnAndRow(itemsLength: number = 0): string[] {
    if (!itemsLength || itemsLength === 1) return [];
    if (itemsLength === 2) return ['two-columns'];
    if (itemsLength === 3) return ['three-columns', 'one-row'];
    if (itemsLength === 4) return ['two-columns', 'two-rows'];
    if ([5, 6].includes(itemsLength)) return ['three-columns', 'two-rows'];
    return ['three-columns', 'three-rows']; // case of more than 7 items
  }

  codePromoInfosHandle(event: any) {
    this.isCodePromoActive = event.detail.checked;
    this.openCodePromoModal();
  }

  async openCodePromoModal() {
    if (!this.isCodePromoActive) {
      return;
    }
    const modal = await this.commonSrv.modalCtrl.create({
      component: PromoCodeModalComponent,
      initialBreakpoint: 0.4,
      cssClass: 'modal',
      breakpoints: [0, 0.6, 0.7],
      mode: 'ios',
    });
    await modal.present();
    const { data } = await modal.onWillDismiss();
    if (!data) {
      this.isCodePromoActive = false;
      return;
    }
    this.isDiscount = true;
    const cart = JSON.parse(this.storageService.load('cart'));
    if (data > cart.amount.TTC) {
      this.commonSrv.showToast({
        color: 'danger',
        message:
          this.isFrench
            ? 'le montant du code promo est superieur au montant de votre commande'
            : 'The promo code amount is greater than your order total amount',
      });
      return (this.isCodePromoActive = false);
    }
    cart.amount.discount = data;
    cart.amount.TTC -= cart.amount.discount;
    this.orderPrice = cart.amount;
    this.storageService.store('cart', JSON.stringify(cart));
    this.commonSrv.showToast({
      color: 'success',
      message:
        this.isFrench
          ? 'Code promo appliqué avec succès'
          : 'The promo code was successfully applied',
    });
    return (this.isCodePromoActive = false);
  }

  cancelDiscount() {
    const cart = JSON.parse(this.storageService.load('cart'));
    cart.amount.TTC += cart.amount.discount;
    delete cart.amount.discount;
    this.orderPrice = cart.amount;
    this.storageService.store('cart', JSON.stringify(cart));
    this.isDiscount = false;
    this.commonSrv.showToast({
      color: 'success',
      message:
        this.isFrench
          ? 'Le code promo a été annuler avec succès'
          : 'The promo code was successfully canceled',
    });
  }

  getPointsForItem(item: CartItem): number {
    // Récupérer le statut de fidélité de l'utilisateur
    let status = this.commonSrv?.user?.points?.status;

    // Si pas de statut dans commonSrv, essayer de le récupérer depuis le storage
    if (!status) {
      try {
        const userInfo = this.storageService.load('USER_INFO');
        if (userInfo) {
          const user = JSON.parse(userInfo);
          status = user?.points?.status;
        }
      } catch (error) {
        console.warn('[POINTS] Erreur lors de la récupération du statut utilisateur:', error);
      }
    }

    // Utiliser AMIGO par défaut si aucun statut trouvé
    const fidelityStatus = status || FidelityStatus.AMIGO;

    const unitValue = item?.packaging?.unit?.value || 0;
    const quantity = item?.quantity || 0;

    // Convertir le statut en clé string pour la matrice
    const statusKey = this.getStatusKey(fidelityStatus);
    const pointPerUnit = this.POINTS_MATRIX?.[statusKey]?.[unitValue] || 0;

    const totalPoints = pointPerUnit * quantity;

    console.log(`[POINTS] Item: ${item?.product?.label}, Unit: ${unitValue}, Quantity: ${quantity}, Status: ${statusKey}, Points per unit: ${pointPerUnit}, Total: ${totalPoints}`);

    return totalPoints;
  }

  private getStatusKey(status: FidelityStatus): string {
    switch (status) {
      case FidelityStatus.AMIGO:
        return 'AMIGO';
      case FidelityStatus.COLOMBE:
        return 'COLOMBE';
      case FidelityStatus.PELICAN:
        return 'PELICAN';
      default:
        return 'AMIGO';
    }
  }

}
