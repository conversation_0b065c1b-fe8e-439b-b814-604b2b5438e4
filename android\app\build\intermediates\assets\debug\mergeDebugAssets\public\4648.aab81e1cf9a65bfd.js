"use strict";(self.webpackChunkapp=self.webpackChunkapp||[]).push([[4648],{24648:(L,y,s)=>{s.r(y),s.d(y,{FormSigninPageModule:()=>X});var O=s(74657),u=s(77575),_=s(73308),a=s(37222),f=s(45312),n=s(2978),c=s(77897),x=s(93860);let R=(()=>{class i{constructor(t){this.platform=t}simulateOtpReceived(t,e=3e3){!this.platform.is("cordova")&&!this.platform.is("capacitor")&&(console.log("[OTP-TEST] Simulation d'un SMS OTP dans",e,"ms..."),setTimeout(()=>{const r=Math.floor(1e5+9e5*Math.random()).toString();console.log("[OTP-TEST] SMS simul\xe9 re\xe7u avec le code:",r),t(r)},e))}simulateVariousOtpFormats(t){const e=["123456","Votre code OTP: 234567",'Code de v\xe9rification: "345678"',"PIN: 456789 pour votre connexion","Utilisez le code 567890 pour vous connecter"],r=e[Math.floor(Math.random()*e.length)];console.log("[OTP-TEST] SMS simul\xe9:",r);const d=r.match(/\d{6}/);d&&setTimeout(()=>t(d[0]),2e3)}testClipboardOtp(t){return(0,_.A)(function*(){try{if(navigator.clipboard&&navigator.clipboard.writeText){const e="123456";yield navigator.clipboard.writeText(`Votre code OTP: ${e}`),console.log("[OTP-TEST] Code OTP copi\xe9 dans le presse-papiers pour test"),setTimeout(()=>{t(e)},1e3)}}catch(e){console.error("[OTP-TEST] Erreur lors du test presse-papiers:",e)}})()}showTestInstructions(){console.log('\n    \u{1f9ea} INSTRUCTIONS DE TEST OTP AUTO-FILL:\n    \n    1. En mode d\xe9veloppement (navigateur):\n       - Un code OTP sera automatiquement simul\xe9 apr\xe8s 3 secondes\n       \n    2. Sur Android (device/\xe9mulateur):\n       - Envoyez-vous un SMS avec un code \xe0 6 chiffres\n       - L\'application devrait d\xe9tecter automatiquement le code\n       \n    3. Test manuel:\n       - Copiez un code dans le presse-papiers\n       - L\'application peut le d\xe9tecter selon la plateforme\n       \n    4. Formats de SMS support\xe9s:\n       - "123456"\n       - "Code: 123456"\n       - "OTP: 123456"\n       - "Votre code de v\xe9rification: 123456"\n    ')}static{this.\u0275fac=function(e){return new(e||i)(n.KVO(c.OD))}}static{this.\u0275prov=n.jDH({token:i,factory:i.\u0275fac,providedIn:"root"})}}return i})();var C=s(56610);function j(i,h){1&i&&(n.j41(0,"div",13),n.nrm(1,"ion-spinner",14),n.j41(2,"ion-text",15)(3,"small"),n.EFF(4,"D\xe9tection automatique en cours..."),n.k0s()()())}function P(i,h){if(1&i){const t=n.RV6();n.j41(0,"span",16),n.bIt("click",function(){n.eBV(t);const r=n.XpG();return n.Njj(r.resendCode())}),n.EFF(1),n.nI1(2,"translate"),n.k0s()}2&i&&(n.R7$(1),n.SpI(" ",n.bMT(2,1,"signin-page.modal-otp-description.resend"),""))}function D(i,h){1&i&&n.nrm(0,"ion-spinner",17)}function p(i,h){1&i&&n.nrm(0,"ion-spinner",17)}let l=(()=>{class i{constructor(t,e,r,d,v){this.modalCtrl=t,this.authService=e,this.router=r,this.platform=d,this.otpTestService=v,this.isLoading=!1,this.isListening=!1,this.loginForm=new a.gE({code:new a.MJ("",[a.k0.required])})}ngOnInit(){this.startListeningForOtp(),f.c.production||this.otpTestService.showTestInstructions()}ngOnDestroy(){this.stopListeningForOtp()}closeModal(){this.stopListeningForOtp(),this.modalCtrl.dismiss()}login(){var t=this;return(0,_.A)(function*(){t.isLoading=!0,t.code={value:Number(t.loginForm.get("code").value)},(yield t.authService.loginWhitOtp(t.code))instanceof Error||(t.router.navigateByUrl("navigation"),t.loginForm.reset(),t.modalCtrl.dismiss()),t.isLoading=!1})()}resendCode(){var t=this;return(0,_.A)(function*(){t.isLoading=!0,yield t.authService.generateOtp(t.credentialOtp),t.isLoading=!1})()}startListeningForOtp(){console.log("[OTP] Initialisation de la lecture automatique..."),this.isListening=!0;const t=e=>{console.log("[OTP] Code OTP d\xe9tect\xe9 automatiquement :",e),this.loginForm.get("code")?.setValue(e),this.isListening=!1,setTimeout(()=>{this.login()},1e3)};this.platform.is("cordova")||this.platform.is("capacitor")?this.authService.startOtpAutoFill(t):f.c.production||this.otpTestService.simulateOtpReceived(t,3e3),setTimeout(()=>{this.isListening&&(this.stopListeningForOtp(),console.log("[OTP] Timeout - arr\xeat de l'\xe9coute automatique"))},3e5)}stopListeningForOtp(){this.isListening&&(console.log("[OTP] Arr\xeat de la lecture automatique..."),this.isListening=!1,this.authService.stopOtpAutoFill())}static{this.\u0275fac=function(e){return new(e||i)(n.rXU(c.W3),n.rXU(x.k),n.rXU(u.Ix),n.rXU(c.OD),n.rXU(R))}}static{this.\u0275cmp=n.VBU({type:i,selectors:[["app-modal-otp-input"]],inputs:{credentialOtp:"credentialOtp"},decls:33,vars:21,consts:[[1,"select-account-container","bottom-sheet-content"],[1,"ion-text-center","ion-padding"],["slot","end",3,"click"],["src","assets/icons/close.svg"],[1,"container"],["class","auto-detection-indicator",4,"ngIf"],[1,"input-group",3,"formGroup"],["position","floating"],["type","number","placeholder","Exemple: 6596589","formControlName","code"],["class","link",3,"click",4,"ngIf"],["name","bubbles",4,"ngIf"],[1,"button-confirm"],["type","submit","color","primary","expand","block",1,"btn","btn--meduim",3,"disabled","click"],[1,"auto-detection-indicator"],["name","circles","color","primary"],["color","primary"],[1,"link",3,"click"],["name","bubbles"]],template:function(e,r){1&e&&(n.j41(0,"div",0)(1,"ion-header")(2,"ion-toolbar",1)(3,"ion-thumbnail",2),n.bIt("click",function(){return r.closeModal()}),n.nrm(4,"ion-img",3),n.k0s(),n.j41(5,"ion-label"),n.EFF(6),n.nI1(7,"translate"),n.k0s()()(),n.j41(8,"ion-content",4)(9,"ion-label"),n.EFF(10),n.nI1(11,"translate"),n.k0s(),n.DNE(12,j,5,0,"div",5),n.j41(13,"form",6)(14,"ion-item")(15,"ion-label",7),n.EFF(16),n.nI1(17,"translate"),n.k0s(),n.nrm(18,"ion-input",8),n.k0s()(),n.j41(19,"div",1)(20,"ion-text")(21,"ion-label"),n.EFF(22),n.nI1(23,"translate"),n.k0s(),n.nrm(24,"br"),n.DNE(25,P,3,3,"span",9),n.DNE(26,D,1,0,"ion-spinner",10),n.k0s()(),n.j41(27,"div",11)(28,"ion-button",12),n.bIt("click",function(){return r.login()}),n.j41(29,"ion-label"),n.EFF(30),n.nI1(31,"translate"),n.k0s(),n.DNE(32,p,1,0,"ion-spinner",10),n.k0s()()()()),2&e&&(n.R7$(6),n.SpI(" ",n.bMT(7,11,"signin-page.modal-otp-description.title")," "),n.R7$(4),n.SpI(" ",n.bMT(11,13,"signin-page.modal-otp-description.instruction")," "),n.R7$(2),n.Y8G("ngIf",r.isListening),n.R7$(1),n.Y8G("formGroup",r.loginForm),n.R7$(3),n.SpI(" ",n.bMT(17,15,"signin-page.modal-otp-description.label"),""),n.R7$(6),n.SpI(" ",n.bMT(23,17,"signin-page.modal-otp-description.do-not-recieve-the-code")," "),n.R7$(3),n.Y8G("ngIf",!r.isLoading&&r.credentialOtp),n.R7$(1),n.Y8G("ngIf",r.isLoading),n.R7$(2),n.Y8G("disabled",r.loginForm.invalid||r.isLoading),n.R7$(2),n.SpI(" ",n.bMT(31,19,"signin-page.modal-otp-description.button-confirm")," "),n.R7$(2),n.Y8G("ngIf",r.isLoading))},dependencies:[C.bT,a.qT,a.BC,a.cb,c.Jm,c.W9,c.eU,c.KW,c.$w,c.uz,c.he,c.w2,c.IO,c.Zx,c.ai,c.su,a.j4,a.JD,O.D9],styles:["*[_ngcontent-%COMP%]{margin:0;padding:0}.header-md[_ngcontent-%COMP%]:after{background-image:none!important}.modal[_ngcontent-%COMP%]{--border-radius: calc(60 * var(--res))}.input-group[_ngcontent-%COMP%]{margin-top:calc(75 * var(--res))}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{--padding-start: 0;margin-bottom:calc(50 * var(--res));--placeholder-color: $color-six}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-input[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{font-family:Mont Regular;font-weight:400;font-size:calc(42 * var(--res));color:#1e1e1e}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{padding-top:calc(25 * var(--res));padding-bottom:calc(25 * var(--res))}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]{margin-bottom:calc(50 * var(--res));display:flex;align-items:center;justify-content:space-between;border-bottom-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-250, #c8c7cc)));border-bottom-width:.55px;border-bottom-style:solid}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{width:90%;margin-bottom:0;--border-color: transparent;--inner-border-width: 0px 0px 0px 0px;--border-style: none}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-checkbox[_ngcontent-%COMP%]{margin-top:5%}.bg-primary[_ngcontent-%COMP%]{color:#143c5d}.message[_ngcontent-%COMP%]{margin-bottom:calc(75 * var(--res))}.message[_ngcontent-%COMP%]   ion-title[_ngcontent-%COMP%]{font-weight:800;font-size:calc(70 * var(--res));padding-bottom:calc(25 * var(--res))}.message[_ngcontent-%COMP%]   ion-text[_ngcontent-%COMP%]{font-weight:400;color:#757474;display:block;line-height:1.5em!important}.btn--meduim[_ngcontent-%COMP%]{height:calc(130 * var(--res));font-size:calc(50 * var(--res));font-family:var(--mont-bold)}.btn--meduim[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:var(--mont-bold)}.btn--small[_ngcontent-%COMP%]{height:calc(100 * var(--res))}.btn--round[_ngcontent-%COMP%]{--border-radius: 50%;padding-inline-start:0;padding-inline-end:0;--padding-start: 0;--padding-end: 0}.btn--border-primary[_ngcontent-%COMP%]{--background: transparent;--background-activated: #c4e6d0;--border-color: var(--ion-color-primary);--border-style: solid;--border-width: 2px}.btn--border-secondary[_ngcontent-%COMP%]{--border-width: 2px;--background: transparent;--background-activated: #ddabb2;--border-color: var(--ion-color-secondary);--border-style: solid}.btn--upper[_ngcontent-%COMP%]{text-transform:uppercase}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]{overflow:hidden}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar{width:5px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-track{box-shadow:inset 0 0 5px #546e8d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background:#143c5d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover{background:#143c5d}.bottom-sheet-content[_ngcontent-%COMP%]{background:#fdfdfe;height:100%}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-thumbnail[_ngcontent-%COMP%]{margin-left:calc(31.25 * var(--res));background-color:#1e1e1e;--border-radius: 50%;--size: 23}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:Mont Bold;font-size:calc(45 * var(--res));text-align:center;color:#757474}.bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%], .bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]{padding:calc(75 * var(--res))}.font-size-5[_ngcontent-%COMP%]{font-size:calc(42 * var(--res))}.select-account-container[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%]{--padding-start: 1em;--padding-end: 1em;--padding-top: .5em;--padding-bottom: .5em}.select-account-container[_ngcontent-%COMP%]   .button-confirm[_ngcontent-%COMP%]{margin-top:1rem;width:75%;padding-left:5rem}.select-account-container[_ngcontent-%COMP%]   .link[_ngcontent-%COMP%]{text-decoration:underline;color:var(--ion-color-primary)}.select-account-container[_ngcontent-%COMP%]   .auto-detection-indicator[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;margin:1rem 0;padding:1rem;background:rgba(var(--ion-color-primary-rgb),.1);border-radius:8px}.select-account-container[_ngcontent-%COMP%]   .auto-detection-indicator[_ngcontent-%COMP%]   ion-spinner[_ngcontent-%COMP%]{margin-bottom:.5rem}"]})}}return i})();var o=s(58133),g=s(99987),m=s(62049);const M=function(i){return{active:i}};function b(i,h){if(1&i){const t=n.RV6();n.j41(0,"ion-slide",8),n.bIt("click",function(){const d=n.eBV(t).$implicit,v=n.XpG();return n.Njj(v.selectUser(d.category))}),n.j41(1,"ion-card",10)(2,"div",11),n.nrm(3,"div",12),n.k0s(),n.j41(4,"ion-card-header"),n.nrm(5,"ion-img",13),n.k0s(),n.j41(6,"ion-card-content")(7,"ion-label"),n.EFF(8),n.k0s()()()()}if(2&i){const t=h.$implicit,e=n.XpG();n.R7$(1),n.Y8G("color",e.userCategory===t.category?"primary":"")("ngClass",n.eq3(4,M,e.userCategory===t.category)),n.R7$(4),n.Y8G("src",e.userCategory===t.category?t.activeImg:t.img),n.R7$(3),n.JRh(t.label)}}let k=(()=>{class i{constructor(t,e,r){this.modalCtrl=t,this.router=e,this.translateService=r,this.slideOpts={initialSlide:0,speed:400,slidesPerView:2},this.userTypesForFrench=[{label:"Particulier",category:o.s.Particular,activeImg:"assets/icons/user-white.svg",img:"assets/icons/user.svg"},{label:"Revendeur",category:o.s.Retailer,activeImg:"assets/icons/dealer-white.svg",img:"assets/icons/dealer.svg"}],this.userTypesForEnglish=[{label:"Particular",category:o.s.Particular,activeImg:"assets/icons/user-white.svg",img:"assets/icons/user.svg"},{label:"Retailer",category:o.s.Retailer,activeImg:"assets/icons/dealer-white.svg",img:"assets/icons/dealer.svg"},{label:"Employee Cimencam",category:o.s.EmployeeLapasta,activeImg:"assets/icons/user-white.svg",img:"assets/icons/user.svg"}],this.userTypes=[],this.userCategory=o.s.Particular}ngOnInit(){this.userTypes=g.T.French===this.translateService.currentLang?this.userTypesForFrench:this.userTypesForEnglish}closeModal(){this.modalCtrl.dismiss()}createAccount(){this.modalCtrl.dismiss(),this.router.navigateByUrl("authentication/signup/first-step/"+this.userCategory)}selectUser(t){this.userCategory=t}static{this.\u0275fac=function(e){return new(e||i)(n.rXU(c.W3),n.rXU(u.Ix),n.rXU(m.E))}}static{this.\u0275cmp=n.VBU({type:i,selectors:[["app-modal-select-account"]],decls:18,vars:8,consts:[[1,"select-account-container","bottom-sheet-content"],[1,"ion-text-center","ion-padding"],["slot","end",3,"click"],["src","assets/icons/close.svg"],[1,"container"],[3,"options"],[3,"click",4,"ngFor","ngForOf"],[1,"ion-text-end"],[3,"click"],["src","assets/icons/next-green.svg"],[3,"color","ngClass"],[1,"ellipses"],[1,"sonOfEllipse"],[3,"src"]],template:function(e,r){1&e&&(n.j41(0,"div",0)(1,"ion-header")(2,"ion-toolbar",1)(3,"ion-thumbnail",2),n.bIt("click",function(){return r.closeModal()}),n.nrm(4,"ion-img",3),n.k0s(),n.j41(5,"ion-label"),n.EFF(6),n.nI1(7,"translate"),n.k0s()()(),n.j41(8,"ion-content")(9,"div",4)(10,"ion-slides",5),n.DNE(11,b,9,6,"ion-slide",6),n.k0s(),n.j41(12,"div",7)(13,"ion-chip",8),n.bIt("click",function(){return r.createAccount()}),n.j41(14,"ion-label"),n.EFF(15),n.nI1(16,"translate"),n.k0s(),n.nrm(17,"ion-img",9),n.k0s()()()()()),2&e&&(n.R7$(6),n.SpI(" ",n.bMT(7,4,"signin-page.modal-create-account.title")," "),n.R7$(4),n.Y8G("options",r.slideOpts),n.R7$(1),n.Y8G("ngForOf",r.userTypes),n.R7$(4),n.JRh(n.bMT(16,6,"signin-page.modal-create-account.next-button-label")))},dependencies:[C.YU,C.Sq,c.b_,c.I9,c.ME,c.ZB,c.W9,c.eU,c.KW,c.he,c.q3,c.tR,c.Zx,c.ai,O.D9],styles:["*[_ngcontent-%COMP%]{margin:0;padding:0}.header-md[_ngcontent-%COMP%]:after{background-image:none!important}.modal[_ngcontent-%COMP%]{--border-radius: calc(60 * var(--res))}.input-group[_ngcontent-%COMP%]{margin-top:calc(75 * var(--res))}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{--padding-start: 0;margin-bottom:calc(50 * var(--res));--placeholder-color: $color-six}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-input[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{font-family:Mont Regular;font-weight:400;font-size:calc(42 * var(--res));color:#1e1e1e}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{padding-top:calc(25 * var(--res));padding-bottom:calc(25 * var(--res))}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]{margin-bottom:calc(50 * var(--res));display:flex;align-items:center;justify-content:space-between;border-bottom-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-250, #c8c7cc)));border-bottom-width:.55px;border-bottom-style:solid}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{width:90%;margin-bottom:0;--border-color: transparent;--inner-border-width: 0px 0px 0px 0px;--border-style: none}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-checkbox[_ngcontent-%COMP%]{margin-top:5%}.bg-primary[_ngcontent-%COMP%]{color:#143c5d}.message[_ngcontent-%COMP%]{margin-bottom:calc(75 * var(--res))}.message[_ngcontent-%COMP%]   ion-title[_ngcontent-%COMP%]{font-weight:800;font-size:calc(70 * var(--res));padding-bottom:calc(25 * var(--res))}.message[_ngcontent-%COMP%]   ion-text[_ngcontent-%COMP%]{font-weight:400;color:#757474;display:block;line-height:1.5em!important}.btn--meduim[_ngcontent-%COMP%]{height:calc(130 * var(--res));font-size:calc(50 * var(--res));font-family:var(--mont-bold)}.btn--meduim[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:var(--mont-bold)}.btn--small[_ngcontent-%COMP%]{height:calc(100 * var(--res))}.btn--round[_ngcontent-%COMP%]{--border-radius: 50%;padding-inline-start:0;padding-inline-end:0;--padding-start: 0;--padding-end: 0}.btn--border-primary[_ngcontent-%COMP%]{--background: transparent;--background-activated: #c4e6d0;--border-color: var(--ion-color-primary);--border-style: solid;--border-width: 2px}.btn--border-secondary[_ngcontent-%COMP%]{--border-width: 2px;--background: transparent;--background-activated: #ddabb2;--border-color: var(--ion-color-secondary);--border-style: solid}.btn--upper[_ngcontent-%COMP%]{text-transform:uppercase}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]{overflow:hidden}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar{width:5px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-track{box-shadow:inset 0 0 5px #546e8d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background:#143c5d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover{background:#143c5d}.bottom-sheet-content[_ngcontent-%COMP%]{background:#fdfdfe;height:100%}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-thumbnail[_ngcontent-%COMP%]{margin-left:calc(31.25 * var(--res));background-color:#1e1e1e;--border-radius: 50%;--size: 23}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:Mont Bold;font-size:calc(45 * var(--res));text-align:center;color:#757474}.bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%], .bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]{padding:calc(75 * var(--res))}.font-size-5[_ngcontent-%COMP%]{font-size:calc(42 * var(--res))}.select-account-container[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%]{padding:0}.select-account-container[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%]   ion-slides[_ngcontent-%COMP%]{padding:calc(75 * var(--res)) calc(75 * var(--res)) calc(50 * var(--res)) calc(75 * var(--res))}.select-account-container[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%]   ion-slides[_ngcontent-%COMP%]   ion-slide[_ngcontent-%COMP%]{margin-right:calc(50 * var(--res));width:calc(44 * var(--resW))!important}.select-account-container[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%]   ion-slides[_ngcontent-%COMP%]   ion-slide[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]{height:calc(50 * var(--resW));--background: $color-nine;border:1px solid #ebebeb;border-radius:calc(45 * var(--res));padding:calc(75 * var(--res))}.select-account-container[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%]   ion-slides[_ngcontent-%COMP%]   ion-slide[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]   .ellipses[_ngcontent-%COMP%]{display:none}.select-account-container[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%]   ion-slides[_ngcontent-%COMP%]   ion-slide[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]   ion-card-header[_ngcontent-%COMP%]{height:80%}.select-account-container[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%]   ion-slides[_ngcontent-%COMP%]   ion-slide[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]   ion-card-header[_ngcontent-%COMP%]   ion-img[_ngcontent-%COMP%]{margin:auto;width:35%;height:100%}.select-account-container[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%]   ion-slides[_ngcontent-%COMP%]   ion-slide[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]{margin-top:-10px}.select-account-container[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%]   ion-slides[_ngcontent-%COMP%]   ion-slide[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:Mont Bold;text-align:center;color:#1e1e1e;font-size:calc(34 * var(--res))}@supports (mask-image: paint(smooth-corners)){.select-account-container[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%]   ion-slides[_ngcontent-%COMP%]   ion-slide[_ngcontent-%COMP%]   ion-card.is-loaded[_ngcontent-%COMP%]{border-radius:0;-webkit-mask-image:paint(smooth-corners);mask-image:paint(smooth-corners);--smooth-corners: 4.8}}.select-account-container[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%]   ion-slides[_ngcontent-%COMP%]   ion-slide[_ngcontent-%COMP%]   .active[_ngcontent-%COMP%]{--background: $color-primary;--color: $color-nine}.select-account-container[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%]   ion-slides[_ngcontent-%COMP%]   ion-slide[_ngcontent-%COMP%]   .active[_ngcontent-%COMP%]   .ellipses[_ngcontent-%COMP%]{display:block;position:absolute;clip-path:polygon(0 0,100% 0,100% 41%,100% 100%,60% 76%,42% 60%,29% 48%,16% 31%,7% 14%);top:0;right:0;width:60%;height:55%;background-color:#419cfb}.select-account-container[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%]   ion-slides[_ngcontent-%COMP%]   ion-slide[_ngcontent-%COMP%]   .active[_ngcontent-%COMP%]   .ellipses[_ngcontent-%COMP%]   .sonOfEllipse[_ngcontent-%COMP%]{width:40%;background:hsl(0,0%,85%);height:100%;clip-path:polygon(59% 31%,77% 18%,100% 8%,100% 100%,24% 100%,32% 71%,44% 49%);margin-left:auto}.select-account-container[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%]   ion-slides[_ngcontent-%COMP%]   ion-slide[_ngcontent-%COMP%]   .active[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{color:#fff}.select-account-container[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%]   ion-chip[_ngcontent-%COMP%]{padding-right:calc(75 * var(--res))}.select-account-container[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%]   ion-chip[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{color:#143c5d}"]})}}return i})();var w=s(6997),T=s(14599),F=s(82571),I=s(43556),$=s(66866),S=s(511);function E(i,h){if(1&i){const t=n.RV6();n.j41(0,"div",4)(1,"span",5),n.EFF(2),n.nI1(3,"translate"),n.k0s(),n.j41(4,"div",6)(5,"ion-text",7),n.EFF(6),n.nI1(7,"translate"),n.k0s(),n.nrm(8,"ion-img",8),n.j41(9,"ion-text",9),n.EFF(10),n.nI1(11,"translate"),n.k0s(),n.j41(12,"div",10)(13,"div",11),n.nrm(14,"input",12),n.k0s(),n.j41(15,"div",13),n.bIt("click",function(){n.eBV(t);const r=n.XpG();return n.Njj(r.changeLanguage())}),n.EFF(16),n.nI1(17,"translate"),n.k0s()()()()}if(2&i){const t=n.XpG();n.Y8G("formGroup",t.loginOtpForm),n.R7$(2),n.JRh(n.bMT(3,7,"signin-page.title")),n.R7$(4),n.SpI(" ",n.bMT(7,9,"signin-page.description")," "),n.R7$(4),n.SpI(" ",n.bMT(11,11,"signin-page.text-message")," "),n.R7$(2),n.Y8G("hidden","phone"!==t.tabOption),n.R7$(2),n.Y8G("readonly",t.isLoading),n.R7$(2),n.SpI(" ",n.bMT(17,13,"signin-page.change-language")," ")}}function G(i,h){if(1&i){const t=n.RV6();n.j41(0,"div",4)(1,"span",5),n.EFF(2),n.nI1(3,"translate"),n.k0s(),n.j41(4,"div",6),n.nrm(5,"ion-img",14),n.j41(6,"ion-text",9),n.EFF(7),n.nI1(8,"translate"),n.k0s(),n.j41(9,"div",10)(10,"div",11),n.nrm(11,"input",15),n.k0s()()(),n.j41(12,"div",16)(13,"div",17),n.EFF(14),n.nI1(15,"translate"),n.j41(16,"div",18),n.bIt("click",function(){n.eBV(t);const r=n.XpG();return n.Njj(r.resendCode())}),n.j41(17,"a"),n.EFF(18),n.nI1(19,"translate"),n.k0s()()(),n.j41(20,"div",19)(21,"div",20)(22,"a",21),n.bIt("click",function(){n.eBV(t);const r=n.XpG();return n.Njj(r.goBackToPhoneInput())}),n.EFF(23),n.nI1(24,"translate"),n.k0s()()()()()}if(2&i){const t=n.XpG();n.Y8G("formGroup",t.otpForm),n.R7$(2),n.JRh(n.bMT(3,10,"signin-page.modal-otp-description.title")),n.R7$(3),n.Y8G("src","../assets/logos/otp.svg"),n.R7$(2),n.SpI(" ",n.bMT(8,12,"signin-page.modal-otp-description.label")," "),n.R7$(2),n.Y8G("hidden","phone"!==t.tabOption),n.R7$(2),n.Y8G("readonly",t.isLoading)("readonly",t.isLoading),n.R7$(3),n.SpI(" ",n.bMT(15,14,"signin-page.modal-otp-description.resend-otp")," "),n.R7$(4),n.JRh(n.bMT(19,16,"signin-page.modal-otp-description.resend")),n.R7$(5),n.JRh(n.bMT(24,18,"signin-page.modal-otp-description.change-phone"))}}function U(i,h){1&i&&n.nrm(0,"ion-spinner",24)}function B(i,h){if(1&i){const t=n.RV6();n.j41(0,"ion-button",22),n.bIt("click",function(){n.eBV(t);const r=n.XpG();return n.Njj(r.login())}),n.EFF(1),n.nI1(2,"translate"),n.DNE(3,U,1,0,"ion-spinner",23),n.k0s()}if(2&i){const t=n.XpG();n.FS9("disabled","INVALID"===t.loginOtpForm.status),n.R7$(1),n.SpI(" ",n.bMT(2,3,"signin-page.modal-otp-description.button-confirm")," "),n.R7$(2),n.Y8G("ngIf",t.isLoading)}}function A(i,h){1&i&&n.nrm(0,"ion-spinner",24)}function N(i,h){if(1&i){const t=n.RV6();n.j41(0,"ion-button",22),n.bIt("click",function(){n.eBV(t);const r=n.XpG();return n.Njj(r.verifyOtp())}),n.EFF(1),n.nI1(2,"translate"),n.DNE(3,A,1,0,"ion-spinner",23),n.k0s()}if(2&i){const t=n.XpG();n.FS9("disabled","INVALID"===(null==t.otpForm?null:t.otpForm.status)),n.R7$(1),n.SpI(" ",n.bMT(2,3,"signin-page.modal-otp-description.button-login")," "),n.R7$(2),n.Y8G("ngIf",t.isLoading)}}const z=[{path:"",component:(()=>{class i{constructor(t,e,r,d,v,W){this.router=t,this.modalCtrl=e,this.storageSrv=r,this.commonSrv=d,this.companySrv=v,this.authService=W,this.tabOption="phone",this.loginForm=new a.gE({email:new a.MJ("",[a.k0.email,a.k0.min(3)]),phone:new a.MJ("",[a.k0.min(8)]),password:new a.MJ("",[a.k0.min(3),a.k0.required])}),this.loginOtpForm=new a.gE({phone:new a.MJ("",[a.k0.required,a.k0.minLength(12)])}),this.otpForm=new a.gE({code:new a.MJ("",[a.k0.required])}),this.isLoading=!1,this.isOtpGenerate=!1}ngOnInit(){this.storageSrv.getUserConnected()&&this.commonSrv?.user?.accessToken&&this.commonSrv.user.roles.includes("client")&&this.router.navigateByUrl("navigation"),console.log("otpForm",this.isOtpGenerate)}ionViewWillEnter(){this.loginForm.reset(),this.checkValidator()}showPassword(){this.typeOfPassword=!this.typeOfPassword}checkValidator(){this.loginForm.get("phone").setValidators("phone"===this.tabOption?[a.k0.min(8),a.k0.required]:[]),this.loginForm.get("phone").updateValueAndValidity(),this.loginForm.get("email").setValidators("email"===this.tabOption?[a.k0.email,a.k0.min(3),a.k0.required]:[]),this.loginForm.get("email").updateValueAndValidity()}changeTab(t){this.isLoading||(this.loginForm.reset(),this.tabOption=t,this.checkValidator())}navigateTo(t){this.router.navigateByUrl(t)}login(){var t=this;return(0,_.A)(function*(){if(t.isLoading=!0,"email"===t.tabOption){t.credential={email:t.loginForm.value.email,password:t.loginForm.value.password};let e=yield t.authService.login(t.credential);e instanceof Error||!e.accessToken||!e.roles.includes("client")||(t.storageSrv.load("ONB_INFO")?(t.commonSrv.anboardingView=!1,t.router.navigateByUrl("navigation")):(t.storageSrv.store("ONB_INFO",JSON.stringify({value:!0})),t.router.navigate(["navigation/home/<USER>"])),t.loginForm.reset())}if("phone"===t.tabOption){const e={emailOrTel:t.loginOtpForm.value.phone.replace(/\D+/g,"")};(yield t.authService.generateOtp(e))instanceof Error||(t.isOtpGenerate=!0)}t.isLoading=!1})()}showModalOtp(t){var e=this;return(0,_.A)(function*(){const r=yield e.modalCtrl.create({component:l,initialBreakpoint:.6,cssClass:"modal",breakpoints:[0,.6,.7,.8,.85],mode:"ios",componentProps:{credentialOtp:t??null}});r.present(),yield r.onWillDismiss()})()}openModal(){var t=this;return(0,_.A)(function*(){const e=yield t.modalCtrl.create({component:k,initialBreakpoint:.5,cssClass:"modal",breakpoints:[0,.5],mode:"ios"});e.present(),yield e.onWillDismiss()})()}changeLanguage(){var t=this;return(0,_.A)(function*(){const e=yield t.modalCtrl.create({component:w.S,cssClass:"modalClass",componentProps:{}});yield e.present(),yield e.onDidDismiss()})()}verifyTel(t){return t=`${t}`.replace(/\s/g,"").replace(/-/g,""),!!/^6[0-9]{8}$/.test(t)}verifyOtp(){var t=this;return(0,_.A)(function*(){t.isLoading=!0,t.code={value:Number(t.otpForm.get("code").value)};const e=yield t.authService.loginWhitOtp(t.code);if(e?.category===o.s.CompanyUser){const{data:r}=yield t.companySrv.getCompanies({users:e?._id});r?.length>0&&(r?.push(e.company),e.associatedCompanies=r)}if(t.storageSrv.store("USER_INFO",JSON.stringify(e)),!(e instanceof Error)){if(t.loginForm.reset(),t.otpForm.reset(),t.isOtpGenerate=!1,t.isLoading=!1,e?.category===o.s.Particular||e?.category===o.s.DonutAnimator)return t.router.navigateByUrl("navigation/home-alt");if(t.storageSrv.load("ONB_INFO"))t.router.navigateByUrl("navigation/home");else{const d={value:!0};t.commonSrv.anboardingView=!1,t.storageSrv.store("ONB_INFO",JSON.stringify(d)),t.router.navigate(["navigation/home/<USER>"])}t.loginForm.reset(),t.otpForm.reset(),t.isOtpGenerate=!1}t.isLoading=!1})()}goBackToPhoneInput(){var t=this;return(0,_.A)(function*(){t.isOtpGenerate=!1,t.otpForm.reset(),t.loginOtpForm.get("phone").reset()})()}resendCode(){var t=this;return(0,_.A)(function*(){t.isLoading=!0;try{const e={emailOrTel:t.loginOtpForm.get("phone").value.replace(/\D+/g,"")};(yield t.authService.generateOtp(e))instanceof Error?t.commonSrv.showToast({color:"danger",icon:"",message:"Erreur lors du renvoi de l'OTP"}):t.commonSrv.showToast({color:"success",icon:"",message:"Otp renvoy\xe9 avec succes"})}catch(e){console.error("Erreur lors du renvoi de l'OTP:",e),t.commonSrv.showToast({color:"danger",icon:"",message:"erreur"})}finally{t.isLoading=!1}})()}static{this.\u0275fac=function(e){return new(e||i)(n.rXU(u.Ix),n.rXU(c.W3),n.rXU(T.n),n.rXU(F.h),n.rXU(I.B),n.rXU(x.k))}}static{this.\u0275cmp=n.VBU({type:i,selectors:[["app-form-signin"]],inputs:{credentialOtp:"credentialOtp"},decls:8,vars:7,consts:[[1,"scroll",3,"scrollEvents","fullscreen","scrollY"],["id","container",3,"formGroup",4,"ngIf"],[1,"btn","btn-primary"],["expand","block",3,"disabled","click",4,"ngIf"],["id","container",3,"formGroup"],[1,"login"],[1,"ion-text-center","message","login-message"],[1,"ion-padding-vertical"],["src","../assets/logos/Password.svg",1,"password-key"],[1,"text-message"],[1,"phone-input-container",3,"hidden"],[1,"phone-input"],["type","tel","appPhoneFormat","","placeholder","650 00 00 00","formControlName","phone","clearInput","",1,"custom-input",3,"readonly"],[1,"change-language",3,"click"],[1,"password-key",3,"src"],["type","number","placeholder","1 2 3 4","formControlName","phone","clearInput","","formControlName","code","clearInput","","maxlength","7",1,"custom-input",3,"readonly"],[1,"otp-actions"],[1,"no-otp"],[1,"resend-otp",3,"click"],[1,"bloc-elt"],[1,"change-phone"],[1,"change-phone-action",3,"click"],["expand","block",3,"disabled","click"],["name","bubbles",4,"ngIf"],["name","bubbles"]],template:function(e,r){1&e&&(n.nrm(0,"app-header-connect"),n.j41(1,"ion-content",0),n.DNE(2,E,18,15,"div",1),n.DNE(3,G,25,20,"div",1),n.k0s(),n.j41(4,"ion-footer")(5,"div",2),n.DNE(6,B,4,5,"ion-button",3),n.DNE(7,N,4,5,"ion-button",3),n.k0s()()),2&e&&(n.R7$(1),n.Y8G("scrollEvents",!0)("fullscreen",!0)("scrollY",!0),n.R7$(1),n.Y8G("ngIf",!r.isOtpGenerate),n.R7$(1),n.Y8G("ngIf",r.isOtpGenerate),n.R7$(3),n.Y8G("ngIf",!r.isOtpGenerate),n.R7$(1),n.Y8G("ngIf",r.isOtpGenerate))},dependencies:[C.bT,a.me,a.Q0,a.BC,a.cb,a.tU,c.Jm,c.W9,c.M0,c.KW,c.w2,c.IO,a.j4,a.JD,$.v,S.f,O.D9],styles:['*[_ngcontent-%COMP%]{margin:0;padding:0}.header-md[_ngcontent-%COMP%]:after{background-image:none!important}.modal[_ngcontent-%COMP%]{--border-radius: calc(60 * var(--res))}.input-group[_ngcontent-%COMP%]{margin-top:calc(75 * var(--res))}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{--padding-start: 0;margin-bottom:calc(50 * var(--res));--placeholder-color: $color-six}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-input[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{font-family:Mont Regular;font-weight:400;font-size:calc(42 * var(--res));color:#1e1e1e}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{padding-top:calc(25 * var(--res));padding-bottom:calc(25 * var(--res))}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]{margin-bottom:calc(50 * var(--res));display:flex;align-items:center;justify-content:space-between;border-bottom-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-250, #c8c7cc)));border-bottom-width:.55px;border-bottom-style:solid}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{width:90%;margin-bottom:0;--border-color: transparent;--inner-border-width: 0px 0px 0px 0px;--border-style: none}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-checkbox[_ngcontent-%COMP%]{margin-top:5%}.bg-primary[_ngcontent-%COMP%]{color:#143c5d}.message[_ngcontent-%COMP%]{margin-bottom:calc(75 * var(--res))}.message[_ngcontent-%COMP%]   ion-title[_ngcontent-%COMP%]{font-weight:800;font-size:calc(70 * var(--res));padding-bottom:calc(25 * var(--res))}.message[_ngcontent-%COMP%]   ion-text[_ngcontent-%COMP%]{font-weight:400;color:#757474;display:block;line-height:1.5em!important}.btn--meduim[_ngcontent-%COMP%]{height:calc(130 * var(--res));font-size:calc(50 * var(--res));font-family:var(--mont-bold)}.btn--meduim[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:var(--mont-bold)}.btn--small[_ngcontent-%COMP%]{height:calc(100 * var(--res))}.btn--round[_ngcontent-%COMP%]{--border-radius: 50%;padding-inline-start:0;padding-inline-end:0;--padding-start: 0;--padding-end: 0}.btn--border-primary[_ngcontent-%COMP%]{--background: transparent;--background-activated: #c4e6d0;--border-color: var(--ion-color-primary);--border-style: solid;--border-width: 2px}.btn--border-secondary[_ngcontent-%COMP%]{--border-width: 2px;--background: transparent;--background-activated: #ddabb2;--border-color: var(--ion-color-secondary);--border-style: solid}.btn--upper[_ngcontent-%COMP%]{text-transform:uppercase}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]{overflow:hidden}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar{width:5px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-track{box-shadow:inset 0 0 5px #546e8d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background:#143c5d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover{background:#143c5d}.bottom-sheet-content[_ngcontent-%COMP%]{background:#fdfdfe;height:100%}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-thumbnail[_ngcontent-%COMP%]{margin-left:calc(31.25 * var(--res));background-color:#1e1e1e;--border-radius: 50%;--size: 23}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:Mont Bold;font-size:calc(45 * var(--res));text-align:center;color:#757474}.bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%], .bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]{padding:calc(75 * var(--res))}.font-size-5[_ngcontent-%COMP%]{font-size:calc(42 * var(--res))}ion-content.scroll[_ngcontent-%COMP%]{--overflow: hidden}#container[_ngcontent-%COMP%]{height:100%;display:flex;flex-direction:column;align-items:center;max-width:var(--content-max-width);padding:0 calc(41 * var(--res));margin:0 auto}#container[_ngcontent-%COMP%]   .login[_ngcontent-%COMP%]{margin-top:4vh;margin-bottom:4vh;font-family:Mont Bold;font-size:clamp(28px,5vw,38px);color:var(--clr-primary-800);font-weight:var(---mont-bold);text-align:center;font-size:1.5rem;font-style:normal;font-weight:800;line-height:normal;letter-spacing:-.01031rem}#container[_ngcontent-%COMP%]   .message.login-message[_ngcontent-%COMP%]{width:100%;display:flex;flex-direction:column;align-items:center;text-align:center}#container[_ngcontent-%COMP%]   .message.login-message[_ngcontent-%COMP%]   ion-text[_ngcontent-%COMP%]{display:block;margin-bottom:3vh;font-size:12px;color:var(--clr-primary-300);line-height:1;text-align:center;font-size:.75rem;font-style:normal;font-weight:600;line-height:normal;letter-spacing:-.01031rem}#container[_ngcontent-%COMP%]   .message.login-message[_ngcontent-%COMP%]   ion-text[_ngcontent-%COMP%]:last-of-type{color:var(#0B305C);margin-bottom:3vh;font-size:clamp(16px,3.5vw,20px)}#container[_ngcontent-%COMP%]   .message.login-message[_ngcontent-%COMP%]   ion-text.text-message[_ngcontent-%COMP%]{color:var(--clr-primary-700);margin-bottom:4vh;font-size:var(--fs-14-px);font-weight:var(---mont-bold);font-family:Mont SemiBold!important;text-align:center;font-size:.875rem;font-style:normal;line-height:normal;letter-spacing:-.01031rem}#container[_ngcontent-%COMP%]   .message.login-message[_ngcontent-%COMP%]   .password-key[_ngcontent-%COMP%]{margin-bottom:4vh;width:60px;height:60px}#container[_ngcontent-%COMP%]   .message.login-message[_ngcontent-%COMP%]   .key-otp-bloc[_ngcontent-%COMP%]{margin-bottom:4vh}#container[_ngcontent-%COMP%]   .phone-input-container[_ngcontent-%COMP%]{width:100%}#container[_ngcontent-%COMP%]   .phone-input-container[_ngcontent-%COMP%]   .phone-input[_ngcontent-%COMP%]{margin-left:auto;margin-right:auto}#container[_ngcontent-%COMP%]   .phone-input-container[_ngcontent-%COMP%]   .phone-input[_ngcontent-%COMP%]   .custom-input[_ngcontent-%COMP%]{border-radius:.375rem;border:1px solid var(--bleu-main-color-n-140-white, #6D839D);background:transparent;text-align:center;color:#0b305c;font-family:var(---mont-regular);font-size:var(--fs-18-px);width:100%;padding:calc(26 * var(--res));letter-spacing:.12em}#container[_ngcontent-%COMP%]   .phone-input-container[_ngcontent-%COMP%]   .phone-input[_ngcontent-%COMP%]   .custom-input[_ngcontent-%COMP%]::part(native){padding:12px}#container[_ngcontent-%COMP%]   .phone-input-container[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]::placeholder{display:flex;align-items:center;justify-content:center;color:#cfcfcf;text-align:center;font-family:var(---mont-regular);font-size:var(--fs-18-px);letter-spacing:-.01031rem}#container[_ngcontent-%COMP%]   .change-language[_ngcontent-%COMP%]{text-align:center;margin-top:4vh;font-family:Mont Regular,"sans serif";font-size:clamp(12px,2vw,14px);text-decoration:underline;color:var(--clr-secondary-400)}#container[_ngcontent-%COMP%]   .otp-actions[_ngcontent-%COMP%]{text-align:center;margin-top:4vh}#container[_ngcontent-%COMP%]   .otp-actions[_ngcontent-%COMP%]   .no-otp[_ngcontent-%COMP%]{font-family:Mont Regular,"sans serif";font-size:clamp(12px,2vw,14px);color:var(--clr-primary-300);margin-top:-1vh}#container[_ngcontent-%COMP%]   .otp-actions[_ngcontent-%COMP%]   .no-otp[_ngcontent-%COMP%]   .resend-otp[_ngcontent-%COMP%]{text-decoration:underline}#container[_ngcontent-%COMP%]   .otp-actions[_ngcontent-%COMP%]   .no-otp[_ngcontent-%COMP%]   .resend-otp[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]{color:var(--clr-secondary-300)}#container[_ngcontent-%COMP%]   .otp-actions[_ngcontent-%COMP%]   .bloc-elt[_ngcontent-%COMP%]{margin-top:4vh}#container[_ngcontent-%COMP%]   .otp-actions[_ngcontent-%COMP%]   .bloc-elt[_ngcontent-%COMP%]   .change-phone[_ngcontent-%COMP%]   .change-phone-action[_ngcontent-%COMP%]{color:var(--clr-secondary-400);color:var(--clr-primary-700);font-family:Mont Regular,"sans serif";font-size:clamp(12px,2vw,14px);text-decoration:underline;font-family:Mont SemiBold;font-weight:700;cursor:pointer}ion-item[_ngcontent-%COMP%]   div.item-native[_ngcontent-%COMP%]{border:none!important}.footer-md[_ngcontent-%COMP%]:before{background:none!important}ion-footer[_ngcontent-%COMP%]{border:none;height:7rem;display:flex;justify-content:center;align-items:center}ion-footer[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]{padding:calc(41 * var(--res))}@media (min-width: 1024px){[_ngcontent-%COMP%]:root{--container-padding: 10vw;--content-max-width: 800px}#container[_ngcontent-%COMP%]{padding:var(--container-padding);max-width:var(--content-max-width)}#container[_ngcontent-%COMP%]   .login[_ngcontent-%COMP%]{font-size:clamp(28px,4vw,36px);margin-top:6vh;margin-bottom:4vh}#container[_ngcontent-%COMP%]   .message.login-message[_ngcontent-%COMP%]{padding-left:20px;padding-right:20px}#container[_ngcontent-%COMP%]   .message.login-message[_ngcontent-%COMP%]   ion-text[_ngcontent-%COMP%]{font-size:clamp(16px,2.5vw,20px);margin-bottom:3vh;padding:0 1.8rem}#container[_ngcontent-%COMP%]   .message.login-message[_ngcontent-%COMP%]   ion-text[_ngcontent-%COMP%]:last-of-type{font-size:clamp(18px,3vw,22px);margin-top:3vh}#container[_ngcontent-%COMP%]   .message.login-message[_ngcontent-%COMP%]   .password-key[_ngcontent-%COMP%]{width:80px;height:80px;margin-top:3vh}#container[_ngcontent-%COMP%]   .phone-input[_ngcontent-%COMP%]{width:100%;max-width:500px;height:54px;margin-bottom:4vh}#container[_ngcontent-%COMP%]   .phone-input[_ngcontent-%COMP%]   .custom-input[_ngcontent-%COMP%]{font-size:clamp(16px,2.5vw,20px)}#container[_ngcontent-%COMP%]   .phone-input[_ngcontent-%COMP%]   .custom-input[_ngcontent-%COMP%]::part(native){padding:16px}ion-footer[_ngcontent-%COMP%]{height:25rem;border:none}ion-footer[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]{max-width:500px;height:54px}ion-footer[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%]{--padding-top: 18px;--padding-bottom: 18px}}@media (min-width: 768px){[_ngcontent-%COMP%]:root{--container-padding: 8vw}#container[_ngcontent-%COMP%]{justify-content:center}#container[_ngcontent-%COMP%]   .login[_ngcontent-%COMP%]{margin-top:0}ion-footer[_ngcontent-%COMP%]{height:15rem;border:none}ion-footer[_ngcontent-%COMP%]   .phone-input[_ngcontent-%COMP%], ion-footer[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]{max-width:600px;height:42px}}@media (min-width: 1024px){[_ngcontent-%COMP%]:root{--container-padding: 10vw;--content-max-width: 800px}#container[_ngcontent-%COMP%]{padding:var(--container-padding);max-width:var(--content-max-width)}#container[_ngcontent-%COMP%]   .login[_ngcontent-%COMP%]{font-size:clamp(28px,4vw,36px);margin-top:6vh;margin-bottom:4vh}#container[_ngcontent-%COMP%]   .message.login-message[_ngcontent-%COMP%]{padding-left:20px;padding-right:20px}#container[_ngcontent-%COMP%]   .message.login-message[_ngcontent-%COMP%]   ion-text[_ngcontent-%COMP%]{font-size:clamp(16px,2.5vw,20px);margin-bottom:3vh}#container[_ngcontent-%COMP%]   .message.login-message[_ngcontent-%COMP%]   ion-text[_ngcontent-%COMP%]:last-of-type{font-size:clamp(18px,3vw,22px);margin-top:3vh}#container[_ngcontent-%COMP%]   .message.login-message[_ngcontent-%COMP%]   .password-key[_ngcontent-%COMP%]{width:80px;height:80px;margin-top:3vh;margin-bottom:3vh}#container[_ngcontent-%COMP%]   .phone-input[_ngcontent-%COMP%]{width:100%;max-width:500px;height:54px;margin-bottom:4vh}#container[_ngcontent-%COMP%]   .phone-input[_ngcontent-%COMP%]   .custom-input[_ngcontent-%COMP%]{font-size:clamp(16px,2.5vw,20px)}#container[_ngcontent-%COMP%]   .phone-input[_ngcontent-%COMP%]   .custom-input[_ngcontent-%COMP%]::part(native){padding:16px}ion-footer[_ngcontent-%COMP%]{height:25rem;border:none}ion-footer[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]{max-width:500px;height:54px}}']})}}return i})()}];let V=(()=>{class i{static{this.\u0275fac=function(e){return new(e||i)}}static{this.\u0275mod=n.$C({type:i})}static{this.\u0275inj=n.G2t({imports:[u.iI.forChild(z),u.iI]})}}return i})();var Y=s(93887);let X=(()=>{class i{static{this.\u0275fac=function(e){return new(e||i)}}static{this.\u0275mod=n.$C({type:i})}static{this.\u0275inj=n.G2t({imports:[C.MD,a.YN,c.bv,V,a.X1,Y.G,O.h]})}}return i})()},43556:(L,y,s)=>{s.d(y,{B:()=>j});var O=s(73308),u=s(94934),_=s(45312),a=s(26409),n=(s(99987),s(2978)),c=s(33607),x=s(82571),R=s(14599),C=s(74657);let j=(()=>{class P{constructor(p,l,o,g,m){this.baseUrl=p,this.http=l,this.commonSrv=o,this.storageSrv=g,this.translateService=m,this.base_url=`${this.baseUrl.getOrigin()}${_.c.basePath}`,this.base_url+="companies"}create(p){var l=this;return(0,O.A)(function*(){try{return delete p._id,yield(0,u.s)(l.http.post(l.base_url,p))}catch(o){return l.commonSrv.getError("Echec de cr\xe9ation de la compagnie",o)}})()}getCompanies(p){var l=this;return(0,O.A)(function*(){try{let o=new a.Nl;const{category:g,city:m,limit:M,name:b,regionCom:k,solToId:w,tel:T,users:F,offset:I,enable:$=!0,projection:S,isLoyaltyProgDistributor:E}=p;return void 0!==g&&(o=o.append("category",g)),m&&(o=o.append("address.city",m)),b&&(o=o.append("name",b)),w&&(o=o.append("erpSoldToId",w)),T&&(o=o.append("tel",`${T}`)),S&&(o=o.append("projection",`${S}`)),F&&(o=o.append("users",`${F}`)),k&&(o=o.append("address.commercialRegion",k)),E&&(o=o.append("isLoyaltyProgDistributor",E)),void 0!==M&&(o=o.append("limit",M)),void 0!==I&&(o=o.append("offset",I)),o=o.set("enable",$),yield(0,u.s)(l.http.get(l.base_url,{params:o}))}catch(o){const m={message:l.commonSrv.getError("",o).message,color:"danger"};return yield l.commonSrv.showToast(m),o}})()}getParticularCompanies(p){var l=this;return(0,O.A)(function*(){let o=new a.Nl;const{limit:g,offset:m,enable:M=!0,commercialRegion:b}=p;return void 0!==g&&(o=o.append("limit",g)),void 0!==m&&(o=o.append("offset",m)),b&&(o=o.append("address.commercialRegion",b)),o=o.set("enable",M),yield(0,u.s)(l.http.get(l.base_url+"/particular-suppliers",{params:o}))})()}find(p){var l=this;return(0,O.A)(function*(){try{return yield(0,u.s)(l.http.get(l.base_url+"/"+p))}catch{return l.commonSrv.initCompany()}})()}getBalance(p){var l=this;return(0,O.A)(function*(){try{let o=new a.Nl;const{company:g}=l.storageSrv.getUserConnected();return o=o.set("_id",g?g?._id:p?.companyId),yield(0,u.s)(l.http.get(`${l.base_url}/balance`,{params:o}))}catch(o){return yield l.commonSrv.showToast({message:"Une erreur est survenue lors de la r\xe9cup\xe9ration de votre solde",color:"danger"}),o}})()}getUsersCompany(p,l){var o=this;return(0,O.A)(function*(){try{let g=new a.Nl;const{email:m,enable:M=!0}=l;return m&&(g=g.append("email",m)),g=g.append("enable",M),yield(0,u.s)(o.http.get(`${o.base_url}/${p}/users`,{params:g}))}catch(g){return yield o.commonSrv.showToast({message:"Une erreur est survenue lors de la r\xe9cup\xe9ration de vos informations",color:"danger"}),g}})()}static{this.\u0275fac=function(l){return new(l||P)(n.KVO(c.K),n.KVO(a.Qq),n.KVO(x.h),n.KVO(R.n),n.KVO(C.c$))}}static{this.\u0275prov=n.jDH({token:P,factory:P.\u0275fac,providedIn:"root"})}}return P})()}}]);