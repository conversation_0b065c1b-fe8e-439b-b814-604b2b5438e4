import { StorageService } from './storage.service';
import { BaseUrlService } from './base-url.service';
import {
  CodeOtp,
  CredentialDto,
  CredentialOtpDto as CredentialOtp,
} from './../models/credential-dto';
import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { QueryResult } from '../models/query-result';
import { environment } from 'src/environments/environment';
import { BehaviorSubject, firstValueFrom, from, lastValueFrom, Observable, switchMap, throwError } from 'rxjs';
import { CommonService } from './common.service';
import { Platform, ToastController } from '@ionic/angular';
import { ToastModel } from '../models/toast.model';
import { Router } from '@angular/router';
import { AuthUser, BaseUser } from '../models/user.models';
import { TranslateConfigService } from './translate-config.service';
import { OtpAutoFillService } from './otp-auto-fill.service';

@Injectable({
  providedIn: 'root',
})
export class AuthenticationService {
  authState = new BehaviorSubject(false);
  url: string = '';
  private currentOtpCredential: CredentialOtp | null = null;

  constructor(
    private router: Router,
    private http: HttpClient,
    private platform: Platform,
    private commonSrv: CommonService,
    private baseUrlService: BaseUrlService,
    public toastController: ToastController,
    private storageService: StorageService,
    private translateSrv: TranslateConfigService,
    private otpAutoFillService: OtpAutoFillService
  ) {
    this.url = this.baseUrlService.getOrigin() + environment.basePath + 'auth/';
    this.platform.ready().then(() => {
      this.checkUser();
    });
  }

  checkUser() {
    let response = this.storageService.load('USER_INFO');
    if (response) {
      this.authState.next(true);
    }
  }

  async login(credential: CredentialDto): Promise<AuthUser | any> {
    let toast: ToastModel;

    try {
      let response = await lastValueFrom(
        this.http.post<AuthUser>(this.url + 'login', credential));

      if (response.roles.includes('client')) {
        this.commonSrv.showToast({
          color: 'success',
          icon: 'checkmark-circle-outline',
          message: 'Connexion réussie',
        });
        this.storageService.store('USER_INFO', JSON.stringify(response));
        this.authState.next(true);
      } else {
        this.commonSrv.showToast({
          color: 'danger',
          icon: '',
          message: 'Seuls les clients sont admis à la plateforme',
        });
      }

      return response;
    } catch (error) {
      toast = {
        color: 'danger',
        icon: 'close',
        message: error.error.message || "Une erreur s'est produite",
      };
      await this.commonSrv.showToast(toast);

      return error;
    }
  }

  async loginWhitOtp(credential: CodeOtp): Promise<BaseUser | Error> {

    try {
      let response = await lastValueFrom(this.http.post<BaseUser>(this.url + 'verify-otp', credential));
      if (!(response.roles.includes('client'))) {
        this.commonSrv.showToast({
          color: 'warning',
          icon: '',
          message: 'Seuls les clients sont admis à la plateforme',
        });
        return new Error();
      }
      this.storageService.store('USER_INFO', JSON.stringify(response));
      this.authState.next(true);
      this.commonSrv.showToast({
        color: 'success',
        icon: 'checkmark-circle-outline',
        message: 'Connexion réussie',
      });
      return response;
    } catch (error) {
      const errorMessage = this.commonSrv.getError('', error)
      const toastMessage: ToastModel = {
        message: errorMessage.message,
        color: 'danger',
      }
      await this.commonSrv.showToast(toastMessage);

      return new Error(error);
    }
  }

  async generateOtp(credential: CredentialOtp): Promise<BaseUser | Error> {
    try {
      // Stocker les credentials pour l'auto-fill
      this.currentOtpCredential = credential;

      const user = await lastValueFrom(
        this.http.post<BaseUser>(this.url + 'loginOtp', credential)
      );
      if (!(user.roles.includes('client'))) {
        this.commonSrv.showToast({
          color: 'warning',
          icon: '',
          message: 'Seuls les clients sont admis à la plateforme',
        });
        return new Error();
      }
      this.commonSrv.showToast({
        color: 'success',
        icon: 'checkmark-circle-outline',
        message: 'Votre code otp à été genéré',
      });
      return user;
    } catch (error: any) {
      const errorMessage = this.commonSrv.getError('', error)
      const toastMessage: ToastModel = {
        message: errorMessage.message,
        color: 'danger',
      }
      await this.commonSrv.showToast(toastMessage);
      return new Error(error);
    }
  }

  /**
   * Démarre l'auto-fill OTP
   */
  startOtpAutoFill(callback: (otp: string) => void): void {
    if (this.otpAutoFillService.isSupported()) {
      console.log('[AUTH] Démarrage auto-fill OTP...');
      this.otpAutoFillService.startListening(callback);
    } else {
      console.warn('[AUTH] Auto-fill OTP non supporté sur cette plateforme');
    }
  }

  /**
   * Arrête l'auto-fill OTP
   */
  stopOtpAutoFill(): void {
    this.otpAutoFillService.stopListening();
  }

  /**
   * Récupère les credentials OTP actuels
   */
  getCurrentOtpCredential(): CredentialOtp | null {
    return this.currentOtpCredential;
  }

  /**
   * Nettoie les credentials OTP
   */
  clearOtpCredential(): void {
    this.currentOtpCredential = null;
  }

  async signup(user: BaseUser): Promise<QueryResult> {
    try {
      if (user?.tel) {
        user.tel = +user.tel as any;
      }
      let response = await lastValueFrom(this.http.post<QueryResult>(this.url + 'register', user));

      this.commonSrv.showToast({
        color: 'success',
        message: 'Compte crée avec success',
      });
      return response;
    } catch (error) {
      const errorMessage = this.commonSrv.getError('', error)
      const toastMessage: ToastModel = {
        message: errorMessage.message,
        color: 'danger',
      }
      await this.commonSrv.showToast(toastMessage);

      return error;
    }
  }

  async signupParticular(user: BaseUser): Promise<QueryResult> {
    try {
      if (user?.tel) {
        user.tel = +user.tel as any;
      }
      let response = await lastValueFrom(this.http.post<QueryResult>(this.url + 'register-particular', user));

      this.commonSrv.showToast({
        color: 'success',
        message: 'Compte crée avec success',
      });
      return response;
    } catch (error) {
      const errorMessage = this.commonSrv.getError('', error)
      const toastMessage: ToastModel = {
        message: errorMessage.message,
        color: 'danger',
      }
      await this.commonSrv.showToast(toastMessage);

      return error;
    }
  }
  async resetPassword(email: String): Promise<unknown> {
    let toast: ToastModel;

    try {
      toast = {
        color: 'success',
        icon: 'checkmark-circle-outline',
        message: 'Email envoyé',
      };

      let response = await lastValueFrom(
        this.http.post<unknown>(this.url + 'reset-password', { email })
      );
      this.commonSrv.showToast(toast);

      return response;
    } catch (error) {
      toast = {
        color: 'danger',
        icon: 'close',
        message: error.error.message,
      };
      await this.commonSrv.showToast(toast);

      return error;
    }
  }

  logout() {
    this.storageService.clear();
    this.router.navigateByUrl('/authentication/signin');
    this.authState.next(false);
  }

  // async refreshToken(): Promise<{ newToken: string }> {
  //   try {
  //     const { accessToken } = this.storageService.getUserConnected();
  //     return await lastValueFrom(
  //       this.http.post<{ newToken: string }>(this.url + 'refresh-token', {
  //         currentToken: accessToken,
  //       })
  //     );
  //   } catch (error) {
  //     return error;ks
  //   }
  // }

  refreshToken(): Observable<{ newToken: string }> {
    const user = this.storageService.getUserConnected();
    const accessToken = user?.accessToken;
    const _id = user?._id;

    if (!accessToken || !_id) {
      this.router.navigate(['authentication']);
      return from(this.translateSrv.translate('reconnection')).pipe(
        switchMap((message) => {
          const toastMessage: ToastModel = {
            message,
            color: 'danger',
          };
          this.storageService.clear();
          this.commonSrv.showToast(toastMessage);
          return throwError(() => new Error('User not authenticated'));
        })
      );
    }

    return this.http.post<{ newToken: string }>(`${this.url}refresh-token`, { token: accessToken });
  }


  isAuthenticated() {
    return this.authState.value;
  }
}
