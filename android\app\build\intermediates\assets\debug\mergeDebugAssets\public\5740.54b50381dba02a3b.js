"use strict";(self.webpackChunkapp=self.webpackChunkapp||[]).push([[5740],{10393:(m,e,t)=>{t.d(e,{K:()=>d});var r=t(97270),i=t(84592),s=t(2978),d=function(a){function n(){return null!==a&&a.apply(this,arguments)||this}return(0,r.C6)(n,a),n.prototype.startWatching=function(){return(0,i.bk)(this,"startWatching",{},arguments)},n.prototype.getAppHash=function(){return(0,i.bk)(this,"getAppHash",{},arguments)},n.\u0275fac=function(){let l;return function(o){return(l||(l=s.xGo(n)))(o||n)}}(),n.\u0275prov=s.jDH({token:n,factory:n.\u0275fac}),n.pluginName="SmsRetriever",n.plugin="cordova-plugin-sms-retriever-manager",n.pluginRef="cordova.plugins.smsRetriever",n.repo="https://github.com/hanatharesh2712/ionic-native-sms-retriever-plugin-master",n.install='ionic cordova plugin add cordova-plugin-sms-retriever-manager --variable PLAY_SERVICES_VERSION="15.0.1"',n.installVariables=["PLAY_SERVICES_VERSION"],n.platforms=["Android"],n=(0,r.Cg)([],n)}(i.UF)},45740:(m,e,t)=>{t.r(e),t.d(e,{MenuAuthenticationModule:()=>u});var r=t(56610),i=t(77575),s=t(2978);const d=[{path:"",redirectTo:"signin",pathMatch:"full"},{path:"signin",loadChildren:()=>Promise.all([t.e(2076),t.e(7293)]).then(t.bind(t,27293)).then(o=>o.FormSigninPageModule)},{path:"reset-password",loadChildren:()=>Promise.all([t.e(2076),t.e(8131)]).then(t.bind(t,88131)).then(o=>o.FormResetPswrdPageModule)},{path:"signup",loadChildren:()=>Promise.all([t.e(9963),t.e(2076),t.e(8686)]).then(t.bind(t,78686)).then(o=>o.FormSignupAccountPageModule)}];let a=(()=>{class o{static{this.\u0275fac=function(h){return new(h||o)}}static{this.\u0275mod=s.$C({type:o})}static{this.\u0275inj=s.G2t({imports:[i.iI.forChild(d),i.iI]})}}return o})();var n=t(93887),l=t(10393);let u=(()=>{class o{static{this.\u0275fac=function(h){return new(h||o)}}static{this.\u0275mod=s.$C({type:o})}static{this.\u0275inj=s.G2t({providers:[l.K],imports:[r.MD,a,n.G]})}}return o})()}}]);