"use strict";(self.webpackChunkapp=self.webpackChunkapp||[]).push([[5571],{65571:(k,v,a)=>{a.r(v),a.d(v,{ProductDetailPageModule:()=>$});var g=a(56610),p=a(37222),s=a(77897),m=a(77575),_=a(73308),n=a(2978),b=a(39316),P=a(99987),T=a(81559),f=a(26409),R=a(28863),O=a(14599),u=a(82571),C=a(13217),r=a(62049),e=a(71333),t=a(74657);function i(h,B){1&h&&n.nrm(0,"app-progress-spinner")}let c=(()=>{class h{constructor(o,l,d,x,U,L){this.modalCtrl=o,this.marketPlaceService=l,this.storageSrv=d,this.commonSrv=x,this.fidelityService=U,this.translateService=L,this.isPointValidate=!0,this.orderSrv=(0,n.WQX)(T.Q)}ngOnInit(){var o=this;return(0,_.A)(function*(){o.marketPlaceOrderForm=new p.gE({quantity:new p.MJ("",[p.k0.required,p.k0.pattern("^[0-9]{9}$")])}),yield o.getPoints()})()}getPoints(){var o=this;return(0,_.A)(function*(){o.user=o.storageSrv.getUserConnected();let l={};"company"in o.user&&(l.companyId=o.user?.company?._id);const d=yield o.fidelityService.getPoints(l);o.points=d?.points})()}doPayment(){var o=this;return(0,_.A)(function*(){if(!o.marketPlaceOrderForm.value.quantity||o.marketPlaceOrderForm.value.quantity*o.item?.price>o.points?.validate)return o.commonSrv.showToast({color:"warning",message:o.translateService.currentLang===P.T.French?"Vous n'avez pas assez de points pour acheter cette quantit\xe9\xa0d'articles.":"You do not have enough points to purchase this quantity of items."});o.isLoading=!0;const l={cart:{items:{...o.item},quantity:Number(o.marketPlaceOrderForm.value.quantity),amount:{HT:o.item?.price,TTC:o.item?.price,VAT:o.item?.price}},user:o.user};o.orderSrv.response=yield o.marketPlaceService.create(l),o.isLoading=!1,o.orderSrv.response instanceof f.yz?o.commonSrv.showToast({color:"danger",message:o.orderSrv.response.message}):(o.commonSrv.showToast({color:"success",message:o.translateService.currentLang===P.T.French?"La commande a \xe9t\xe9 cr\xe9\xe9e avec succes.":"Order created successfully."}),o.modalCtrl.dismiss())})()}verifyPoints(o){}closeModal(){this.modalCtrl.dismiss()}static{this.\u0275fac=function(l){return new(l||h)(n.rXU(s.W3),n.rXU(R.j),n.rXU(O.n),n.rXU(u.h),n.rXU(C._),n.rXU(r.E))}}static{this.\u0275cmp=n.VBU({type:h,selectors:[["app-bottom-sheet"]],inputs:{item:"item"},decls:22,vars:12,consts:[[4,"ngIf"],[1,"ion-text-center","ion-padding"],["slot","end",3,"click"],["src","assets/icons/close-btn.svg",3,"click"],["color","primary",1,"title"],[1,"ion-padding"],[1,"content-wrapper",3,"formGroup"],[1,"input-group"],["position","floating"],["formControlName","quantity","clearInput","","required","quantity","placeholder","0",3,"ionChange"],["expand","block",3,"click"]],template:function(l,d){1&l&&(n.DNE(0,i,1,0,"app-progress-spinner",0),n.j41(1,"ion-header")(2,"ion-toolbar",1)(3,"ion-thumbnail",2),n.bIt("click",function(){return d.closeModal()}),n.j41(4,"ion-img",3),n.bIt("click",function(){return d.closeModal()}),n.k0s()(),n.j41(5,"ion-title",4),n.EFF(6),n.nI1(7,"translate"),n.k0s()()(),n.j41(8,"ion-content",5)(9,"form",6)(10,"div",7)(11,"ion-item")(12,"ion-label",8),n.EFF(13),n.nI1(14,"translate"),n.k0s(),n.j41(15,"ion-input",9),n.bIt("ionChange",function(U){return d.verifyPoints(U)}),n.k0s()(),n.j41(16,"div")(17,"ion-label"),n.EFF(18),n.k0s()()(),n.j41(19,"ion-button",10),n.bIt("click",function(){return d.doPayment()}),n.EFF(20),n.nI1(21,"translate"),n.k0s()()()),2&l&&(n.Y8G("ngIf",d.isLoading),n.R7$(6),n.SpI(" ",n.bMT(7,6,"AMIGO")," "),n.R7$(3),n.Y8G("formGroup",d.marketPlaceOrderForm),n.R7$(4),n.SpI("",n.bMT(14,8,"market-place.fill-quantity")," "),n.R7$(5),n.SpI("",(null==d.points?null:d.points.validate)||0," pts"),n.R7$(2),n.SpI(" ",n.bMT(21,10,"market-place.pay")," "))},dependencies:[g.bT,p.qT,p.BC,p.cb,p.YS,s.Jm,s.W9,s.eU,s.KW,s.$w,s.uz,s.he,s.Zx,s.BC,s.ai,s.Gw,e._,p.j4,p.JD,t.D9],styles:["*[_ngcontent-%COMP%]{margin:0;padding:0}.header-md[_ngcontent-%COMP%]:after{background-image:none!important}.modal[_ngcontent-%COMP%]{--border-radius: calc(60 * var(--res))}.input-group[_ngcontent-%COMP%]{margin-top:calc(75 * var(--res))}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{--padding-start: 0;margin-bottom:calc(50 * var(--res));--placeholder-color: $color-six}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-input[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{font-family:Mont Regular;font-weight:400;font-size:calc(42 * var(--res));color:#1e1e1e}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{padding-top:calc(25 * var(--res));padding-bottom:calc(25 * var(--res))}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]{margin-bottom:calc(50 * var(--res));display:flex;align-items:center;justify-content:space-between;border-bottom-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-250, #c8c7cc)));border-bottom-width:.55px;border-bottom-style:solid}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{width:90%;margin-bottom:0;--border-color: transparent;--inner-border-width: 0px 0px 0px 0px;--border-style: none}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-checkbox[_ngcontent-%COMP%]{margin-top:5%}.bg-primary[_ngcontent-%COMP%]{color:#143c5d}.message[_ngcontent-%COMP%]{margin-bottom:calc(75 * var(--res))}.message[_ngcontent-%COMP%]   ion-title[_ngcontent-%COMP%]{font-weight:800;font-size:calc(70 * var(--res));padding-bottom:calc(25 * var(--res))}.message[_ngcontent-%COMP%]   ion-text[_ngcontent-%COMP%]{font-weight:400;color:#757474;display:block;line-height:1.5em!important}.btn--meduim[_ngcontent-%COMP%]{height:calc(130 * var(--res));font-size:calc(50 * var(--res));font-family:var(--mont-bold)}.btn--meduim[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:var(--mont-bold)}.btn--small[_ngcontent-%COMP%]{height:calc(100 * var(--res))}.btn--round[_ngcontent-%COMP%]{--border-radius: 50%;padding-inline-start:0;padding-inline-end:0;--padding-start: 0;--padding-end: 0}.btn--border-primary[_ngcontent-%COMP%]{--background: transparent;--background-activated: #c4e6d0;--border-color: var(--ion-color-primary);--border-style: solid;--border-width: 2px}.btn--border-secondary[_ngcontent-%COMP%]{--border-width: 2px;--background: transparent;--background-activated: #ddabb2;--border-color: var(--ion-color-secondary);--border-style: solid}.btn--upper[_ngcontent-%COMP%]{text-transform:uppercase}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]{overflow:hidden}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar{width:5px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-track{box-shadow:inset 0 0 5px #546e8d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background:#143c5d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover{background:#143c5d}.bottom-sheet-content[_ngcontent-%COMP%]{background:#fdfdfe;height:100%}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-thumbnail[_ngcontent-%COMP%]{margin-left:calc(31.25 * var(--res));background-color:#1e1e1e;--border-radius: 50%;--size: 23}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:Mont Bold;font-size:calc(45 * var(--res));text-align:center;color:#757474}.bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%], .bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]{padding:calc(75 * var(--res))}.font-size-5[_ngcontent-%COMP%]{font-size:calc(42 * var(--res))}ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]{--background: rgba(187, 215, 243, .61);--color: $color-primary;--border-width: 0;--padding-bottom: 0;padding:0}ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-title[_ngcontent-%COMP%]{font-family:Mont Bold;color:#143c5d;font-size:calc(40 * var(--res));margin-bottom:.7em}ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-thumbnail[_ngcontent-%COMP%]{z-index:10;position:absolute;right:calc(41 * var(--res));height:max-content;width:max-content;margin-bottom:.3em}ion-content[_ngcontent-%COMP%]   .content-wrapper[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:1rem}ion-content[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]{color:#143c5d;margin-bottom:.5em;margin-top:calc(40 * var(--res))!important}ion-content[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{color:#143c5d}ion-content[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   div[_ngcontent-%COMP%]{text-align:center}ion-content[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   div[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{background:#83bbf7;padding:10px;border-radius:18px;font-size:14px;font-weight:700}ion-content[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%]{margin-top:0;--background: var(--clr-primary-900);color:var(--ion-color-tertiary-contrast);font-family:var(--mont-bold)}"]})}}return h})();var M=a(55988),y=a(58133),E=a(94440);function D(h,B){if(1&h){const o=n.RV6();n.j41(0,"ion-button",13),n.bIt("click",function(){n.eBV(o);const d=n.XpG();return n.Njj(d.showModalConfirmValidation())}),n.EFF(1),n.nI1(2,"translate"),n.k0s()}if(2&h){const o=n.XpG();n.Y8G("disabled",o.isOpen),n.R7$(1),n.SpI(" ",n.bMT(2,2,"market-place.reclaim")," ")}}const w=[{path:"",component:(()=>{class h{constructor(o,l,d){this.location=o,this.commonSrv=l,this.translateService=d,this.userCategory=y.s,this.slideOpts={initialSlide:0,spaceBetween:16,autoplay:!0},this.tabOption="other",this.usine="USINE DE BONABERI",this.imagesItem=["/assets/images/amigo.png","/assets/images/amigo.png","/assets/images/amigo.png","/assets/images/amigo.png","/assets/images/amigo.png","/assets/images/amigo.png","/assets/images/amigo.png"],this.starsIcon=["/assets/icons/star-yellow.svg","/assets/icons/star-yellow.svg","/assets/icons/star-yellow.svg","/assets/icons/star-yellow.svg","/assets/icons/star-grey.svg"],this.productSrv=(0,n.WQX)(b.b),this.modalCtrl=(0,n.WQX)(s.W3),this.storageSrv=(0,n.WQX)(O.n),this.marketSrv=(0,n.WQX)(M.L)}ngOnInit(){var o=this;return(0,_.A)(function*(){o.user=o.storageSrv.getUserConnected(),o.product=o.marketSrv.currentItems,o.isOpen=o.marketSrv.isOpen,o.alertNotification()})()}alertNotification(){var o=this;return(0,_.A)(function*(){o.isOpen&&(yield o.commonSrv.showToast({message:o.translateService.currentLang===P.T.French?"Vous pourrez \xe9changez vos points \xe0 la fin du compte \xe0 rebours.":"You can redeem your points at the end of the countdown.",color:"warning"}))})()}getProduct(o){var l=this;return(0,_.A)(function*(){yield l.getOrtherProducts()})()}getProductApplications(o){switch(o?.toLowerCase()){case"amigo":return"item-details-page.amigo-applications";case"camerounaise":return"item-details-page.camerounaise-applications";case"colombe":return"item-details-page.colombe-applications";case"pelican":return"item-details-page.pelican-applications";default:return"item-details-page.default-applications"}}getProductBenefits(o){switch(o?.toLowerCase()){case"amigo":return"item-details-page.amigo-benefits";case"camerounaise":return"item-details-page.camerounaise-benefits";case"colombe":return"item-details-page.colombe-benefits";case"pelican":return"item-details-page.pelican-benefits";default:return"item-details-page.default-benefits"}}getProductFeatures(o){switch(o?.toLowerCase()){case"amigo":return"item-details-page.amigo-features";case"camerounaise":return"item-details-page.camerounaise-features";case"colombe":return"item-details-page.colombe-features";case"pelican":return"item-details-page.pelican-features";default:return"item-details-page.default-features"}}getProductDescription(o){return(0,_.A)(function*(){let l;switch(o?.toLowerCase()){case"camerounaise":l="item-details-page.camerounaise-description";break;case"colombe":l="item-details-page.colombe-description";break;case"pelican":l="item-details-page.pelican-description";break;case"amigo":l="item-details-page.amigo-description";break;default:l="item-details-page.default-description"}return l})()}getOrtherProducts(){var o=this;return(0,_.A)(function*(){o.products=(yield o.productSrv.getProducts()).data,o.products?.filter((l,d)=>{l?._id===o.product?._id&&o.products.splice(d,1)})})()}showModalConfirmValidation(){var o=this;return(0,_.A)(function*(){const l=yield o.modalCtrl.create({component:c,cssClass:"modal",initialBreakpoint:.4,breakpoints:[0,.4,.5],mode:"ios",componentProps:{item:o.product}});yield l.present(),yield l.onWillDismiss()})()}changeTab(o){this.tabOption=o}back(){this.location.back()}static{this.\u0275fac=function(l){return new(l||h)(n.rXU(g.aZ),n.rXU(u.h),n.rXU(r.E))}}static{this.\u0275cmp=n.VBU({type:h,selectors:[["app-product-detail"]],decls:23,vars:9,consts:[[1,"header-page"],[1,"div-start"],[1,"logo-tag"],["slot","start","src","/assets/icons/arrow-back.svg",3,"click"],[1,"title"],[3,"fullscreen"],["id","container"],[1,"item-detail-focus"],[3,"options"],[1,"head-img",3,"src"],[1,"row"],[1,"detail-item"],["class","btn btn--medium btn--upper pay-btn","color","primary","expand","block",3,"disabled","click",4,"ngIf"],["color","primary","expand","block",1,"btn","btn--medium","btn--upper","pay-btn",3,"disabled","click"]],template:function(l,d){1&l&&(n.j41(0,"ion-header")(1,"ion-toolbar")(2,"div",0)(3,"div",1)(4,"div",2)(5,"ion-img",3),n.bIt("click",function(){return d.back()}),n.k0s()(),n.j41(6,"ion-title",4),n.EFF(7),n.nI1(8,"truncateString"),n.k0s()()()()(),n.j41(9,"ion-content",5)(10,"div",6)(11,"div",7)(12,"ion-item")(13,"ion-slides",8)(14,"ion-slide"),n.nrm(15,"ion-img",9),n.k0s()()(),n.j41(16,"p"),n.EFF(17),n.k0s(),n.j41(18,"div",10)(19,"div",11)(20,"ion-label",4),n.EFF(21),n.k0s()()(),n.DNE(22,D,3,4,"ion-button",12),n.k0s()()()),2&l&&(n.R7$(7),n.SpI(" ",n.bMT(8,7,null==d.product?null:d.product.name),""),n.R7$(2),n.Y8G("fullscreen",!0),n.R7$(4),n.Y8G("options",d.slideOpts),n.R7$(2),n.Y8G("src",null==d.product?null:d.product.image),n.R7$(2),n.SpI("",(null==d.product?null:d.product.description)||"N/A"," "),n.R7$(4),n.SpI(" ",(null==d.product?null:d.product.price)||"N/A"," Point(s) "),n.R7$(1),n.Y8G("ngIf",d.user.category===d.userCategory.Particular))},dependencies:[g.bT,s.Jm,s.W9,s.eU,s.KW,s.uz,s.he,s.q3,s.tR,s.BC,s.ai,E.c,t.D9],styles:["*[_ngcontent-%COMP%]{margin:0;padding:0}.header-md[_ngcontent-%COMP%]:after{background-image:none!important}.modal[_ngcontent-%COMP%]{--border-radius: calc(60 * var(--res))}.input-group[_ngcontent-%COMP%]{margin-top:calc(75 * var(--res))}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{--padding-start: 0;margin-bottom:calc(50 * var(--res));--placeholder-color: $color-six}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-input[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{font-family:Mont Regular;font-weight:400;font-size:calc(42 * var(--res));color:#1e1e1e}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{padding-top:calc(25 * var(--res));padding-bottom:calc(25 * var(--res))}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]{margin-bottom:calc(50 * var(--res));display:flex;align-items:center;justify-content:space-between;border-bottom-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-250, #c8c7cc)));border-bottom-width:.55px;border-bottom-style:solid}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{width:90%;margin-bottom:0;--border-color: transparent;--inner-border-width: 0px 0px 0px 0px;--border-style: none}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-checkbox[_ngcontent-%COMP%]{margin-top:5%}.bg-primary[_ngcontent-%COMP%]{color:#143c5d}.message[_ngcontent-%COMP%]{margin-bottom:calc(75 * var(--res))}.message[_ngcontent-%COMP%]   ion-title[_ngcontent-%COMP%]{font-weight:800;font-size:calc(70 * var(--res));padding-bottom:calc(25 * var(--res))}.message[_ngcontent-%COMP%]   ion-text[_ngcontent-%COMP%]{font-weight:400;color:#757474;display:block;line-height:1.5em!important}.btn--meduim[_ngcontent-%COMP%]{height:calc(130 * var(--res));font-size:calc(50 * var(--res));font-family:var(--mont-bold)}.btn--meduim[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:var(--mont-bold)}.btn--small[_ngcontent-%COMP%]{height:calc(100 * var(--res))}.btn--round[_ngcontent-%COMP%]{--border-radius: 50%;padding-inline-start:0;padding-inline-end:0;--padding-start: 0;--padding-end: 0}.btn--border-primary[_ngcontent-%COMP%]{--background: transparent;--background-activated: #c4e6d0;--border-color: var(--ion-color-primary);--border-style: solid;--border-width: 2px}.btn--border-secondary[_ngcontent-%COMP%]{--border-width: 2px;--background: transparent;--background-activated: #ddabb2;--border-color: var(--ion-color-secondary);--border-style: solid}.btn--upper[_ngcontent-%COMP%]{text-transform:uppercase}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]{overflow:hidden}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar{width:5px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-track{box-shadow:inset 0 0 5px #546e8d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background:#143c5d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover{background:#143c5d}.bottom-sheet-content[_ngcontent-%COMP%]{background:#fdfdfe;height:100%}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-thumbnail[_ngcontent-%COMP%]{margin-left:calc(31.25 * var(--res));background-color:#1e1e1e;--border-radius: 50%;--size: 23}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:Mont Bold;font-size:calc(45 * var(--res));text-align:center;color:#757474}.bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%], .bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]{padding:calc(75 * var(--res))}.font-size-5[_ngcontent-%COMP%]{font-size:calc(42 * var(--res))}ion-toolbar[_ngcontent-%COMP%]{--padding-start: var(--space-5);--padding-end: var(--space-5);--padding-top: var(--space-5);--border-color: transparent;--background-color: transparent}ion-toolbar[_ngcontent-%COMP%]   .div-start[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:space-between}ion-toolbar[_ngcontent-%COMP%]   .div-start[_ngcontent-%COMP%]   ion-img[_ngcontent-%COMP%]{width:calc(50 * var(--res));margin-right:calc(25 * var(--res))}ion-toolbar[_ngcontent-%COMP%]   .div-start[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{font-family:var(--mont-bold);text-align:start;color:var(--clr-primary-900)}ion-toolbar[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]{background:#f4f4f4}ion-toolbar[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%], ion-toolbar[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   .subtitle[_ngcontent-%COMP%]{font-family:Mont Regular}ion-toolbar[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{color:#0b305c;font-size:calc(45 * var(--res))}ion-toolbar[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   .subtitle[_ngcontent-%COMP%]{color:#000;font-size:calc(45 * var(--res))}ion-toolbar[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   .space-h-v[_ngcontent-%COMP%]{margin:calc(41 * var(--res)) 0;padding:0 calc(41 * var(--res))}ion-toolbar[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   .space-h-v[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%]::part(native){color:var(--clr-primary-700);background:var(--clr-primary-0)}ion-toolbar[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   .space-h-v[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:var(--mont-bold)}ion-toolbar[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   .searchbar[_ngcontent-%COMP%]{--background: transparent;height:calc(5 * var(--resH));border:none;--box-shadow: none;border-bottom:1px solid #1e1e1e}ion-toolbar[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]{text-transform:initial}ion-toolbar[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]{--border-color: transparent;padding:calc(41 * var(--res)) calc(41 * var(--res)) 0 calc(41 * var(--res));--background: #f4f4f4}ion-toolbar[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]{display:flex;align-items:center}ion-toolbar[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .header-title[_ngcontent-%COMP%]{display:flex;align-items:center}ion-toolbar[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .header-title[_ngcontent-%COMP%]   ion-img[_ngcontent-%COMP%]{width:calc(50 * var(--res));margin-right:calc(25 * var(--res))}ion-toolbar[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .header-title[_ngcontent-%COMP%]   .ion-arrow[_ngcontent-%COMP%]{height:20px;width:25px}ion-toolbar[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .header-title[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{font-size:calc(46 * var(--res));text-align:start;color:#0b305c}ion-toolbar[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .icons_profil[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:flex-end;gap:calc(31 * var(--res));width:100px}ion-toolbar[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .icons_profil[_ngcontent-%COMP%]   ion-img[_ngcontent-%COMP%]{width:calc(60 * var(--res))}ion-toolbar[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .icons_profil[_ngcontent-%COMP%]   .notification[_ngcontent-%COMP%]{text-align:right;position:relative}ion-toolbar[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .icons_profil[_ngcontent-%COMP%]   .notification[_ngcontent-%COMP%]   .badge[_ngcontent-%COMP%]{position:absolute;right:18%;width:calc(20 * var(--res));height:calc(20 * var(--res));border-radius:50%;background:rgb(173,5,5)}#container[_ngcontent-%COMP%]{padding:0;background-color:#fff}#container[_ngcontent-%COMP%]   .item-detail-focus[_ngcontent-%COMP%]{padding:0 calc(75 * var(--res));padding-bottom:calc(75 * var(--res));background-color:#fff;border-radius:0px 0px calc(35 * var(--res)) calc(35 * var(--res))}#container[_ngcontent-%COMP%]   .item-detail-focus[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{--padding-start: 0px;--inner-padding-end: 0px;margin-bottom:.7em}#container[_ngcontent-%COMP%]   .item-detail-focus[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-slides[_ngcontent-%COMP%]{height:calc(36.5 * var(--resH));padding-bottom:1em;width:100%}#container[_ngcontent-%COMP%]   .item-detail-focus[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-slides[_ngcontent-%COMP%]   .swiper-container-horizontal[_ngcontent-%COMP%]   .swiper-pagination-bullets[_ngcontent-%COMP%]{bottom:0!important;left:0;width:100%;justify-content:center}#container[_ngcontent-%COMP%]   .item-detail-focus[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-slides[_ngcontent-%COMP%]   ion-slide[_ngcontent-%COMP%]   ion-img[_ngcontent-%COMP%]{height:100%;width:100%}#container[_ngcontent-%COMP%]   .item-detail-focus[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{font-size:12px}#container[_ngcontent-%COMP%]   .item-detail-focus[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]{margin-bottom:1em;display:flex;align-items:center;justify-content:space-between}#container[_ngcontent-%COMP%]   .item-detail-focus[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{font-size:14px;color:#143c5d;font-style:normal;font-weight:400;line-height:normal;letter-spacing:-.165px}#container[_ngcontent-%COMP%]   .item-detail-focus[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .stars[_ngcontent-%COMP%]{margin-top:.25em;display:flex}#container[_ngcontent-%COMP%]   .item-detail-focus[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .stars[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{height:.75em;width:.75em}#container[_ngcontent-%COMP%]   .item-detail-focus[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .stars[_ngcontent-%COMP%]   ion-text[_ngcontent-%COMP%]{margin-left:.55em}#container[_ngcontent-%COMP%]   .item-detail-focus[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .stars[_ngcontent-%COMP%]   .small-stars[_ngcontent-%COMP%]{font-size:17px}#container[_ngcontent-%COMP%]   .item-detail-focus[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .stars[_ngcontent-%COMP%]   .small-text[_ngcontent-%COMP%]{font-size:10px;font-style:normal;font-weight:400;line-height:normal;letter-spacing:-.165px}#container[_ngcontent-%COMP%]   .item-detail-focus[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .btn-group[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%]{width:calc(4 * var(--resH));height:calc(4 * var(--resH));--padding-end: 0;--padding-start: 0;--border-radius: 50%}#container[_ngcontent-%COMP%]   .item-detail-focus[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .btn-group[_ngcontent-%COMP%]   .btn-border[_ngcontent-%COMP%]{margin-left:.5em;--border-color: #d9d9d9;--border-style: solid;--border-width: 1px;width:25px;height:25px}#container[_ngcontent-%COMP%]   .item-detail-focus[_ngcontent-%COMP%]   .pay-btn[_ngcontent-%COMP%]{height:40px}"]})}}return h})()}];let A=(()=>{class h{static{this.\u0275fac=function(l){return new(l||h)}}static{this.\u0275mod=n.$C({type:h})}static{this.\u0275inj=n.G2t({imports:[m.iI.forChild(w),m.iI]})}}return h})();var I=a(93887);let $=(()=>{class h{static{this.\u0275fac=function(l){return new(l||h)}}static{this.\u0275mod=n.$C({type:h})}static{this.\u0275inj=n.G2t({imports:[g.MD,p.YN,s.bv,I.G,t.h,p.X1,A]})}}return h})()},81559:(k,v,a)=>{a.d(v,{Q:()=>O});var g=a(73308),p=a(35025),s=a.n(p),m=a(94934),_=a(56610),n=a(45312),b=a(26409),P=a(2978),T=a(82571),f=a(33607),R=a(14599);let O=(()=>{class u{constructor(r,e,t,i){this.http=r,this.commonSrv=e,this.baseUrlService=t,this.storageSrv=i,this.url=this.baseUrlService.getOrigin()+n.c.basePath}create(r){var e=this;return(0,g.A)(function*(){try{return yield(0,m.s)(e.http.post(`${e.url}orders`,r))}catch(t){const c={message:e.commonSrv.getError("",t).message,color:"danger"};return yield e.commonSrv.showToast(c),t}})()}createOrderByCommercialForClient(r,e){var t=this;return(0,g.A)(function*(){try{return yield(0,m.s)(t.http.post(`${t.url}orders/${e}`,r))}catch(i){const M={message:t.commonSrv.getError("",i).message,color:"danger"};return yield t.commonSrv.showToast(M),i}})()}getAllOrder(r){var e=this;return(0,g.A)(function*(){try{let t=new b.Nl;const{num:i,commercialId:c,status:M,offset:y,limit:E,startDate:D,endDate:S,customerReference:w,selectedCompanyId:A}=r;return D&&S&&(t=t.append("startDate",new _.vh("fr").transform(D,"YYYY-MM-dd"))),S&&D&&(t=t.append("endDate",new _.vh("fr").transform(S,"YYYY-MM-dd"))),w&&(t=t.append("appReference",w)),A&&(t=t.append("selectedCompanyId",A)),c&&(t=t.append("commercial",c)),void 0!==y&&(t=t.append("offset",y)),E&&(t=t.append("limit",E)),M&&(t=t.append("status",M)),i&&(t=t.append("appReference",i)),yield(0,m.s)(e.http.get(`${e.url}orders/history`,{params:t}))}catch(t){const c={message:e.commonSrv.getError("",t).message,color:"danger"};return yield e.commonSrv.showToast(c),t}})()}getOrders(r){var e=this;return(0,g.A)(function*(){try{let t=new b.Nl;const{status:i,appReference:c,offset:M,limit:y,userCategory:E,paymentMode:D,validation:S,customer:w,product:A,date:I,enable:$=!0}=r;return M&&(t=t.append("offset",M)),y&&(t=t.append("limit",y)),i&&(t=t.append("status",i)),c&&(t=t.append("appReference",`${c}`)),D&&(t=t.append("payment.mode.id",D)),E&&(t=t.append("user.category",E)),w&&(t=t.append("user.email",w)),A&&(t=t.append("cart.items.product.label",A)),S&&(t=t.append("validation",S)),I.start&&I.end&&(t=t.append("startDate",s()(I.start).format("YYYY-MM-DD")),t=t.append("endDate",s()(I.end).format("YYYY-MM-DD"))),t=t.append("enable",$),yield(0,m.s)(e.http.get(`${e.url}orders`,{params:t}))}catch(t){return t}})()}updateOrders(r,e){var t=this;return(0,g.A)(function*(){try{return yield(0,m.s)(t.http.patch(`${t.url}orders/${r}`,e))}catch(i){const M={message:t.commonSrv.getError("",i).message,color:"danger"};return yield t.commonSrv.showToast(M),i}})()}RhValidatedOrder(r,e){var t=this;return(0,g.A)(function*(){try{return yield(0,m.s)(t.http.patch(`${t.url}orders/${r._id}/validate`,e))}catch(i){const M={message:t.commonSrv.getError("",i).message,color:"danger"};return yield t.commonSrv.showToast(M),i}})()}RhRejectOrder(r){var e=this;return(0,g.A)(function*(){try{return yield(0,m.s)(e.http.patch(`${e.url}orders/${r._id}/reject`,{}))}catch(t){const c={message:e.commonSrv.getError("",t).message,color:"danger"};return yield e.commonSrv.showToast(c),t}})()}sendOtp(r){var e=this;return(0,g.A)(function*(){try{return yield(0,m.s)(e.http.post(`${e.url}callback/afriland`,r))}catch(t){const c={message:e.commonSrv.getError("",t).message,color:"danger"};return yield e.commonSrv.showToast(c),t}})()}sendWallet(r){var e=this;return(0,g.A)(function*(){try{return yield(0,m.s)(e.http.post(`${e.url}orders/verify-Wallet-Nber`,r))}catch(t){const c={message:e.commonSrv.getError("",t).message,color:"danger"};return yield e.commonSrv.showToast(c),t}})()}ubaPayment(r){var e=this;return(0,g.A)(function*(){try{return yield(0,m.s)(e.http.post(`${e.url}orders/m2u-paymentRequest`,r))}catch(t){const c={message:e.commonSrv.getError("",t).message,color:"danger"};return yield e.commonSrv.showToast(c),t}})()}find(r){var e=this;return(0,g.A)(function*(){try{return yield(0,m.s)(e.http.get(e.url+"orders/"+r))}catch(t){const c={message:e.commonSrv.getError("",t).message,color:"danger"};return yield e.commonSrv.showToast(c),t}})()}getCardToken(){var r=this;return(0,g.A)(function*(){try{return yield(0,m.s)(r.http.post(`${r.url}orders/order-generate-visa-key`,{}))}catch(e){const i={message:r.commonSrv.getError("",e).message,color:"danger"};return yield r.commonSrv.showToast(i),e}})()}setupPayerAuthentication(r,e){var t=this;return(0,g.A)(function*(){try{return yield(0,m.s)(t.http.post(`${t.url}orders/order-setup-payer-auth`,{transientTokenJwt:r,order:e}))}catch(i){const M={message:t.commonSrv.getError("",i).message,color:"danger"};return yield t.commonSrv.showToast(M),i}})()}authorizationWithPAEnroll(r,e){var t=this;return(0,g.A)(function*(){try{return yield(0,m.s)(t.http.post(`${t.url}orders/order-authorization-pay-enroll`,{order:r,options:e}))}catch(i){const M={message:t.commonSrv.getError("",i).message,color:"danger"};return yield t.commonSrv.showToast(M),i}})()}checkIfOrderExist(r){var e=this;return(0,g.A)(function*(){try{return yield(0,m.s)(e.http.get(`${e.url}orders/${r}/exist`))}catch(t){const c={message:e.commonSrv.getError("",t).message,color:"danger"};return yield e.commonSrv.showToast(c),t}})()}generatePurchaseOrder(r){var e=this;return(0,g.A)(function*(){try{return yield(0,m.s)(e.http.get(`${e.url}orders/${r}/generate-purchase`))}catch(t){const c={message:e.commonSrv.getError("",t).message,color:"danger"};return yield e.commonSrv.showToast(c),t}})()}cancellationOrder(r,e){var t=this;return(0,g.A)(function*(){try{return yield(0,m.s)(t.http.patch(`${t.url}orders/${r}/cancellation-order`,e))}catch(i){const M={message:t.commonSrv.getError("",i).message,color:"danger"};return yield t.commonSrv.showToast(M),i}})()}updateCarrier(r,e){var t=this;return(0,g.A)(function*(){try{return yield(0,m.s)(t.http.patch(`${t.url}orders/${r}/add-carrier`,{carrier:e}))}catch(i){const M={message:t.commonSrv.getError("",i).message,color:"danger"};return yield t.commonSrv.showToast(M),i}})()}static{this.\u0275fac=function(e){return new(e||u)(P.KVO(b.Qq),P.KVO(T.h),P.KVO(f.K),P.KVO(R.n))}}static{this.\u0275prov=P.jDH({token:u,factory:u.\u0275fac,providedIn:"root"})}}return u})()},39316:(k,v,a)=>{a.d(v,{b:()=>T});var g=a(73308),p=a(26409),s=a(94934),m=a(45312),_=a(2978),n=a(82571),b=a(33607),P=a(77897);let T=(()=>{class f{constructor(O,u,C,r){this.http=O,this.commonSrv=u,this.baseUrlService=C,this.toastController=r,this.prices=[],this.currentDataProductScan=[],this.dataQrCode=[],this.url=this.baseUrlService.getOrigin()+m.c.basePath+"products"}getProducts(O){var u=this;return(0,g.A)(function*(){try{let C=new p.Nl;return O?.limit&&(C=C.append("limit",O?.limit)),yield(0,s.s)(u.http.get(u.url,{params:C}))}catch(C){const e={message:u.commonSrv.getError("",C).message,color:"danger"};return yield u.commonSrv.showToast(e),C}})()}getProduct(O){var u=this;return(0,g.A)(function*(){try{return yield(0,s.s)(u.http.get(`${u.url}/${O}`))}catch(C){const e={message:u.commonSrv.getError("",C).message,color:"danger"};return yield u.commonSrv.showToast(e),C}})()}static{this.\u0275fac=function(u){return new(u||f)(_.KVO(p.Qq),_.KVO(n.h),_.KVO(b.K),_.KVO(P.K_))}}static{this.\u0275prov=_.jDH({token:f,factory:f.\u0275fac,providedIn:"root"})}}return f})()},80153:(k,v,a)=>{a.d(v,{s:()=>p});var p=function(s){return s[s.PENDING=200]="PENDING",s[s.VALIDATED=300]="VALIDATED",s[s.EXPIRED=400]="EXPIRED",s}(p||{})},94440:(k,v,a)=>{a.d(v,{c:()=>p});var g=a(2978);let p=(()=>{class s{transform(_,...n){return _?_.length>n[0]?`${_.substring(0,n[0]-3)}...`:_:""}static{this.\u0275fac=function(n){return new(n||s)}}static{this.\u0275pipe=g.EJ8({name:"truncateString",type:s,pure:!0})}}return s})()},13217:(k,v,a)=>{a.d(v,{_:()=>O});var g=a(73308),p=a(26409),s=a(45312),m=a(94934),_=a(79801),n=a(80153),b=a(99987),P=a(2978),T=a(33607),f=a(82571),R=a(62049);let O=(()=>{class u{constructor(r,e,t,i){this.baseUrl=r,this.http=e,this.commonSrv=t,this.translateSrv=i,this.ratioOfPointsByVolume=.2,this.POINTS_MATRIX={AMIGO:{5:1,25:5,50:10},COLOMBE:{5:2,25:8,50:15},PELICAN:{5:5,25:12,50:25}},this.base_url=`${this.baseUrl.getOrigin()}${s.c.basePath}`}calculateTotalPointsOrder(r,e){const t=e?.points?.status||_.Th.AMIGO;return r.reduce((i,c)=>{const M=c?.packaging?.unit?.value||0,y=c?.quantity||0;return i+(this.POINTS_MATRIX[t]?.[M]||0)*y},0)}getPoints(r){var e=this;return(0,g.A)(function*(){let t=new p.Nl;const{companyId:i}=r;return i&&(t=t.append("companyId",i)),yield(0,m.s)(e.http.get(`${e.base_url}loyalty-program/points`,{params:t}))})()}getBenefitRewards(r){var e=this;return(0,g.A)(function*(){let t=new p.Nl;return r?.monthly&&(t=t.append("monthly",JSON.stringify(r.monthly))),r?.statusValue&&(t=t.append("statusValue",JSON.stringify(r?.statusValue))),r?.annual&&(t=t.append("annual",JSON.stringify(r.annual))),r?.punctual&&(t=t.append("punctual",JSON.stringify(r.punctual))),yield(0,m.s)(e.http.get(`${e.base_url}advantages`,{params:t}))})()}acceptInvitation(r,e){var t=this;return(0,g.A)(function*(){return yield(0,m.s)(t.http.patch(`${t.base_url}invitations/${r}/accept`,{referredUserId:e}))})()}getInvitations(r){var e=this;return(0,g.A)(function*(){let t=new p.Nl;const{limit:i,status:c,prospectTel:M}=r;return i&&(t=t.append("limit",i)),M&&(t=t.append("prospectTel",M)),c&&(t=t.append("status",c)),yield(0,m.s)(e.http.get(`${e.base_url}invitations`,{params:t}))})()}sendReferralInvitation(r,e){var t=this;return(0,g.A)(function*(){try{const i={referrerId:r,prospectTel:e};return yield(0,m.s)(t.http.post(`${t.base_url}invitations/${r}/invite-referral`,i))}catch(i){const c=t.translateSrv.currentLang===b.T.French?"Impossible d'envoyer l'invitation de parrainage":"Failed to send referral invitation";return yield t.commonSrv.showToast({message:i?.error?.message??t.commonSrv.getError(c,i).message,color:"danger"}),i}})()}getPendingInvitationForUser(r){var e=this;return(0,g.A)(function*(){let t=new p.Nl;return t=t.set("prospectTel",+r),t=t.append("status",n.s.PENDING),yield(0,m.s)(e.http.get(`${e.base_url}invitations/pending-invitation`,{params:t}))})()}getDisplayedPoints(r,e){return"pending"===e?r?.unValidated||0:r?.totalPoints||0}getDisplayedPointsLabel(r){return"pending"===r?"fidelity-page.waiting":"fidelity-page.total-points"}static{this.\u0275fac=function(e){return new(e||u)(P.KVO(T.K),P.KVO(p.Qq),P.KVO(f.h),P.KVO(R.E))}}static{this.\u0275prov=P.jDH({token:u,factory:u.\u0275fac,providedIn:"root"})}}return u})()}}]);