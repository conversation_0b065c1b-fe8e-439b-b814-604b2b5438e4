1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.clickcadyst.mobile"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="23"
9        android:targetSdkVersion="35" />
10
11    <!-- Permissions -->
12
13    <uses-permission android:name="android.permission.INTERNET" />
13-->C:\Users\<USER>\Desktop\Projet LONDO TECH\LA PASTA\la-pasta-mobile\android\app\src\main\AndroidManifest.xml:36:5-67
13-->C:\Users\<USER>\Desktop\Projet LONDO TECH\LA PASTA\la-pasta-mobile\android\app\src\main\AndroidManifest.xml:36:22-64
14    <uses-permission android:name="android.permission.CAMERA" />
14-->C:\Users\<USER>\Desktop\Projet LONDO TECH\LA PASTA\la-pasta-mobile\android\app\src\main\AndroidManifest.xml:37:5-65
14-->C:\Users\<USER>\Desktop\Projet LONDO TECH\LA PASTA\la-pasta-mobile\android\app\src\main\AndroidManifest.xml:37:22-62
15    <uses-permission android:name="android.permission.FLASHLIGHT" />
15-->C:\Users\<USER>\Desktop\Projet LONDO TECH\LA PASTA\la-pasta-mobile\android\app\src\main\AndroidManifest.xml:38:5-69
15-->C:\Users\<USER>\Desktop\Projet LONDO TECH\LA PASTA\la-pasta-mobile\android\app\src\main\AndroidManifest.xml:38:22-66
16    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
16-->C:\Users\<USER>\Desktop\Projet LONDO TECH\LA PASTA\la-pasta-mobile\android\app\src\main\AndroidManifest.xml:39:5-81
16-->C:\Users\<USER>\Desktop\Projet LONDO TECH\LA PASTA\la-pasta-mobile\android\app\src\main\AndroidManifest.xml:39:22-78
17    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
17-->C:\Users\<USER>\Desktop\Projet LONDO TECH\LA PASTA\la-pasta-mobile\android\app\src\main\AndroidManifest.xml:40:5-79
17-->C:\Users\<USER>\Desktop\Projet LONDO TECH\LA PASTA\la-pasta-mobile\android\app\src\main\AndroidManifest.xml:40:22-76
18
19    <!-- SMS Retriever permissions -->
20    <uses-permission android:name="android.permission.RECEIVE_SMS" />
20-->C:\Users\<USER>\Desktop\Projet LONDO TECH\LA PASTA\la-pasta-mobile\android\app\src\main\AndroidManifest.xml:43:5-70
20-->C:\Users\<USER>\Desktop\Projet LONDO TECH\LA PASTA\la-pasta-mobile\android\app\src\main\AndroidManifest.xml:43:22-67
21    <uses-permission android:name="android.permission.READ_SMS" />
21-->C:\Users\<USER>\Desktop\Projet LONDO TECH\LA PASTA\la-pasta-mobile\android\app\src\main\AndroidManifest.xml:44:5-67
21-->C:\Users\<USER>\Desktop\Projet LONDO TECH\LA PASTA\la-pasta-mobile\android\app\src\main\AndroidManifest.xml:44:22-64
22    <uses-permission android:name="com.google.android.gms.permission.AD_ID" />
22-->C:\Users\<USER>\Desktop\Projet LONDO TECH\LA PASTA\la-pasta-mobile\android\app\src\main\AndroidManifest.xml:45:5-79
22-->C:\Users\<USER>\Desktop\Projet LONDO TECH\LA PASTA\la-pasta-mobile\android\app\src\main\AndroidManifest.xml:45:22-76
23
24    <queries>
24-->[:capacitor-camera] C:\Users\<USER>\Desktop\Projet LONDO TECH\LA PASTA\la-pasta-mobile\node_modules\@capacitor\camera\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-11:15
25        <intent>
25-->[:capacitor-camera] C:\Users\<USER>\Desktop\Projet LONDO TECH\LA PASTA\la-pasta-mobile\node_modules\@capacitor\camera\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-10:18
26            <action android:name="android.media.action.IMAGE_CAPTURE" />
26-->[:capacitor-camera] C:\Users\<USER>\Desktop\Projet LONDO TECH\LA PASTA\la-pasta-mobile\node_modules\@capacitor\camera\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-73
26-->[:capacitor-camera] C:\Users\<USER>\Desktop\Projet LONDO TECH\LA PASTA\la-pasta-mobile\node_modules\@capacitor\camera\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:21-70
27        </intent>
28    </queries>
29
30    <uses-feature
30-->[:capacitor-cordova-android-plugins] C:\Users\<USER>\Desktop\Projet LONDO TECH\LA PASTA\la-pasta-mobile\android\capacitor-cordova-android-plugins\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-10:36
31        android:name="android.hardware.telephony"
31-->[:capacitor-cordova-android-plugins] C:\Users\<USER>\Desktop\Projet LONDO TECH\LA PASTA\la-pasta-mobile\android\capacitor-cordova-android-plugins\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:9-50
32        android:required="false" />
32-->[:capacitor-cordova-android-plugins] C:\Users\<USER>\Desktop\Projet LONDO TECH\LA PASTA\la-pasta-mobile\android\capacitor-cordova-android-plugins\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:9-33
33
34    <permission
34-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e218b8c118997427953592d90c2fa2e8\transformed\core-1.15.0\AndroidManifest.xml:22:5-24:47
35        android:name="com.clickcadyst.mobile.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
35-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e218b8c118997427953592d90c2fa2e8\transformed\core-1.15.0\AndroidManifest.xml:23:9-81
36        android:protectionLevel="signature" />
36-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e218b8c118997427953592d90c2fa2e8\transformed\core-1.15.0\AndroidManifest.xml:24:9-44
37
38    <uses-permission android:name="com.clickcadyst.mobile.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" /> <!-- Although the *SdkVersion is captured in gradle build files, this is required for non gradle builds -->
38-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e218b8c118997427953592d90c2fa2e8\transformed\core-1.15.0\AndroidManifest.xml:26:5-97
38-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e218b8c118997427953592d90c2fa2e8\transformed\core-1.15.0\AndroidManifest.xml:26:22-94
39    <!-- <uses-sdk android:minSdkVersion="14"/> -->
40    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
40-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\06c45ee83264f0a53a1a2872f349892b\transformed\jetified-transport-backend-cct-2.3.3\AndroidManifest.xml:25:5-79
40-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\06c45ee83264f0a53a1a2872f349892b\transformed\jetified-transport-backend-cct-2.3.3\AndroidManifest.xml:25:22-76
41
42    <application
42-->C:\Users\<USER>\Desktop\Projet LONDO TECH\LA PASTA\la-pasta-mobile\android\app\src\main\AndroidManifest.xml:3:5-32:19
43        android:allowBackup="true"
43-->C:\Users\<USER>\Desktop\Projet LONDO TECH\LA PASTA\la-pasta-mobile\android\app\src\main\AndroidManifest.xml:4:9-35
44        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
44-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e218b8c118997427953592d90c2fa2e8\transformed\core-1.15.0\AndroidManifest.xml:28:18-86
45        android:debuggable="true"
46        android:extractNativeLibs="false"
47        android:icon="@mipmap/ic_launcher"
47-->C:\Users\<USER>\Desktop\Projet LONDO TECH\LA PASTA\la-pasta-mobile\android\app\src\main\AndroidManifest.xml:5:9-43
48        android:label="@string/app_name"
48-->C:\Users\<USER>\Desktop\Projet LONDO TECH\LA PASTA\la-pasta-mobile\android\app\src\main\AndroidManifest.xml:6:9-41
49        android:roundIcon="@mipmap/ic_launcher_round"
49-->C:\Users\<USER>\Desktop\Projet LONDO TECH\LA PASTA\la-pasta-mobile\android\app\src\main\AndroidManifest.xml:7:9-54
50        android:supportsRtl="true"
50-->C:\Users\<USER>\Desktop\Projet LONDO TECH\LA PASTA\la-pasta-mobile\android\app\src\main\AndroidManifest.xml:8:9-35
51        android:testOnly="true"
52        android:theme="@style/AppTheme" >
52-->C:\Users\<USER>\Desktop\Projet LONDO TECH\LA PASTA\la-pasta-mobile\android\app\src\main\AndroidManifest.xml:9:9-40
53
54        <!-- ML Kit barcode scanner dependency -->
55        <meta-data
55-->C:\Users\<USER>\Desktop\Projet LONDO TECH\LA PASTA\la-pasta-mobile\android\app\src\main\AndroidManifest.xml:11:9-101
56            android:name="com.google.mlkit.vision.DEPENDENCIES"
56-->C:\Users\<USER>\Desktop\Projet LONDO TECH\LA PASTA\la-pasta-mobile\android\app\src\main\AndroidManifest.xml:11:20-71
57            android:value="barcode_ui" />
57-->C:\Users\<USER>\Desktop\Projet LONDO TECH\LA PASTA\la-pasta-mobile\android\app\src\main\AndroidManifest.xml:11:72-98
58
59        <activity
59-->C:\Users\<USER>\Desktop\Projet LONDO TECH\LA PASTA\la-pasta-mobile\android\app\src\main\AndroidManifest.xml:12:9-23:20
60            android:name="com.clickcadyst.mobile.MainActivity"
60-->C:\Users\<USER>\Desktop\Projet LONDO TECH\LA PASTA\la-pasta-mobile\android\app\src\main\AndroidManifest.xml:14:13-41
61            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|locale|smallestScreenSize|screenLayout|uiMode|navigation"
61-->C:\Users\<USER>\Desktop\Projet LONDO TECH\LA PASTA\la-pasta-mobile\android\app\src\main\AndroidManifest.xml:13:13-140
62            android:exported="true"
62-->C:\Users\<USER>\Desktop\Projet LONDO TECH\LA PASTA\la-pasta-mobile\android\app\src\main\AndroidManifest.xml:18:13-36
63            android:label="@string/title_activity_main"
63-->C:\Users\<USER>\Desktop\Projet LONDO TECH\LA PASTA\la-pasta-mobile\android\app\src\main\AndroidManifest.xml:15:13-56
64            android:launchMode="singleTask"
64-->C:\Users\<USER>\Desktop\Projet LONDO TECH\LA PASTA\la-pasta-mobile\android\app\src\main\AndroidManifest.xml:17:13-44
65            android:theme="@style/AppTheme.NoActionBarLaunch" >
65-->C:\Users\<USER>\Desktop\Projet LONDO TECH\LA PASTA\la-pasta-mobile\android\app\src\main\AndroidManifest.xml:16:13-62
66            <intent-filter>
66-->C:\Users\<USER>\Desktop\Projet LONDO TECH\LA PASTA\la-pasta-mobile\android\app\src\main\AndroidManifest.xml:19:13-22:29
67                <action android:name="android.intent.action.MAIN" />
67-->C:\Users\<USER>\Desktop\Projet LONDO TECH\LA PASTA\la-pasta-mobile\android\app\src\main\AndroidManifest.xml:20:17-69
67-->C:\Users\<USER>\Desktop\Projet LONDO TECH\LA PASTA\la-pasta-mobile\android\app\src\main\AndroidManifest.xml:20:25-66
68
69                <category android:name="android.intent.category.LAUNCHER" />
69-->C:\Users\<USER>\Desktop\Projet LONDO TECH\LA PASTA\la-pasta-mobile\android\app\src\main\AndroidManifest.xml:21:17-77
69-->C:\Users\<USER>\Desktop\Projet LONDO TECH\LA PASTA\la-pasta-mobile\android\app\src\main\AndroidManifest.xml:21:27-74
70            </intent-filter>
71        </activity>
72
73        <provider
74            android:name="androidx.core.content.FileProvider"
74-->C:\Users\<USER>\Desktop\Projet LONDO TECH\LA PASTA\la-pasta-mobile\android\app\src\main\AndroidManifest.xml:26:13-62
75            android:authorities="com.clickcadyst.mobile.fileprovider"
75-->C:\Users\<USER>\Desktop\Projet LONDO TECH\LA PASTA\la-pasta-mobile\android\app\src\main\AndroidManifest.xml:27:13-64
76            android:exported="false"
76-->C:\Users\<USER>\Desktop\Projet LONDO TECH\LA PASTA\la-pasta-mobile\android\app\src\main\AndroidManifest.xml:28:13-37
77            android:grantUriPermissions="true" >
77-->C:\Users\<USER>\Desktop\Projet LONDO TECH\LA PASTA\la-pasta-mobile\android\app\src\main\AndroidManifest.xml:29:13-47
78            <meta-data
78-->C:\Users\<USER>\Desktop\Projet LONDO TECH\LA PASTA\la-pasta-mobile\android\app\src\main\AndroidManifest.xml:30:13-112
79                android:name="android.support.FILE_PROVIDER_PATHS"
79-->C:\Users\<USER>\Desktop\Projet LONDO TECH\LA PASTA\la-pasta-mobile\android\app\src\main\AndroidManifest.xml:30:24-74
80                android:resource="@xml/file_paths" />
80-->C:\Users\<USER>\Desktop\Projet LONDO TECH\LA PASTA\la-pasta-mobile\android\app\src\main\AndroidManifest.xml:30:75-109
81        </provider>
82
83        <receiver
83-->[:capacitor-cordova-android-plugins] C:\Users\<USER>\Desktop\Projet LONDO TECH\LA PASTA\la-pasta-mobile\android\capacitor-cordova-android-plugins\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:9-20:20
84            android:name="com.andreszs.smsretriever.SMSBroadcastReceiver"
84-->[:capacitor-cordova-android-plugins] C:\Users\<USER>\Desktop\Projet LONDO TECH\LA PASTA\la-pasta-mobile\android\capacitor-cordova-android-plugins\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-74
85            android:exported="true"
85-->[:capacitor-cordova-android-plugins] C:\Users\<USER>\Desktop\Projet LONDO TECH\LA PASTA\la-pasta-mobile\android\capacitor-cordova-android-plugins\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:13-36
86            android:permission="com.google.android.gms.auth.api.phone.permission.SEND" >
86-->[:capacitor-cordova-android-plugins] C:\Users\<USER>\Desktop\Projet LONDO TECH\LA PASTA\la-pasta-mobile\android\capacitor-cordova-android-plugins\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:13-87
87            <intent-filter>
87-->[:capacitor-cordova-android-plugins] C:\Users\<USER>\Desktop\Projet LONDO TECH\LA PASTA\la-pasta-mobile\android\capacitor-cordova-android-plugins\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:17:13-19:29
88                <action android:name="com.google.android.gms.auth.api.phone.SMS_RETRIEVED" />
88-->[:capacitor-cordova-android-plugins] C:\Users\<USER>\Desktop\Projet LONDO TECH\LA PASTA\la-pasta-mobile\android\capacitor-cordova-android-plugins\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:18:17-94
88-->[:capacitor-cordova-android-plugins] C:\Users\<USER>\Desktop\Projet LONDO TECH\LA PASTA\la-pasta-mobile\android\capacitor-cordova-android-plugins\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:18:25-91
89            </intent-filter>
90        </receiver>
91        <!--
92        This activity is an invisible delegate activity to start scanner activity
93        and receive result, so it's unnecessary to support screen orientation and
94        we can avoid any side effect from activity recreation in any case.
95        -->
96        <activity
96-->[com.google.android.gms:play-services-code-scanner:16.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\809000ee9483f32dd44add99df2c7cb4\transformed\jetified-play-services-code-scanner-16.1.0\AndroidManifest.xml:15:9-20:20
97            android:name="com.google.mlkit.vision.codescanner.internal.GmsBarcodeScanningDelegateActivity"
97-->[com.google.android.gms:play-services-code-scanner:16.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\809000ee9483f32dd44add99df2c7cb4\transformed\jetified-play-services-code-scanner-16.1.0\AndroidManifest.xml:16:13-107
98            android:exported="false"
98-->[com.google.android.gms:play-services-code-scanner:16.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\809000ee9483f32dd44add99df2c7cb4\transformed\jetified-play-services-code-scanner-16.1.0\AndroidManifest.xml:17:13-37
99            android:screenOrientation="portrait" >
99-->[com.google.android.gms:play-services-code-scanner:16.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\809000ee9483f32dd44add99df2c7cb4\transformed\jetified-play-services-code-scanner-16.1.0\AndroidManifest.xml:18:13-49
100        </activity>
101
102        <service
102-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d55bd8fa6ecdc0a194a8fd32ba400784\transformed\jetified-play-services-mlkit-barcode-scanning-18.3.1\AndroidManifest.xml:9:9-15:19
103            android:name="com.google.mlkit.common.internal.MlKitComponentDiscoveryService"
103-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d55bd8fa6ecdc0a194a8fd32ba400784\transformed\jetified-play-services-mlkit-barcode-scanning-18.3.1\AndroidManifest.xml:10:13-91
104            android:directBootAware="true"
104-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a5a91bd578d149a6c95bbcc3efd16a9d\transformed\jetified-common-18.11.0\AndroidManifest.xml:17:13-43
105            android:exported="false" >
105-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d55bd8fa6ecdc0a194a8fd32ba400784\transformed\jetified-play-services-mlkit-barcode-scanning-18.3.1\AndroidManifest.xml:11:13-37
106            <meta-data
106-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d55bd8fa6ecdc0a194a8fd32ba400784\transformed\jetified-play-services-mlkit-barcode-scanning-18.3.1\AndroidManifest.xml:12:13-14:85
107                android:name="com.google.firebase.components:com.google.mlkit.vision.barcode.internal.BarcodeRegistrar"
107-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d55bd8fa6ecdc0a194a8fd32ba400784\transformed\jetified-play-services-mlkit-barcode-scanning-18.3.1\AndroidManifest.xml:13:17-120
108                android:value="com.google.firebase.components.ComponentRegistrar" />
108-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d55bd8fa6ecdc0a194a8fd32ba400784\transformed\jetified-play-services-mlkit-barcode-scanning-18.3.1\AndroidManifest.xml:14:17-82
109            <meta-data
109-->[com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b063b32a14ce5f5b3194ab73cfc4ee14\transformed\jetified-vision-common-17.3.0\AndroidManifest.xml:12:13-14:85
110                android:name="com.google.firebase.components:com.google.mlkit.vision.common.internal.VisionCommonRegistrar"
110-->[com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b063b32a14ce5f5b3194ab73cfc4ee14\transformed\jetified-vision-common-17.3.0\AndroidManifest.xml:13:17-124
111                android:value="com.google.firebase.components.ComponentRegistrar" />
111-->[com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b063b32a14ce5f5b3194ab73cfc4ee14\transformed\jetified-vision-common-17.3.0\AndroidManifest.xml:14:17-82
112            <meta-data
112-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a5a91bd578d149a6c95bbcc3efd16a9d\transformed\jetified-common-18.11.0\AndroidManifest.xml:20:13-22:85
113                android:name="com.google.firebase.components:com.google.mlkit.common.internal.CommonComponentRegistrar"
113-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a5a91bd578d149a6c95bbcc3efd16a9d\transformed\jetified-common-18.11.0\AndroidManifest.xml:21:17-120
114                android:value="com.google.firebase.components.ComponentRegistrar" />
114-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a5a91bd578d149a6c95bbcc3efd16a9d\transformed\jetified-common-18.11.0\AndroidManifest.xml:22:17-82
115        </service>
116
117        <provider
117-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a5a91bd578d149a6c95bbcc3efd16a9d\transformed\jetified-common-18.11.0\AndroidManifest.xml:9:9-13:38
118            android:name="com.google.mlkit.common.internal.MlKitInitProvider"
118-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a5a91bd578d149a6c95bbcc3efd16a9d\transformed\jetified-common-18.11.0\AndroidManifest.xml:10:13-78
119            android:authorities="com.clickcadyst.mobile.mlkitinitprovider"
119-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a5a91bd578d149a6c95bbcc3efd16a9d\transformed\jetified-common-18.11.0\AndroidManifest.xml:11:13-69
120            android:exported="false"
120-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a5a91bd578d149a6c95bbcc3efd16a9d\transformed\jetified-common-18.11.0\AndroidManifest.xml:12:13-37
121            android:initOrder="99" />
121-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a5a91bd578d149a6c95bbcc3efd16a9d\transformed\jetified-common-18.11.0\AndroidManifest.xml:13:13-35
122
123        <activity
123-->[com.google.android.gms:play-services-auth:21.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c08f3e536f5af5cebd7b0f2a9c8eb2d1\transformed\jetified-play-services-auth-21.2.0\AndroidManifest.xml:23:9-27:75
124            android:name="com.google.android.gms.auth.api.signin.internal.SignInHubActivity"
124-->[com.google.android.gms:play-services-auth:21.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c08f3e536f5af5cebd7b0f2a9c8eb2d1\transformed\jetified-play-services-auth-21.2.0\AndroidManifest.xml:24:13-93
125            android:excludeFromRecents="true"
125-->[com.google.android.gms:play-services-auth:21.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c08f3e536f5af5cebd7b0f2a9c8eb2d1\transformed\jetified-play-services-auth-21.2.0\AndroidManifest.xml:25:13-46
126            android:exported="false"
126-->[com.google.android.gms:play-services-auth:21.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c08f3e536f5af5cebd7b0f2a9c8eb2d1\transformed\jetified-play-services-auth-21.2.0\AndroidManifest.xml:26:13-37
127            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
127-->[com.google.android.gms:play-services-auth:21.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c08f3e536f5af5cebd7b0f2a9c8eb2d1\transformed\jetified-play-services-auth-21.2.0\AndroidManifest.xml:27:13-72
128        <!--
129            Service handling Google Sign-In user revocation. For apps that do not integrate with
130            Google Sign-In, this service will never be started.
131        -->
132        <service
132-->[com.google.android.gms:play-services-auth:21.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c08f3e536f5af5cebd7b0f2a9c8eb2d1\transformed\jetified-play-services-auth-21.2.0\AndroidManifest.xml:33:9-37:51
133            android:name="com.google.android.gms.auth.api.signin.RevocationBoundService"
133-->[com.google.android.gms:play-services-auth:21.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c08f3e536f5af5cebd7b0f2a9c8eb2d1\transformed\jetified-play-services-auth-21.2.0\AndroidManifest.xml:34:13-89
134            android:exported="true"
134-->[com.google.android.gms:play-services-auth:21.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c08f3e536f5af5cebd7b0f2a9c8eb2d1\transformed\jetified-play-services-auth-21.2.0\AndroidManifest.xml:35:13-36
135            android:permission="com.google.android.gms.auth.api.signin.permission.REVOCATION_NOTIFICATION"
135-->[com.google.android.gms:play-services-auth:21.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c08f3e536f5af5cebd7b0f2a9c8eb2d1\transformed\jetified-play-services-auth-21.2.0\AndroidManifest.xml:36:13-107
136            android:visibleToInstantApps="true" />
136-->[com.google.android.gms:play-services-auth:21.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c08f3e536f5af5cebd7b0f2a9c8eb2d1\transformed\jetified-play-services-auth-21.2.0\AndroidManifest.xml:37:13-48
137
138        <activity
138-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\28141fbb4925ebb9bc097857c92d6d27\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:9-173
139            android:name="com.google.android.gms.common.api.GoogleApiActivity"
139-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\28141fbb4925ebb9bc097857c92d6d27\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:19-85
140            android:exported="false"
140-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\28141fbb4925ebb9bc097857c92d6d27\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:146-170
141            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
141-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\28141fbb4925ebb9bc097857c92d6d27\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:86-145
142
143        <provider
143-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\543aa4d8cde29bfa9a5da326fa5b8380\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
144            android:name="androidx.startup.InitializationProvider"
144-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\543aa4d8cde29bfa9a5da326fa5b8380\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:25:13-67
145            android:authorities="com.clickcadyst.mobile.androidx-startup"
145-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\543aa4d8cde29bfa9a5da326fa5b8380\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:26:13-68
146            android:exported="false" >
146-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\543aa4d8cde29bfa9a5da326fa5b8380\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:27:13-37
147            <meta-data
147-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\543aa4d8cde29bfa9a5da326fa5b8380\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
148                android:name="androidx.emoji2.text.EmojiCompatInitializer"
148-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\543aa4d8cde29bfa9a5da326fa5b8380\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:30:17-75
149                android:value="androidx.startup" />
149-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\543aa4d8cde29bfa9a5da326fa5b8380\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:31:17-49
150            <meta-data
150-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\900e43aff56f75a168826022aabdc33d\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
151                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
151-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\900e43aff56f75a168826022aabdc33d\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
152                android:value="androidx.startup" />
152-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\900e43aff56f75a168826022aabdc33d\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
153            <meta-data
153-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bb57c83c4e4395aacfd7e9ff476c67e8\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
154                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
154-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bb57c83c4e4395aacfd7e9ff476c67e8\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
155                android:value="androidx.startup" />
155-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bb57c83c4e4395aacfd7e9ff476c67e8\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
156        </provider>
157
158        <service
158-->[androidx.camera:camera-camera2:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8bcc53a3834ebaf77da2288f604a904c\transformed\jetified-camera-camera2-1.1.0\AndroidManifest.xml:26:9-35:19
159            android:name="androidx.camera.core.impl.MetadataHolderService"
159-->[androidx.camera:camera-camera2:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8bcc53a3834ebaf77da2288f604a904c\transformed\jetified-camera-camera2-1.1.0\AndroidManifest.xml:27:13-75
160            android:enabled="false"
160-->[androidx.camera:camera-camera2:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8bcc53a3834ebaf77da2288f604a904c\transformed\jetified-camera-camera2-1.1.0\AndroidManifest.xml:28:13-36
161            android:exported="false" >
161-->[androidx.camera:camera-camera2:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8bcc53a3834ebaf77da2288f604a904c\transformed\jetified-camera-camera2-1.1.0\AndroidManifest.xml:29:13-37
162            <meta-data
162-->[androidx.camera:camera-camera2:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8bcc53a3834ebaf77da2288f604a904c\transformed\jetified-camera-camera2-1.1.0\AndroidManifest.xml:32:13-34:89
163                android:name="androidx.camera.core.impl.MetadataHolderService.DEFAULT_CONFIG_PROVIDER"
163-->[androidx.camera:camera-camera2:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8bcc53a3834ebaf77da2288f604a904c\transformed\jetified-camera-camera2-1.1.0\AndroidManifest.xml:33:17-103
164                android:value="androidx.camera.camera2.Camera2Config$DefaultProvider" />
164-->[androidx.camera:camera-camera2:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8bcc53a3834ebaf77da2288f604a904c\transformed\jetified-camera-camera2-1.1.0\AndroidManifest.xml:34:17-86
165        </service>
166
167        <meta-data
167-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ad29408dc46c7a7882a72a73e429c1d5\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:9-122
168            android:name="com.google.android.gms.version"
168-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ad29408dc46c7a7882a72a73e429c1d5\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:20-65
169            android:value="@integer/google_play_services_version" />
169-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ad29408dc46c7a7882a72a73e429c1d5\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:66-119
170
171        <receiver
171-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bb57c83c4e4395aacfd7e9ff476c67e8\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
172            android:name="androidx.profileinstaller.ProfileInstallReceiver"
172-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bb57c83c4e4395aacfd7e9ff476c67e8\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
173            android:directBootAware="false"
173-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bb57c83c4e4395aacfd7e9ff476c67e8\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
174            android:enabled="true"
174-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bb57c83c4e4395aacfd7e9ff476c67e8\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
175            android:exported="true"
175-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bb57c83c4e4395aacfd7e9ff476c67e8\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
176            android:permission="android.permission.DUMP" >
176-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bb57c83c4e4395aacfd7e9ff476c67e8\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
177            <intent-filter>
177-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bb57c83c4e4395aacfd7e9ff476c67e8\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
178                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
178-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bb57c83c4e4395aacfd7e9ff476c67e8\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
178-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bb57c83c4e4395aacfd7e9ff476c67e8\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
179            </intent-filter>
180            <intent-filter>
180-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bb57c83c4e4395aacfd7e9ff476c67e8\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
181                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
181-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bb57c83c4e4395aacfd7e9ff476c67e8\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
181-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bb57c83c4e4395aacfd7e9ff476c67e8\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
182            </intent-filter>
183            <intent-filter>
183-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bb57c83c4e4395aacfd7e9ff476c67e8\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
184                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
184-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bb57c83c4e4395aacfd7e9ff476c67e8\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
184-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bb57c83c4e4395aacfd7e9ff476c67e8\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
185            </intent-filter>
186            <intent-filter>
186-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bb57c83c4e4395aacfd7e9ff476c67e8\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
187                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
187-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bb57c83c4e4395aacfd7e9ff476c67e8\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
187-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bb57c83c4e4395aacfd7e9ff476c67e8\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
188            </intent-filter>
189        </receiver>
190
191        <service
191-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\06c45ee83264f0a53a1a2872f349892b\transformed\jetified-transport-backend-cct-2.3.3\AndroidManifest.xml:29:9-35:19
192            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
192-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\06c45ee83264f0a53a1a2872f349892b\transformed\jetified-transport-backend-cct-2.3.3\AndroidManifest.xml:30:13-103
193            android:exported="false" >
193-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\06c45ee83264f0a53a1a2872f349892b\transformed\jetified-transport-backend-cct-2.3.3\AndroidManifest.xml:31:13-37
194            <meta-data
194-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\06c45ee83264f0a53a1a2872f349892b\transformed\jetified-transport-backend-cct-2.3.3\AndroidManifest.xml:32:13-34:39
195                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
195-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\06c45ee83264f0a53a1a2872f349892b\transformed\jetified-transport-backend-cct-2.3.3\AndroidManifest.xml:33:17-94
196                android:value="cct" />
196-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\06c45ee83264f0a53a1a2872f349892b\transformed\jetified-transport-backend-cct-2.3.3\AndroidManifest.xml:34:17-36
197        </service>
198        <service
198-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a48f688fa386ce7aed0a486b8b5ba154\transformed\jetified-transport-runtime-2.2.6\AndroidManifest.xml:26:9-30:19
199            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
199-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a48f688fa386ce7aed0a486b8b5ba154\transformed\jetified-transport-runtime-2.2.6\AndroidManifest.xml:27:13-117
200            android:exported="false"
200-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a48f688fa386ce7aed0a486b8b5ba154\transformed\jetified-transport-runtime-2.2.6\AndroidManifest.xml:28:13-37
201            android:permission="android.permission.BIND_JOB_SERVICE" >
201-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a48f688fa386ce7aed0a486b8b5ba154\transformed\jetified-transport-runtime-2.2.6\AndroidManifest.xml:29:13-69
202        </service>
203
204        <receiver
204-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a48f688fa386ce7aed0a486b8b5ba154\transformed\jetified-transport-runtime-2.2.6\AndroidManifest.xml:32:9-34:40
205            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
205-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a48f688fa386ce7aed0a486b8b5ba154\transformed\jetified-transport-runtime-2.2.6\AndroidManifest.xml:33:13-132
206            android:exported="false" />
206-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a48f688fa386ce7aed0a486b8b5ba154\transformed\jetified-transport-runtime-2.2.6\AndroidManifest.xml:34:13-37
207    </application>
208
209</manifest>
