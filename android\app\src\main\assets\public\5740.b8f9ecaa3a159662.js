"use strict";(self.webpackChunkapp=self.webpackChunkapp||[]).push([[5740],{45740:(c,h,t)=>{t.r(h),t.d(h,{MenuAuthenticationModule:()=>m});var l=t(56610),i=t(77575),o=t(2978);const d=[{path:"",redirectTo:"signin",pathMatch:"full"},{path:"signin",loadChildren:()=>Promise.all([t.e(2076),t.e(4648)]).then(t.bind(t,24648)).then(n=>n.FormSigninPageModule)},{path:"reset-password",loadChildren:()=>Promise.all([t.e(2076),t.e(8131)]).then(t.bind(t,88131)).then(n=>n.FormResetPswrdPageModule)},{path:"signup",loadChildren:()=>Promise.all([t.e(9963),t.e(2076),t.e(8686)]).then(t.bind(t,78686)).then(n=>n.FormSignupAccountPageModule)}];let a=(()=>{class n{static{this.\u0275fac=function(s){return new(s||n)}}static{this.\u0275mod=o.$C({type:n})}static{this.\u0275inj=o.G2t({imports:[i.iI.forChild(d),i.iI]})}}return n})();var u=t(93887);let m=(()=>{class n{static{this.\u0275fac=function(s){return new(s||n)}}static{this.\u0275mod=o.$C({type:n})}static{this.\u0275inj=o.G2t({imports:[l.MD,a,u.G]})}}return n})()}}]);