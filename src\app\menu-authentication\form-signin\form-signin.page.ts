import { CommonService } from 'src/app/shared/services/common.service';
import { Router } from '@angular/router';
import { Component, Input, OnInit, OnDestroy } from '@angular/core';
import { Language } from 'src/app/shared/enum/language.enum';
import { <PERSON><PERSON><PERSON>ontroller, ModalController, Platform } from '@ionic/angular';
import { FormGroup, Validators, FormControl } from '@angular/forms';
import { ModalOtpInputComponent } from './modal-otp-input/modal-otp-input.component';
import { AuthenticationService } from 'src/app/shared/services/authentication.service';
import { CodeOtp, CredentialDto, CredentialOtpDto, } from 'src/app/shared/models/credential-dto';
import { TranslateConfigService } from 'src/app/shared/services/translate-config.service';
import { ModalSelectAccountComponent } from './modal-select-account/modal-select-account.component';
import { StorageService } from 'src/app/shared/services/storage.service';
import { ChangeLanguageComponent } from 'src/app/shared/components/change-language/change-language.component';
import { CompanyService } from 'src/app/menu-order/services/company.service';
import { UserCategory } from 'src/app/shared/enum/user-category.enum';
import { BaseUser } from 'src/app/shared/models/user.models';
import { OtpTestService } from 'src/app/shared/services/otp-test.service';
import { environment } from 'src/environments/environment';

@Component({
  selector: 'app-form-signin',
  templateUrl: './form-signin.page.html',
  styleUrls: ['./form-signin.page.scss'],
})
export class FormSigninPage implements OnInit, OnDestroy {
  tabOption: string = 'phone';
  credential: CredentialDto;
  code: CodeOtp;
  @Input() credentialOtp: CredentialOtpDto;

  // Propriétés pour l'auto-fill OTP
  isListening: boolean = false;
  otpAutoFillEnabled: boolean = true;

  loginForm: FormGroup = new FormGroup({
    email: new FormControl('', [Validators.email, Validators.min(3)]),
    phone: new FormControl('', [Validators.min(8)]),
    password: new FormControl('', [Validators.min(3), Validators.required]),
  });

  loginOtpForm: FormGroup = new FormGroup({
    phone: new FormControl('', [Validators.required, Validators.minLength(12)]),
  });

  otpForm = new FormGroup({
    code: new FormControl('', [Validators.required]),
  });

  isLoading: boolean = false;

  typeOfPassword: boolean;
  isOtpGenerate: boolean = false;

  constructor(
    private router: Router,
    private modalCtrl: ModalController,
    private storageSrv: StorageService,
    private commonSrv: CommonService,
    private companySrv: CompanyService,
    private authService: AuthenticationService,
    private platform: Platform,
    private otpTestService: OtpTestService
  ) { }

  ngOnInit(): void {
    if (this.storageSrv.getUserConnected() && this.commonSrv?.user?.accessToken
      && this.commonSrv.user.roles.includes('client')) {
      this.router.navigateByUrl('navigation');
    }
    console.log('otpForm', this.isOtpGenerate);
  }

  ngOnDestroy(): void {
    this.stopOtpAutoFill();
  }

  ionViewWillEnter(): void {
    this.loginForm.reset();
    this.checkValidator();
  }

  showPassword() {
    this.typeOfPassword = !this.typeOfPassword;
  }

  checkValidator(): void {
    this.loginForm
      .get('phone')
      .setValidators(
        this.tabOption === 'phone'
          ? [Validators.min(8), Validators.required]
          : []
      );
    this.loginForm.get('phone').updateValueAndValidity();
    this.loginForm
      .get('email')
      .setValidators(
        this.tabOption === 'email'
          ? [Validators.email, Validators.min(3), Validators.required]
          : []
      );
    this.loginForm.get('email').updateValueAndValidity();
  }

  changeTab(tab: string): void {
    if (!this.isLoading) {
      this.loginForm.reset();
      this.tabOption = tab;
      this.checkValidator();
    }
  }

  navigateTo(url: string): void {
    this.router.navigateByUrl(url);
  }

  async login(): Promise<void> {
    this.isLoading = true;
    if (this.tabOption === 'email') {
      this.credential = {
        email: this.loginForm.value.email,
        password: this.loginForm.value.password,
      };
      let response = await this.authService.login(this.credential);

      if (!(response instanceof Error) && response.accessToken && response.roles.includes('client')) {
        const currOnboarding = this.storageSrv.load('ONB_INFO');

        if (currOnboarding) {
          this.commonSrv.anboardingView = false;
          this.router.navigateByUrl('navigation');
        } else {
          const newOnboarding = {
            value: true,
          };
          this.storageSrv.store('ONB_INFO', JSON.stringify(newOnboarding));
          this.router.navigate(['navigation/home/<USER>']);
        }

        this.loginForm.reset();
      }
    }

    if (this.tabOption === 'phone') {
      const credentialOtp = {
        emailOrTel: this.loginOtpForm.value.phone.replace(/\D+/g, ''),
      };
      const res = await this.authService.generateOtp(credentialOtp);
      if ((!(res instanceof Error))) {
        this.isOtpGenerate = true;
        // Démarrer l'auto-fill OTP après génération
        this.startOtpAutoFill();
      }
    }
    this.isLoading = false;
  }
  async showModalOtp(credentialOtp?: CredentialOtpDto) {
    const modal = await this.modalCtrl.create({
      component: ModalOtpInputComponent,
      initialBreakpoint: 0.6,
      cssClass: 'modal',
      breakpoints: [0, 0.6, 0.7, 0.8, 0.85],
      mode: 'ios',
      componentProps: {
        credentialOtp: credentialOtp ?? null
      },
    });
    modal.present();
    const { data, role } = await modal.onWillDismiss();

  }

  async openModal(): Promise<void> {
    const modal = await this.modalCtrl.create({
      component: ModalSelectAccountComponent,
      initialBreakpoint: 0.5,
      cssClass: 'modal',
      breakpoints: [0, 0.5],
      mode: 'ios',
    });
    modal.present();

    const { data, role } = await modal.onWillDismiss();
  }

  async changeLanguage(): Promise<void> {
    const modal = await this.modalCtrl.create({
      component: ChangeLanguageComponent,
      cssClass: 'modalClass',
      componentProps: {
      },
    });

    await modal.present();

    const { role } = await modal.onDidDismiss();
  }


  verifyTel(tel: any): boolean {
    tel = `${tel}`.replace(/\s/g, '').replace(/-/g, '');

    if (/^6[0-9]{8}$/.test(tel)) {
      return true;
    } else {
      return false;
    }

  }
  async verifyOtp() {
    this.isLoading = true;
    this.code = {
      value: Number(this.otpForm.get('code').value),
    };

    const user = await this.authService.loginWhitOtp(this.code);

    if (user?.['category'] === UserCategory.CompanyUser) {
      const { data } = await this.companySrv.getCompanies({ users: user?.['_id'] });
      data?.length > 0 ? (data?.push(user['company']), user['associatedCompanies'] = data) : null
    }

    this.storageSrv.store('USER_INFO', JSON.stringify(user));

    if (!(user instanceof Error)) {

      // ✅ Réinitialisation toujours exécutée après un login réussi
      this.loginForm.reset();
      this.otpForm.reset();
      this.isOtpGenerate = false;
      this.isLoading = false;

      if (user?.category === UserCategory.Particular || user?.category === UserCategory.DonutAnimator)

        return this.router.navigateByUrl('navigation/home-alt');

      const currOnboarding = this.storageSrv.load('ONB_INFO');

      if (currOnboarding) {
        this.router.navigateByUrl('navigation/home');
      } else {
        const newOnboarding = {
          value: true,
        };
        this.commonSrv.anboardingView = false;
        this.storageSrv.store('ONB_INFO', JSON.stringify(newOnboarding));
        this.router.navigate(['navigation/home/<USER>']);
      }
      this.loginForm.reset();
      this.otpForm.reset();
      this.isOtpGenerate = false;
    }
    this.isLoading = false;
  }
  async goBackToPhoneInput() {
    this.stopOtpAutoFill(); // Arrêter l'auto-fill
    this.isOtpGenerate = false;
    this.otpForm.reset();
    this.loginOtpForm.get('phone').reset();
  }

  async resendCode() {
    this.isLoading = true;
    try {
      const credentialOtp = {
        emailOrTel: this.loginOtpForm.get('phone').value.replace(/\D+/g, ''),
      };
      const response = await this.authService.generateOtp(credentialOtp);
      if (!(response instanceof Error)) {
        this.commonSrv.showToast({
          color: 'success',
          icon: '',
          message: 'Otp renvoyé avec succes',
        });
      } else {
        this.commonSrv.showToast({
          color: 'danger',
          icon: '',
          message: 'Erreur lors du renvoi de l\'OTP',
        });
      }
    } catch (error) {
      console.error('Erreur lors du renvoi de l\'OTP:', error);
      this.commonSrv.showToast({
        color: 'danger',
        icon: '',
        message: 'erreur',
      });
    } finally {
      this.isLoading = false;
    }
  }

  // ===== MÉTHODES AUTO-FILL OTP =====

  startOtpAutoFill() {
    if (!this.otpAutoFillEnabled) return;

    console.log('[OTP-FORM] Initialisation de la lecture automatique...');
    console.log('[OTP-FORM] Environment production:', environment.production);
    console.log('[OTP-FORM] Platform:', this.platform.platforms());

    this.isListening = true;

    // Callback pour traiter l'OTP reçu
    const handleOtpReceived = (otp: string) => {
      console.log('[OTP-FORM] Code OTP détecté automatiquement :', otp);
      this.otpForm.get('code')?.setValue(otp);
      this.isListening = false;

      // Attendre un peu avant la connexion automatique pour que l'utilisateur voie le code
      setTimeout(() => {
        this.verifyOtp();
      }, 1500);
    };

    // Toujours démarrer l'auto-fill service (il gère les différentes plateformes)
    console.log('[OTP-FORM] Démarrage du service auto-fill...');
    this.authService.startOtpAutoFill(handleOtpReceived);

    // En mode développement, AUSSI simuler la réception d'OTP après un délai
    if (!environment.production) {
      console.log('[OTP-FORM] Mode développement - simulation activée');
      setTimeout(() => {
        if (this.isListening) {
          this.otpTestService.simulateOtpReceived(handleOtpReceived, 2000);
        }
      }, 1000);
    }

    // Arrêter l'écoute après 5 minutes
    setTimeout(() => {
      if (this.isListening) {
        this.stopOtpAutoFill();
        console.log('[OTP-FORM] Timeout - arrêt de l\'écoute automatique');
      }
    }, 5 * 60 * 1000);
  }

  stopOtpAutoFill() {
    if (this.isListening) {
      console.log('[OTP-FORM] Arrêt de la lecture automatique...');
      this.isListening = false;
      this.authService.stopOtpAutoFill();
    }
  }

  // Méthode de test pour forcer la simulation d'OTP (pour debug)
  testOtpSimulation() {
    if (!environment.production) {
      console.log('[OTP-FORM] Test manuel de simulation...');
      const testOtp = '123456';
      this.otpForm.get('code')?.setValue(testOtp);
      console.log('[OTP-FORM] Code de test inséré:', testOtp);
    }
  }

  toggleOtpAutoFill() {
    this.otpAutoFillEnabled = !this.otpAutoFillEnabled;
    if (this.otpAutoFillEnabled && this.isOtpGenerate) {
      this.startOtpAutoFill();
    } else {
      this.stopOtpAutoFill();
    }
  }
}
