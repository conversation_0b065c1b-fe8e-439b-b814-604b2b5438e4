@import "src/theme/mixins.scss";
@import "/src/assets/styles/abstracts/common.scss";

// Styles pour l'auto-detection OTP
.auto-detection-indicator {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin: 1rem 0;
  padding: 1rem;
  background: rgba(var(--ion-color-primary-rgb), 0.1);
  border-radius: 8px;

  ion-spinner {
    margin-bottom: 0.5rem;
  }
}

.otp-controls {
  display: flex;
  gap: 10px;
  justify-content: center;
  flex-wrap: wrap;

  ion-button {
    --padding-start: 8px;
    --padding-end: 8px;
    font-size: 12px;
  }
}

ion-content {
  &.scroll {
    --overflow: hidden;
  }

}

#container {
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  max-width: var(--content-max-width);
  padding: 0 calc(41 * var(--res));
  margin: 0 auto;

  .login {
    margin-top: 4vh;
    margin-bottom: 4vh;
    font-family: $font-bold;
    font-size: clamp(28px, 5vw, 38px);
    color: var(--clr-primary-800);
    font-weight: var(---mont-bold);
    text-align: center;
    font-size: 1.5rem;
    font-style: normal;
    font-weight: 800;
    line-height: normal;
    letter-spacing: -0.01031rem;
  }

  .message {
    &.login-message {
      width: 100%;
      display: flex;
      flex-direction: column;
      align-items: center;
      text-align: center;

      ion-text {
        display: block;
        margin-bottom: 3vh;
        font-size: 12px;
        color: var(--clr-primary-300);
        line-height: 1;
        text-align: center;
        font-size: 0.75rem;
        font-style: normal;
        font-weight: 600;
        line-height: normal;
        letter-spacing: -0.01031rem;

        &:last-of-type {
          color: var($color-fiftheen);
          margin-bottom: 3vh;
          font-size: clamp(16px, 3.5vw, 20px);
        }
      }

      ion-text.text-message {
        color: var(--clr-primary-700);
        margin-bottom: 4vh;
        font-size: var(--fs-14-px);
        font-weight: var(---mont-bold);
        font-family: 'Mont SemiBold' !important;
        text-align: center;
        font-size: 0.875rem;
        font-size: 0.875rem;
        font-style: normal;
        line-height: normal;
        letter-spacing: -0.01031rem;

      }

      .password-key {
        margin-bottom: 4vh;
        width: 60px;
        height: 60px;
      }


      .key-otp-bloc {
        // background-color: var(--clr-primary-700);
        // width: 61px;
        // height: 60px;
        // border-radius: 39%;
        // @include v-h-align;
        // box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        margin-bottom: 4vh;

        // .password-key {
        //   padding: .7rem;
        // }
      }
    }
  }

  .phone-input-container {
    width: 100%;

    .phone-input {
      @include h-center;
      // height: 2.6875rem;
      // flex-shrink: 0;
      // padding: var(--container-padding);
      // width: 100%;
      // max-width: 500px;
      // margin-bottom: 1vh;
      // border: var(--clr-primary-300);
      // border-style: solid;
      // border-radius: 5px;
      // border-width: 1px;

      .custom-input {
        border-radius: 0.375rem;
        border: 1px solid var(--bleu-main-color-n-140-white, #6D839D);
        background: transparent;
        text-align: center;
        color: $color-fiftheen;
        font-family: var(---mont-regular);
        font-size: var(--fs-18-px);
        width: 100%;
        padding: calc(26 * var(--res));
        letter-spacing: .12em;

        &::part(native) {
          padding: 12px;
        }
      }
    }

    input::placeholder {
      @include vh-center;
      color: $color-fouth;
      text-align: center;
      font-family: var(---mont-regular);
      font-size: var(--fs-18-px);
      letter-spacing: -0.01031rem;
    }
  }




  .change-language {
    text-align: center;
    margin-top: 4vh;
    font-family: $font-regular, "sans serif";
    font-size: clamp(12px, 2vw, 14px);
    text-decoration: underline;
    color: var(--clr-secondary-400);
  }

  .otp-actions {
    text-align: center;
    margin-top: 4vh;

    .no-otp {
      font-family: $font-regular, "sans serif";
      font-size: clamp(12px, 2vw, 14px);
      color: var(--clr-primary-300);
      margin-top: -1vh;

      .resend-otp {
        text-decoration: underline;

        a {
          color: var(--clr-secondary-300);

        }
      }
    }

    .bloc-elt {
      margin-top: 4vh;

      .change-phone {
        .change-phone-action {
          color: var(--clr-secondary-400);
          color: var(--clr-primary-700);
          font-family: $font-regular, "sans serif";
          font-size: clamp(12px, 2vw, 14px);
          text-decoration: underline;
          font-family: 'Mont SemiBold';
          font-weight: bold;
          cursor: pointer;

        }
      }
    }
  }


}

ion-item {
  div.item-native {
    border: none !important;
  }
}

.footer-md::before {
  background: none !important;
}

ion-footer {
  border: none;
  height: 7rem;
  display: flex;
  justify-content: center;
  align-items: center;


  .btn {
    padding: calc(41 * var(--res));
  }
}

@media (min-width: 768px) {
  :root {
    --container-padding: 8vw;
  }

  #container {
    justify-content: center;

    .login {
      margin-top: 0;

    }

  }

  ion-footer {
    height: 15rem;
    border: none;

    .phone-input,
    .btn {
      max-width: 600px;
      height: 42px;
    }
  }
}

@media (min-width: 1024px) {
  :root {
    --container-padding: 10vw;
    --content-max-width: 800px;
  }

  #container {
    padding: var(--container-padding);
    max-width: var(--content-max-width);

    .login {
      font-size: clamp(28px, 4vw, 36px);
      margin-top: 6vh;
      margin-bottom: 4vh;
    }

    .message {
      &.login-message {
        padding-left: 20px;
        padding-right: 20px;

        ion-text {
          font-size: clamp(16px, 2.5vw, 20px);
          margin-bottom: 3vh;
          padding: 0 1.8rem;

          &:last-of-type {
            font-size: clamp(18px, 3vw, 22px);
            margin-top: 3vh;
          }
        }

        .password-key {
          width: 80px;
          height: 80px;
          margin-top: 3vh;
          // margin-bottom: 3vh;
        }
      }
    }

    .phone-input {
      width: 100%;
      max-width: 500px;
      height: 54px;
      margin-bottom: 4vh;

      .custom-input {
        font-size: clamp(16px, 2.5vw, 20px);

        &::part(native) {
          padding: 16px;
        }
      }
    }
  }

  ion-footer {
    height: 25rem;
    border: none;

    .btn {
      max-width: 500px;
      height: 54px;

      ion-button {
        --padding-top: 18px;
        --padding-bottom: 18px;
        // font-size: clamp(16px, 2.5vw, 20px);
      }
    }
  }
}

@media (min-width: 768px) {
  :root {
    --container-padding: 8vw;
  }

  #container {
    justify-content: center;

    .login {
      margin-top: 0;

    }

  }

  ion-footer {
    height: 15rem;
    border: none;

    .phone-input,
    .btn {
      max-width: 600px;
      height: 42px;
    }
  }
}

@media (min-width: 1024px) {
  :root {
    --container-padding: 10vw;
    --content-max-width: 800px;
  }

  #container {
    padding: var(--container-padding);
    max-width: var(--content-max-width);

    .login {
      font-size: clamp(28px, 4vw, 36px);
      margin-top: 6vh;
      margin-bottom: 4vh;
    }

    .message {
      &.login-message {
        padding-left: 20px;
        padding-right: 20px;

        ion-text {
          font-size: clamp(16px, 2.5vw, 20px);
          margin-bottom: 3vh;

          &:last-of-type {
            font-size: clamp(18px, 3vw, 22px);
            margin-top: 3vh;
          }
        }

        .password-key {
          width: 80px;
          height: 80px;
          margin-top: 3vh;
          margin-bottom: 3vh;
        }
      }
    }

    .phone-input {
      width: 100%;
      max-width: 500px;
      height: 54px;
      margin-bottom: 4vh;

      .custom-input {
        font-size: clamp(16px, 2.5vw, 20px);

        &::part(native) {
          padding: 16px;
        }
      }
    }
  }

  ion-footer {
    height: 25rem;
    border: none;

    .btn {
      max-width: 500px;
      height: 54px;
    }
  }
}