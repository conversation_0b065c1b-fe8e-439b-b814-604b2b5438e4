import { Component, Input, OnInit, OnDestroy } from '@angular/core';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { ModalController, Platform } from '@ionic/angular';
import { CodeOtp, CredentialOtpDto } from 'src/app/shared/models/credential-dto';
import { AuthenticationService } from 'src/app/shared/services/authentication.service';
import { OtpTestService } from 'src/app/shared/services/otp-test.service';
import { environment } from 'src/environments/environment';

@Component({
  selector: 'app-modal-otp-input',
  templateUrl: './modal-otp-input.component.html',
  styleUrls: ['./modal-otp-input.component.scss'],
})
export class ModalOtpInputComponent implements OnInit, OnDestroy {
  isLoading: boolean = false;
  isListening: boolean = false; // Pour gérer le loader de lecture OTP
  code: CodeOtp;
  @Input() credentialOtp: CredentialOtpDto;

  loginForm = new FormGroup({
    code: new FormControl('', [Validators.required]),
  });

  constructor(
    private modalCtrl: ModalController,
    private authService: AuthenticationService,
    private router: Router,
    private platform: Platform,
    private otpTestService: OtpTestService
  ) { }

  ngOnInit() {
    // Démarrer la lecture automatique
    this.startListeningForOtp();

    // En mode développement, afficher les instructions de test
    if (!environment.production) {
      this.otpTestService.showTestInstructions();
    }
  }

  ngOnDestroy() {
    this.stopListeningForOtp();
  }

  closeModal() {
    this.stopListeningForOtp();
    this.modalCtrl.dismiss();
  }

  async login(): Promise<void> {
    this.isLoading = true;
    this.code = {
      value: Number(this.loginForm.get('code').value),
    };

    const response = await this.authService.loginWhitOtp(this.code);

    if (!(response instanceof Error)) {
      this.router.navigateByUrl('navigation');
      this.loginForm.reset();
      this.modalCtrl.dismiss();
    }
    this.isLoading = false;
  }

  async resendCode() {
    this.isLoading = true;
    await this.authService.generateOtp(this.credentialOtp);
    this.isLoading = false;
  }

  startListeningForOtp() {
    console.log('[OTP] Initialisation de la lecture automatique...');
    console.log('[OTP] Environment production:', environment.production);
    console.log('[OTP] Platform:', this.platform.platforms());

    this.isListening = true;

    // Callback pour traiter l'OTP reçu
    const handleOtpReceived = (otp: string) => {
      console.log('[OTP] Code OTP détecté automatiquement :', otp);
      this.loginForm.get('code')?.setValue(otp);
      this.isListening = false;

      // Attendre un peu avant la connexion automatique pour que l'utilisateur voie le code
      setTimeout(() => {
        this.login();
      }, 1500);
    };

    // Toujours démarrer l'auto-fill service (il gère les différentes plateformes)
    console.log('[OTP] Démarrage du service auto-fill...');
    this.authService.startOtpAutoFill(handleOtpReceived);

    // En mode développement, AUSSI simuler la réception d'OTP après un délai
    if (!environment.production) {
      console.log('[OTP] Mode développement - simulation activée');
      setTimeout(() => {
        if (this.isListening) {
          this.otpTestService.simulateOtpReceived(handleOtpReceived, 2000);
        }
      }, 1000);
    }

    // Arrêter l'écoute après 5 minutes
    setTimeout(() => {
      if (this.isListening) {
        this.stopListeningForOtp();
        console.log('[OTP] Timeout - arrêt de l\'écoute automatique');
      }
    }, 5 * 60 * 1000);
  }

  stopListeningForOtp() {
    if (this.isListening) {
      console.log('[OTP] Arrêt de la lecture automatique...');
      this.isListening = false;
      this.authService.stopOtpAutoFill();
    }
  }

  // Méthode de test pour forcer la simulation d'OTP (pour debug)
  testOtpSimulation() {
    if (!environment.production) {
      console.log('[OTP] Test manuel de simulation...');
      const testOtp = '123456';
      this.loginForm.get('code')?.setValue(testOtp);
      console.log('[OTP] Code de test inséré:', testOtp);
    }
  }

}