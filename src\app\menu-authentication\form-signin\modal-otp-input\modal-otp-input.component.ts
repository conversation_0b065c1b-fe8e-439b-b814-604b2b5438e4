import { Component, Input, OnInit } from '@angular/core';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { ModalController } from '@ionic/angular';
import { CodeOtp, CredentialOtpDto } from 'src/app/shared/models/credential-dto';
import { AuthenticationService } from 'src/app/shared/services/authentication.service';

@Component({
  selector: 'app-modal-otp-input',
  templateUrl: './modal-otp-input.component.html',
  styleUrls: ['./modal-otp-input.component.scss'],
})
export class ModalOtpInputComponent implements OnInit {
  isLoading: boolean = false;
  code: CodeOtp;
  @Input() credentialOtp: CredentialOtpDto;

  loginForm = new FormGroup({
    code: new FormControl('', [Validators.required]),
  });

  constructor(
    private modalCtrl: ModalController,
    private authService: AuthenticationService,
    private router: Router
  ) { }

  ngOnInit() {
  }

  closeModal() {
    this.modalCtrl.dismiss();
  }

  async login(): Promise<void> {
    this.isLoading = true;
    this.code = {
      value: Number(this.loginForm.get('code').value),
    };

    const response = await this.authService.loginWhitOtp(this.code);

    if (!(response instanceof Error)) {
      this.router.navigateByUrl('navigation');
      this.loginForm.reset();
      this.modalCtrl.dismiss();
    }
    this.isLoading = false;
  }

  async resendCode() {
    this.isLoading = true;
    await this.authService.generateOtp(this.credentialOtp);
    this.isLoading = false;
  }

}