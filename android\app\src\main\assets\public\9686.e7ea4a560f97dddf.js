"use strict";(self.webpackChunkapp=self.webpackChunkapp||[]).push([[9686],{19686:(Y,w,g)=>{g.r(w),g.d(w,{MyAccountPageModule:()=>Ot});var l=g(77897),a=g(56610),f=g(74657),E=g(93887),r=g(37222),P=g(77575),u=g(73308),v=g(99987),R=g(58133),x=g(63037),F=g(91285),n=g(2978),_=g(82571),C=g(93860),d=g(62049),s=g(23985),m=g(14599),M=g(26409),y=g(71333);function I(e,c){if(1&e&&(n.j41(0,"div",20),n.nrm(1,"ion-icon",21),n.j41(2,"div",22)(3,"ion-label"),n.EFF(4," Mes points: "),n.j41(5,"span"),n.EFF(6),n.k0s()()()()),2&e){const t=n.XpG();n.R7$(6),n.JRh((null==t.commonService.user?null:t.commonService.user.points)||"N/A")}}function j(e,c){if(1&e){const t=n.RV6();n.j41(0,"ion-icon",23),n.bIt("click",function(){n.eBV(t);const o=n.XpG();return n.Njj(o.changeToEdit(!o.isEdit))}),n.k0s()}2&e&&n.Y8G("src","/assets/icons/edit-outline.svg")}function T(e,c){if(1&e){const t=n.RV6();n.j41(0,"ion-icon",23),n.bIt("click",function(){n.eBV(t);const o=n.XpG();return n.Njj(o.undo())}),n.k0s()}2&e&&n.Y8G("src","/assets/icons/undo-outline.svg")}function N(e,c){1&e&&n.nrm(0,"app-progress-spinner")}function $(e,c){if(1&e&&(n.j41(0,"div",9),n.nrm(1,"ion-icon",24),n.j41(2,"div",11)(3,"ion-label"),n.EFF(4),n.nI1(5,"translate"),n.k0s(),n.nrm(6,"ion-input",25),n.k0s()()),2&e){const t=n.XpG();n.R7$(4),n.JRh(n.bMT(5,2,"profile.retailer.input-name-label")),n.R7$(2),n.Y8G("value",(null==t.commonService.user?null:t.commonService.user.lastName)+" "+(null==t.commonService.user?null:t.commonService.user.firstName))}}function G(e,c){1&e&&(n.j41(0,"div",9),n.nrm(1,"ion-icon",24),n.j41(2,"div",11)(3,"ion-label"),n.EFF(4),n.nI1(5,"translate"),n.k0s(),n.nrm(6,"ion-input",26),n.k0s()()),2&e&&(n.R7$(4),n.JRh(n.bMT(5,1,"profile.retailer.input-lastName-label")))}function J(e,c){1&e&&(n.j41(0,"div",9),n.nrm(1,"ion-icon",24),n.j41(2,"div",11)(3,"ion-label"),n.EFF(4),n.nI1(5,"translate"),n.k0s(),n.nrm(6,"ion-input",27),n.k0s()()),2&e&&(n.R7$(4),n.JRh(n.bMT(5,1,"profile.retailer.input-firstName-label")))}function D(e,c){if(1&e&&(n.j41(0,"div",9),n.nrm(1,"ion-icon",28),n.j41(2,"div",11)(3,"ion-label"),n.EFF(4),n.nI1(5,"translate"),n.k0s(),n.nrm(6,"ion-input",29),n.nI1(7,"translate"),n.k0s()()),2&e){const t=n.XpG();n.R7$(4),n.JRh(n.bMT(5,4,"profile.retailer.input-social-reason-label")),n.R7$(2),n.FS9("placeholder",n.bMT(7,6,"profile.retailer.input-social-reason-placeholder")),n.Y8G("readonly",!t.isEdit)("value",null==t.commonService.user?null:t.commonService.user.socialReason)}}function U(e,c){if(1&e&&(n.j41(0,"div",9),n.nrm(1,"ion-icon",30),n.j41(2,"div",11)(3,"ion-label"),n.EFF(4),n.nI1(5,"translate"),n.k0s(),n.nrm(6,"ion-input",25),n.k0s()()),2&e){const t=n.XpG();n.R7$(4),n.JRh(n.bMT(5,4,"profile.retailer.input-address-label")),n.R7$(2),n.yjJ("value","",null==t.commonService.user||null==t.commonService.user.address?null:t.commonService.user.address.region,", ",null==t.commonService.user||null==t.commonService.user.address?null:t.commonService.user.address.city," - ",null==t.commonService.user||null==t.commonService.user.address?null:t.commonService.user.address.district,"")}}function X(e,c){if(1&e&&(n.j41(0,"ion-select-option",25),n.EFF(1),n.k0s()),2&e){const t=c.$implicit;n.FS9("value",t),n.R7$(1),n.SpI("",t," ")}}function z(e,c){if(1&e&&(n.j41(0,"div",31),n.nrm(1,"ion-icon",30),n.j41(2,"div",11)(3,"ion-label"),n.EFF(4),n.nI1(5,"translate"),n.k0s(),n.j41(6,"ion-select",32,33),n.DNE(8,X,2,2,"ion-select-option",34),n.k0s()()()),2&e){const t=n.XpG();n.R7$(4),n.JRh(n.bMT(5,2,"profile.retailer.select-region-label")),n.R7$(4),n.Y8G("ngForOf",t.commonService.getRegions())}}function B(e,c){if(1&e&&(n.j41(0,"ion-select-option",25),n.EFF(1),n.k0s()),2&e){const t=c.$implicit;n.FS9("value",t),n.R7$(1),n.SpI("",t," ")}}function L(e,c){if(1&e&&(n.j41(0,"div",31),n.nrm(1,"ion-icon",30),n.j41(2,"div",11)(3,"ion-label"),n.EFF(4),n.nI1(5,"translate"),n.k0s(),n.j41(6,"ion-select",35),n.DNE(7,B,2,2,"ion-select-option",34),n.k0s()()()),2&e){const t=n.XpG();n.R7$(4),n.JRh(n.bMT(5,2,"profile.retailer.select-city-label")),n.R7$(3),n.Y8G("ngForOf",t.commonService.getCities(t.userForm.get("address").get("region").value))}}function V(e,c){1&e&&(n.j41(0,"div",31),n.nrm(1,"ion-icon",30),n.j41(2,"div",11)(3,"ion-label"),n.EFF(4),n.nI1(5,"translate"),n.k0s(),n.nrm(6,"ion-input",36),n.k0s()()),2&e&&(n.R7$(4),n.JRh(n.bMT(5,1,"profile.retailer.input-district-label")))}function q(e,c){if(1&e&&(n.j41(0,"div",9),n.nrm(1,"ion-icon",14),n.j41(2,"div",11)(3,"ion-label"),n.EFF(4,"Email"),n.k0s(),n.nrm(5,"ion-input",37),n.k0s()()),2&e){const t=n.XpG();n.R7$(5),n.Y8G("readonly",!t.isEdit)}}function K(e,c){if(1&e&&n.nrm(0,"ion-input",38),2&e){const t=n.XpG();n.Y8G("readonly",!t.editPassword)}}function W(e,c){if(1&e){const t=n.RV6();n.j41(0,"div",39),n.bIt("click",function(){n.eBV(t);const o=n.XpG();return n.Njj(o.activeChangepassword())}),n.j41(1,"ion-text"),n.EFF(2,"******"),n.k0s(),n.j41(3,"ion-icon",40),n.bIt("click",function(){n.eBV(t);const o=n.XpG();return n.Njj(o.changeToEdit(!o.isEdit))}),n.k0s()()}}function H(e,c){if(1&e){const t=n.RV6();n.j41(0,"ion-button",41),n.bIt("click",function(){n.eBV(t);const o=n.XpG();return n.Njj(o.update())}),n.j41(1,"ion-label"),n.EFF(2),n.nI1(3,"translate"),n.k0s()()}2&e&&(n.R7$(2),n.SpI(" ",n.bMT(3,1,"profile.retailer.save-button-label")," "))}function Z(e,c){if(1&e){const t=n.RV6();n.j41(0,"ion-button",41),n.bIt("click",function(){n.eBV(t);const o=n.XpG();return n.Njj(o.changePassword())}),n.j41(1,"ion-label"),n.EFF(2),n.nI1(3,"translate"),n.k0s()()}2&e&&(n.R7$(2),n.SpI(" ",n.bMT(3,1,"profile.retailer.save-button-label")," "))}const Q=function(e){return{mbottom250:e}};let nn=(()=>{class e{constructor(t,i,o,p){this.userService=t,this.commonService=i,this.storageService=o,this.translateService=p,this.userCategory=R.s,this.typeOfPassword=!1,this.editPassword=!1,this.userForm=new r.gE({firstName:new r.MJ("",[r.k0.required]),lastName:new r.MJ("",[r.k0.required]),cni:new r.MJ("",[r.k0.required]),nui:new r.MJ("",[r.k0.required]),tel:new r.MJ("",[r.k0.required,r.k0.min(8)]),email:new r.MJ("",[r.k0.required,r.k0.min(3),r.k0.email]),socialReason:new r.MJ("",[r.k0.required]),password:new r.MJ("",[r.k0.required]),address:new r.gE({city:new r.MJ("",[r.k0.required]),district:new r.MJ("",[r.k0.required]),region:new r.MJ("",[r.k0.required])})}),this.storageService?.getUserConnected()}ngOnInit(){this.userForm.patchValue({firstName:this.commonService.user?.firstName,lastName:this.commonService.user?.lastName,cni:this.commonService.user?.cni,nui:this.commonService.user?.nui,tel:this.commonService.user?.tel,email:this.commonService.user?.email,socialReason:this.commonService.user?.company?.name,address:{city:this.commonService.user?.address?.city,district:this.commonService.user?.address?.district,region:this.commonService.user?.address?.region}}),this.balance={date:new Date,amount:5e8}}changeToEdit(t){this.isEdit=t,this.commonService.editUser=t}undo(){this.isEdit=this.editPassword=!1,this.commonService.editUser=!1}changePassword(){var t=this;return(0,u.A)(function*(){t.isLoading=!0;const i=yield t.userService.changePassword(t.userForm.get("password").value);yield t.commonService.showToast({message:`${i.message}`,color:200!==i?.status?"danger":"success"}),t.editPassword=!1,t.isLoading=!1,t.commonService.editUser=!1})()}activeChangepassword(){this.editPassword=!0,this.isEdit=!1,this.commonService.editUser=!0}update(){var t=this;return(0,u.A)(function*(){t.isLoading=!0;const i={...t.userForm?.value};delete i.address,delete i.nui,delete i.lastName,delete i.password,delete i.socialReason,i.tel=`${i.tel}`;const o=t.commonService.verifyAllFieldsForm(i,t.translateService.currentLang);if(o)return yield t.commonService.showToast({message:`${o}`,color:"success"}),t.isLoading=!1;if(!((yield t.userService.updateUser({...t.userForm?.value}))instanceof M.yz)){const O=yield t.userService.find(t.commonService.user._id),{accessToken:h,company:b}=t.commonService.user;let k={accessToken:h,...O};b&&(k.company=b),t.storageService.store("USER_INFO",JSON.stringify(k)),t.storageService.getUserConnected(),t.isEdit=!1,t.commonService.editUser=!1}return t.isLoading=!1})()}static{this.\u0275fac=function(i){return new(i||e)(n.rXU(s.D),n.rXU(_.h),n.rXU(m.n),n.rXU(d.E))}}static{this.\u0275cmp=n.VBU({type:e,selectors:[["app-retailer"]],decls:44,vars:34,consts:[[1,"content"],["class","edit-btn-container-retail",4,"ngIf"],[1,"edit-btn-container"],[3,"click"],["color","primary","class","icon-home",3,"src","click",4,"ngIf"],[4,"ngIf"],[3,"formGroup","ngClass"],[1,"input-group"],["class","form-group",4,"ngIf"],[1,"form-group"],["slot","start","src","/assets/icons/1-phone_iphone.svg",1,"icon-elt"],[1,"field-info"],["formControlName","tel","clearInput","",3,"readonly"],["class","form-group","formGroupName","address",4,"ngIf"],["src","/assets/icons/1-mail.svg",1,"icon-elt"],["formControlName","cni","type","text","clearInput","",3,"readonly"],["slot","start","src","/assets/icons/1-https.svg",1,"icon-elt"],["placeholder","Password","formControlName","password","type","password","clearInput","",3,"readonly",4,"ngIf"],["class","password",3,"click",4,"ngIf"],["class","btn mbottom250 btn--meduim btn--upper","type","submit","color","primary","expand","block",3,"click",4,"ngIf"],[1,"edit-btn-container-retail"],["name","person-circle-outline",1,"account"],[1,"edit-feilds"],["color","primary",1,"icon-home",3,"src","click"],["slot","start","src","/assets/icons/1-person.svg",1,"icon-elt"],[3,"value"],["formControlName","lastName","clearInput",""],["clearInput","","formControlName","firstName"],["slot","start","src","/assets/icons/1-location_city.svg",1,"icon-elt"],["formControlName","socialReason","clearInput","false",3,"readonly","value","placeholder"],["src","/assets/icons/1-pin.svg",1,"icon-elt"],["formGroupName","address",1,"form-group"],["mode","ios","formControlName","region","interface","action-sheet","cancelText","Annuler"],["region",""],[3,"value",4,"ngFor","ngForOf"],["mode","ios","formControlName","city","cancelText","Annuler","interface","action-sheet"],["formControlName","district","clearInput","","placeholder","Entrer votre quartier"],["formControlName","email","type","email","clearInput","",3,"readonly"],["placeholder","Password","formControlName","password","type","password","clearInput","",3,"readonly"],[1,"password",3,"click"],["color","primary","src","/assets/icons/edit-outline.svg",3,"click"],["type","submit","color","primary","expand","block",1,"btn","mbottom250","btn--meduim","btn--upper",3,"click"]],template:function(i,o){1&i&&(n.j41(0,"div",0),n.DNE(1,I,7,1,"div",1),n.j41(2,"div",2)(3,"ion-label",3),n.bIt("click",function(){return o.changeToEdit(!o.isEdit)}),n.EFF(4),n.nI1(5,"translate"),n.nI1(6,"translate"),n.k0s(),n.DNE(7,j,1,1,"ion-icon",4),n.DNE(8,T,1,1,"ion-icon",4),n.k0s(),n.DNE(9,N,1,0,"app-progress-spinner",5),n.j41(10,"form",6)(11,"div",7),n.DNE(12,$,7,4,"div",8),n.DNE(13,G,7,3,"div",8),n.DNE(14,J,7,3,"div",8),n.DNE(15,D,8,8,"div",8),n.j41(16,"div",9),n.nrm(17,"ion-icon",10),n.j41(18,"div",11)(19,"ion-label"),n.EFF(20),n.nI1(21,"translate"),n.k0s(),n.nrm(22,"ion-input",12),n.k0s()(),n.DNE(23,U,7,6,"div",8),n.DNE(24,z,9,4,"div",13),n.DNE(25,L,8,4,"div",13),n.DNE(26,V,7,3,"div",13),n.DNE(27,q,6,1,"div",8),n.j41(28,"div",9),n.nrm(29,"ion-icon",14),n.j41(30,"div",11)(31,"ion-label"),n.EFF(32,"CNI"),n.k0s(),n.nrm(33,"ion-input",15),n.k0s()(),n.j41(34,"div",9),n.nrm(35,"ion-icon",16),n.j41(36,"div",11)(37,"ion-label"),n.EFF(38),n.nI1(39,"translate"),n.k0s(),n.DNE(40,K,1,1,"ion-input",17),n.DNE(41,W,4,0,"div",18),n.k0s()()()(),n.DNE(42,H,4,3,"ion-button",19),n.DNE(43,Z,4,3,"ion-button",19),n.k0s()),2&i&&(n.R7$(1),n.Y8G("ngIf",(null==o.commonService.user?null:o.commonService.user.category)===o.userCategory.Retailer),n.R7$(3),n.SpI(" ",o.commonService.editUser?n.bMT(5,24,"profile.retailer.cancel"):n.bMT(6,26,"profile.retailer.edit")," "),n.R7$(3),n.Y8G("ngIf",!o.isEdit&&!o.editPassword),n.R7$(1),n.Y8G("ngIf",o.isEdit||o.editPassword),n.R7$(1),n.Y8G("ngIf",o.isLoading),n.R7$(1),n.Y8G("formGroup",o.userForm)("ngClass",n.eq3(32,Q,!o.isEdit)),n.R7$(2),n.Y8G("ngIf",!o.isEdit),n.R7$(1),n.Y8G("ngIf",o.isEdit),n.R7$(1),n.Y8G("ngIf",o.isEdit),n.R7$(1),n.Y8G("ngIf",!o.isEdit&&(null==o.commonService.user?null:o.commonService.user.socialReason)),n.R7$(5),n.JRh(n.bMT(21,28,"profile.retailer.input-phone-label")),n.R7$(2),n.Y8G("readonly",!o.isEdit),n.R7$(1),n.Y8G("ngIf",!o.isEdit),n.R7$(1),n.Y8G("ngIf",o.isEdit),n.R7$(1),n.Y8G("ngIf",o.isEdit),n.R7$(1),n.Y8G("ngIf",o.isEdit),n.R7$(1),n.Y8G("ngIf",!o.isEdit),n.R7$(6),n.Y8G("readonly",!o.isEdit),n.R7$(5),n.JRh(n.bMT(39,30,"profile.retailer.input-password-label")),n.R7$(2),n.Y8G("ngIf",o.editPassword),n.R7$(1),n.Y8G("ngIf",!o.editPassword),n.R7$(1),n.Y8G("ngIf",o.isEdit),n.R7$(1),n.Y8G("ngIf",o.editPassword))},dependencies:[r.qT,r.BC,r.cb,l.Jm,l.iq,l.$w,l.he,l.Nm,l.Ip,l.IO,l.Je,l.Gw,a.YU,a.Sq,a.bT,y._,r.j4,r.JD,r.$R,f.D9],styles:["*[_ngcontent-%COMP%]{margin:0;padding:0}.header-md[_ngcontent-%COMP%]:after{background-image:none!important}.modal[_ngcontent-%COMP%]{--border-radius: calc(60 * var(--res))}.input-group[_ngcontent-%COMP%]{margin-top:calc(75 * var(--res))}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{--padding-start: 0;margin-bottom:calc(50 * var(--res));--placeholder-color: $color-six}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-input[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{font-family:Mont Regular;font-weight:400;font-size:calc(42 * var(--res));color:#1e1e1e}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{padding-top:calc(25 * var(--res));padding-bottom:calc(25 * var(--res))}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]{margin-bottom:calc(50 * var(--res));display:flex;align-items:center;justify-content:space-between;border-bottom-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-250, #c8c7cc)));border-bottom-width:.55px;border-bottom-style:solid}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{width:90%;margin-bottom:0;--border-color: transparent;--inner-border-width: 0px 0px 0px 0px;--border-style: none}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-checkbox[_ngcontent-%COMP%]{margin-top:5%}.bg-primary[_ngcontent-%COMP%]{color:#143c5d}.message[_ngcontent-%COMP%]{margin-bottom:calc(75 * var(--res))}.message[_ngcontent-%COMP%]   ion-title[_ngcontent-%COMP%]{font-weight:800;font-size:calc(70 * var(--res));padding-bottom:calc(25 * var(--res))}.message[_ngcontent-%COMP%]   ion-text[_ngcontent-%COMP%]{font-weight:400;color:#757474;display:block;line-height:1.5em!important}.btn--meduim[_ngcontent-%COMP%]{height:calc(130 * var(--res));font-size:calc(50 * var(--res));font-family:var(--mont-bold)}.btn--meduim[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:var(--mont-bold)}.btn--small[_ngcontent-%COMP%]{height:calc(100 * var(--res))}.btn--round[_ngcontent-%COMP%]{--border-radius: 50%;padding-inline-start:0;padding-inline-end:0;--padding-start: 0;--padding-end: 0}.btn--border-primary[_ngcontent-%COMP%]{--background: transparent;--background-activated: #c4e6d0;--border-color: var(--ion-color-primary);--border-style: solid;--border-width: 2px}.btn--border-secondary[_ngcontent-%COMP%]{--border-width: 2px;--background: transparent;--background-activated: #ddabb2;--border-color: var(--ion-color-secondary);--border-style: solid}.btn--upper[_ngcontent-%COMP%]{text-transform:uppercase}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]{overflow:hidden}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar{width:5px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-track{box-shadow:inset 0 0 5px #546e8d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background:#143c5d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover{background:#143c5d}.bottom-sheet-content[_ngcontent-%COMP%]{background:#fdfdfe;height:100%}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-thumbnail[_ngcontent-%COMP%]{margin-left:calc(31.25 * var(--res));background-color:#1e1e1e;--border-radius: 50%;--size: 23}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:Mont Bold;font-size:calc(45 * var(--res));text-align:center;color:#757474}.bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%], .bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]{padding:calc(75 * var(--res))}.font-size-5[_ngcontent-%COMP%]{font-size:calc(42 * var(--res))}.content[_ngcontent-%COMP%]   .account-amount-container[_ngcontent-%COMP%]{padding:calc(41 * var(--res));padding-bottom:0}.content[_ngcontent-%COMP%]   .account-amount-container[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{display:block}.content[_ngcontent-%COMP%]   .account-amount-container[_ngcontent-%COMP%]   .account-amount-value[_ngcontent-%COMP%]{font-family:Mont Bold;font-size:calc(100 * var(--res))}.content[_ngcontent-%COMP%]   .edit-btn-container[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:flex-end;margin-top:1rem;padding-right:calc(41 * var(--res))}.content[_ngcontent-%COMP%]   .edit-btn-container-retail[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:.5em;justify-content:center;align-items:center;margin-top:1rem;padding-right:calc(41 * var(--res))}.content[_ngcontent-%COMP%]   .edit-btn-container-retail[_ngcontent-%COMP%]   .account[_ngcontent-%COMP%]{height:5rem;width:5rem;color:#143c5d}.content[_ngcontent-%COMP%]   .edit-btn-container-retail[_ngcontent-%COMP%]   .edit-feilds[_ngcontent-%COMP%]{display:flex;justify-content:center;align-items:center}.content[_ngcontent-%COMP%]   .edit-btn-container-retail[_ngcontent-%COMP%]   .edit-feilds[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:Mont Regular;font-weight:400;font-size:calc(42 * var(--res))}.content[_ngcontent-%COMP%]   .edit-btn-container-retail[_ngcontent-%COMP%]   .edit-feilds[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{color:#143c5d}.content[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]{padding:calc(41 * var(--res));display:flex;align-items:center;justify-content:space-between;border-bottom-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-250, #c8c7cc)));border-bottom-width:.55px;border-bottom-style:solid}.content[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   .field-info[_ngcontent-%COMP%]{width:90%;margin-bottom:0;display:flex;align-items:center;justify-content:space-between}.content[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   .field-info[_ngcontent-%COMP%]   ion-input[_ngcontent-%COMP%], .content[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   .field-info[_ngcontent-%COMP%]   ion-text[_ngcontent-%COMP%], .content[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   .field-info[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%], .content[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   .field-info[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{font-family:Mont Regular;font-weight:400;font-size:calc(42 * var(--res));color:#1e1e1e}.content[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   .field-info[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{margin-right:calc(37.5 * var(--res));font-family:Mont Bold}.content[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   .field-info[_ngcontent-%COMP%]   ion-input[_ngcontent-%COMP%]{text-align:right;flex-direction:row-reverse}.content[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   .field-info[_ngcontent-%COMP%]   ion-input[_ngcontent-%COMP%], .content[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   .field-info[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%], .content[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   .field-info[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{color:#1e1e1e}.content[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   .field-info[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{padding-top:calc(25 * var(--res));padding-bottom:calc(25 * var(--res))}.content[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   .field-info[_ngcontent-%COMP%]   .password[_ngcontent-%COMP%]{padding-top:10px;padding-bottom:10px;display:flex;align-items:center;justify-content:space-between}.content[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   .field-info[_ngcontent-%COMP%]   .password[_ngcontent-%COMP%]   ion-text[_ngcontent-%COMP%]:last-child{color:#143c5d}.content[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   ion-checkbox[_ngcontent-%COMP%]{margin-top:5%}.content[_ngcontent-%COMP%]   .mbottom250[_ngcontent-%COMP%]{margin-bottom:calc(50 * var(--res))}.content[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]{padding:0 calc(41 * var(--res));margin-top:calc(50 * var(--res))}"]})}}return e})();function tn(e,c){if(1&e){const t=n.RV6();n.j41(0,"ion-icon",21),n.bIt("click",function(){n.eBV(t);const o=n.XpG();return n.Njj(o.changeToEdit())}),n.k0s()}2&e&&n.Y8G("src","/assets/icons/edit-outline.svg")}function en(e,c){if(1&e){const t=n.RV6();n.j41(0,"ion-icon",21),n.bIt("click",function(){n.eBV(t);const o=n.XpG();return n.Njj(o.undo())}),n.k0s()}2&e&&n.Y8G("src","/assets/icons/undo-outline.svg")}function on(e,c){1&e&&n.nrm(0,"app-progress-spinner")}function rn(e,c){if(1&e&&(n.j41(0,"div",10),n.nrm(1,"ion-icon",22),n.j41(2,"div",12)(3,"ion-label"),n.EFF(4),n.nI1(5,"translate"),n.k0s(),n.nrm(6,"ion-input",23),n.k0s()()),2&e){const t=n.XpG();n.R7$(4),n.JRh(n.bMT(5,3,"profile.retailer.input-name-label")),n.R7$(2),n.Y8G("readonly",!0)("value",(null==t.commonService.user?null:t.commonService.user.lastName)+" "+(null==t.commonService.user?null:t.commonService.user.firstName))}}function cn(e,c){1&e&&(n.j41(0,"div",10),n.nrm(1,"ion-icon",22),n.j41(2,"div",12)(3,"ion-label"),n.EFF(4),n.nI1(5,"translate"),n.k0s(),n.nrm(6,"ion-input",24),n.k0s()()),2&e&&(n.R7$(4),n.JRh(n.bMT(5,1,"profile.retailer.input-lastName-label")))}function an(e,c){1&e&&(n.j41(0,"div",10),n.nrm(1,"ion-icon",22),n.j41(2,"div",12)(3,"ion-label"),n.EFF(4),n.nI1(5,"translate"),n.k0s(),n.nrm(6,"ion-input",25),n.k0s()()),2&e&&(n.R7$(4),n.JRh(n.bMT(5,1,"profile.retailer.input-firstName-label")))}function sn(e,c){if(1&e&&(n.j41(0,"div",10),n.nrm(1,"ion-icon",26),n.j41(2,"div",12)(3,"ion-label"),n.EFF(4),n.nI1(5,"translate"),n.k0s(),n.nrm(6,"ion-input",27),n.k0s()()),2&e){const t=n.XpG();n.R7$(4),n.JRh(n.bMT(5,4,"profile.retailer.input-address-label")),n.R7$(2),n.yjJ("value","",null==t.commonService.user||null==t.commonService.user.address?null:t.commonService.user.address.region,", ",null==t.commonService.user||null==t.commonService.user.address?null:t.commonService.user.address.city," - ",null==t.commonService.user||null==t.commonService.user.address?null:t.commonService.user.address.district,"")}}function ln(e,c){if(1&e&&(n.j41(0,"div",10),n.nrm(1,"ion-icon",26),n.j41(2,"div",12)(3,"ion-label"),n.EFF(4),n.nI1(5,"translate"),n.k0s(),n.nrm(6,"ion-input",28),n.k0s()()),2&e){const t=n.XpG();n.R7$(4),n.JRh(n.bMT(5,2,"profile.retailer.tonnageLelft")),n.R7$(2),n.Y8G("readonly",!t.isEdit)}}function dn(e,c){if(1&e&&(n.j41(0,"ion-select-option",33),n.EFF(1),n.k0s()),2&e){const t=c.$implicit;n.FS9("value",t),n.R7$(1),n.SpI("",t," ")}}function gn(e,c){if(1&e&&(n.j41(0,"div",29),n.nrm(1,"ion-icon",26),n.j41(2,"div",12)(3,"ion-label"),n.EFF(4),n.nI1(5,"translate"),n.k0s(),n.j41(6,"ion-select",30,31),n.DNE(8,dn,2,2,"ion-select-option",32),n.k0s()()()),2&e){const t=n.XpG();n.R7$(4),n.JRh(n.bMT(5,2,"profile.retailer.select-region-label")),n.R7$(4),n.Y8G("ngForOf",t.commonService.getRegions())}}function mn(e,c){if(1&e&&(n.j41(0,"ion-select-option",33),n.EFF(1),n.k0s()),2&e){const t=c.$implicit;n.FS9("value",t),n.R7$(1),n.SpI("",t," ")}}function pn(e,c){if(1&e&&(n.j41(0,"div",29),n.nrm(1,"ion-icon",26),n.j41(2,"div",12)(3,"ion-label"),n.EFF(4),n.nI1(5,"translate"),n.k0s(),n.j41(6,"ion-select",34),n.DNE(7,mn,2,2,"ion-select-option",32),n.k0s()()()),2&e){const t=n.XpG();n.R7$(4),n.JRh(n.bMT(5,2,"profile.retailer.select-city-label")),n.R7$(3),n.Y8G("ngForOf",t.commonService.getCities(t.userForm.get("address").get("region").value))}}function un(e,c){1&e&&(n.j41(0,"div",29),n.nrm(1,"ion-icon",26),n.j41(2,"div",12)(3,"ion-label"),n.EFF(4),n.nI1(5,"translate"),n.k0s(),n.nrm(6,"ion-input",35),n.k0s()()),2&e&&(n.R7$(4),n.JRh(n.bMT(5,1,"profile.retailer.input-district-label")))}function _n(e,c){if(1&e&&(n.j41(0,"div",10),n.nrm(1,"ion-icon",36),n.j41(2,"div",12)(3,"ion-label"),n.EFF(4,"Email"),n.k0s(),n.nrm(5,"ion-input",37),n.k0s()()),2&e){const t=n.XpG();n.R7$(5),n.Y8G("readonly",!t.isEdit)}}function fn(e,c){if(1&e&&n.nrm(0,"ion-input",38),2&e){const t=n.XpG();n.Y8G("readonly",!t.editPassword)}}function Cn(e,c){if(1&e){const t=n.RV6();n.j41(0,"div",39),n.bIt("click",function(){n.eBV(t);const o=n.XpG();return n.Njj(o.activeChangepassword())}),n.j41(1,"ion-text"),n.EFF(2,"********"),n.k0s(),n.nrm(3,"ion-icon",40),n.k0s()}}function Mn(e,c){if(1&e){const t=n.RV6();n.j41(0,"ion-button",41),n.bIt("click",function(){n.eBV(t);const o=n.XpG();return n.Njj(o.update())}),n.j41(1,"ion-label"),n.EFF(2),n.nI1(3,"translate"),n.k0s()()}2&e&&(n.R7$(2),n.SpI(" ",n.bMT(3,1,"profile.retailer.save-button-label")," "))}function Pn(e,c){if(1&e){const t=n.RV6();n.j41(0,"ion-button",41),n.bIt("click",function(){n.eBV(t);const o=n.XpG();return n.Njj(o.changePassword())}),n.j41(1,"ion-label"),n.EFF(2),n.nI1(3,"translate"),n.k0s()()}2&e&&(n.R7$(2),n.SpI(" ",n.bMT(3,1,"profile.retailer.save-button-label")," "))}const On=function(e){return{mbottom250:e}};let bn=(()=>{class e{constructor(t,i,o,p){this.userService=t,this.commonService=i,this.storageService=o,this.translateService=p,this.userCategory=R.s,this.typeOfPassword=!1,this.editPassword=!1,this.userForm=new r.gE({firstName:new r.MJ("",[r.k0.required]),lastName:new r.MJ("",[r.k0.required]),cni:new r.MJ("",[r.k0.required]),nui:new r.MJ("",[r.k0.required]),tonnage:new r.MJ("",[r.k0.required]),tel:new r.MJ("",[r.k0.required,r.k0.min(8)]),email:new r.MJ("",[r.k0.required,r.k0.min(3),r.k0.email]),password:new r.MJ("",[r.k0.required]),address:new r.gE({city:new r.MJ("",[r.k0.required]),district:new r.MJ("",[r.k0.required]),region:new r.MJ("",[r.k0.required])})}),this.storageService?.getUserConnected()}ngOnInit(){const t=this.commonService.user;this.userForm.patchValue({firstName:t.firstName,lastName:t.lastName,cni:t.cni,nui:t.nui,tel:t.tel,email:t.email,tonnage:t.tonnage.capacityLeft,address:{city:t.address?.city,district:t.address?.district,region:t.address?.region}}),this.balance={date:new Date,amount:5e8}}changeToEdit(){this.isEdit=!0,this.commonService.editUser=!0}undo(){this.isEdit=this.editPassword=!1,this.commonService.editUser=!1}changePassword(){var t=this;return(0,u.A)(function*(){t.isLoading=!0;const i=yield t.userService.changePassword(t.userForm.get("password").value);yield t.commonService.showToast({message:`${i.message}`,color:200!==i?.status?"danger":"success"}),t.editPassword=!1,t.commonService.editUser=!1,t.isLoading=!1})()}activeChangepassword(){this.editPassword=!0,this.isEdit=!1,this.commonService.editUser=!0}update(){var t=this;return(0,u.A)(function*(){t.isLoading=!0;const i={...t.userForm?.value};delete i.address,delete i.nui,delete i.lastName,delete i.password,delete i.tonnage,delete i.service,i.tel=`${i.tel}`;const o=t.commonService.verifyAllFieldsForm(i,t.translateService.currentLang);if(o)return yield t.commonService.showToast({message:`${o}`,color:"success"}),t.isLoading=!1;if(!((yield t.userService.updateUser({...t.userForm?.value}))instanceof M.yz)){const O=yield t.userService.find(t.commonService.user._id),{accessToken:h,company:b}=t.commonService.user;let k={accessToken:h,...O};b&&(k.company=b),t.storageService.store("USER_INFO",JSON.stringify(k)),t.storageService.getUserConnected(),t.isEdit=!1,t.commonService.editUser=!1}return t.isLoading=!1})()}static{this.\u0275fac=function(i){return new(i||e)(n.rXU(s.D),n.rXU(_.h),n.rXU(m.n),n.rXU(d.E))}}static{this.\u0275cmp=n.VBU({type:e,selectors:[["app-employee"]],decls:53,vars:40,consts:[[1,"content"],[1,"edit-btn-container"],["name","person-circle-outline",1,"account"],[1,"edit-feilds"],[3,"click"],["color","primary","class","icon-home",3,"src","click",4,"ngIf"],[4,"ngIf"],[3,"formGroup","ngClass"],[1,"input-group"],["class","form-group",4,"ngIf"],[1,"form-group"],["slot","start","src","/assets/icons/1-phone_iphone.svg",1,"icon-elt"],[1,"field-info"],["formControlName","tel","clearInput","",3,"readonly"],["formControlName","cni","clearInput","",3,"readonly"],["formControlName","nui","clearInput","",3,"readonly"],["class","form-group","formGroupName","address",4,"ngIf"],["slot","start","src","/assets/icons/1-https.svg",1,"icon-elt"],["placeholder","Password","formControlName","password","type","password","clearInput","",3,"readonly",4,"ngIf"],["class","password",3,"click",4,"ngIf"],["class","btn mbottom250 btn--meduim btn--upper","type","submit","color","primary","expand","block",3,"click",4,"ngIf"],["color","primary",1,"icon-home",3,"src","click"],["slot","start","src","/assets/icons/1-person.svg",1,"icon-elt"],[3,"readonly","value"],["formControlName","lastName","clearInput",""],["clearInput","","formControlName","firstName"],["src","/assets/icons/1-pin.svg",1,"icon-elt"],["clearInput","",3,"value"],["formControlName","tonnage",3,"readonly"],["formGroupName","address",1,"form-group"],["mode","ios","formControlName","region","interface","action-sheet","cancelText","Annuler"],["region",""],[3,"value",4,"ngFor","ngForOf"],[3,"value"],["mode","ios","formControlName","city","cancelText","Annuler","interface","action-sheet"],["formControlName","district","clearInput","","placeholder","Entrer votre quartier"],["src","/assets/icons/1-mail.svg",1,"icon-elt"],["formControlName","email","type","email","clearInput","",3,"readonly"],["placeholder","Password","formControlName","password","type","password","clearInput","",3,"readonly"],[1,"password",3,"click"],["color","primary","src","/assets/icons/edit-outline.svg"],["type","submit","color","primary","expand","block",1,"btn","mbottom250","btn--meduim","btn--upper",3,"click"]],template:function(i,o){1&i&&(n.j41(0,"div",0)(1,"div",1),n.nrm(2,"ion-icon",2),n.j41(3,"div",3)(4,"ion-label",4),n.bIt("click",function(){return o.commonService.editUser?o.undo():o.changeToEdit()}),n.EFF(5),n.nI1(6,"translate"),n.nI1(7,"translate"),n.k0s(),n.DNE(8,tn,1,1,"ion-icon",5),n.DNE(9,en,1,1,"ion-icon",5),n.k0s()(),n.DNE(10,on,1,0,"app-progress-spinner",6),n.j41(11,"form",7)(12,"div",8),n.DNE(13,rn,7,5,"div",9),n.DNE(14,cn,7,3,"div",9),n.DNE(15,an,7,3,"div",9),n.j41(16,"div",10),n.nrm(17,"ion-icon",11),n.j41(18,"div",12)(19,"ion-label"),n.EFF(20),n.nI1(21,"translate"),n.k0s(),n.nrm(22,"ion-input",13),n.k0s()(),n.j41(23,"div",10),n.nrm(24,"ion-icon",11),n.j41(25,"div",12)(26,"ion-label"),n.EFF(27),n.nI1(28,"translate"),n.k0s(),n.nrm(29,"ion-input",14),n.k0s()(),n.j41(30,"div",10),n.nrm(31,"ion-icon",11),n.j41(32,"div",12)(33,"ion-label"),n.EFF(34),n.nI1(35,"translate"),n.k0s(),n.nrm(36,"ion-input",15),n.k0s()(),n.DNE(37,sn,7,6,"div",9),n.DNE(38,ln,7,4,"div",9),n.DNE(39,gn,9,4,"div",16),n.DNE(40,pn,8,4,"div",16),n.DNE(41,un,7,3,"div",16),n.DNE(42,_n,6,1,"div",9),n.j41(43,"div",10),n.nrm(44,"ion-icon",17),n.j41(45,"div",12)(46,"ion-label"),n.EFF(47),n.nI1(48,"translate"),n.k0s(),n.DNE(49,fn,1,1,"ion-input",18),n.DNE(50,Cn,4,0,"div",19),n.k0s()()()(),n.DNE(51,Mn,4,3,"ion-button",20),n.DNE(52,Pn,4,3,"ion-button",20),n.k0s()),2&i&&(n.R7$(5),n.SpI(" ",o.commonService.editUser?n.bMT(6,26,"profile.retailer.cancel"):n.bMT(7,28,"profile.retailer.edit")," "),n.R7$(3),n.Y8G("ngIf",!o.isEdit&&!o.editPassword),n.R7$(1),n.Y8G("ngIf",o.isEdit||o.editPassword),n.R7$(1),n.Y8G("ngIf",o.isLoading),n.R7$(1),n.Y8G("formGroup",o.userForm)("ngClass",n.eq3(38,On,!o.isEdit)),n.R7$(2),n.Y8G("ngIf",!o.isEdit),n.R7$(1),n.Y8G("ngIf",o.isEdit),n.R7$(1),n.Y8G("ngIf",o.isEdit),n.R7$(5),n.JRh(n.bMT(21,30,"profile.retailer.input-phone-label")),n.R7$(2),n.Y8G("readonly",!o.isEdit),n.R7$(5),n.JRh(n.bMT(28,32,"profile.retailer.cni")),n.R7$(2),n.Y8G("readonly",!o.isEdit),n.R7$(5),n.JRh(n.bMT(35,34,"profile.retailer.nui")),n.R7$(2),n.Y8G("readonly",!o.isEdit),n.R7$(1),n.Y8G("ngIf",!o.isEdit),n.R7$(1),n.Y8G("ngIf",!o.isEdit),n.R7$(1),n.Y8G("ngIf",o.isEdit),n.R7$(1),n.Y8G("ngIf",o.isEdit),n.R7$(1),n.Y8G("ngIf",o.isEdit),n.R7$(1),n.Y8G("ngIf",!o.isEdit),n.R7$(5),n.JRh(n.bMT(48,36,"profile.retailer.input-password-label")),n.R7$(2),n.Y8G("ngIf",o.editPassword),n.R7$(1),n.Y8G("ngIf",!o.editPassword),n.R7$(1),n.Y8G("ngIf",o.isEdit),n.R7$(1),n.Y8G("ngIf",o.editPassword))},dependencies:[r.qT,r.BC,r.cb,l.Jm,l.iq,l.$w,l.he,l.Nm,l.Ip,l.IO,l.Je,l.Gw,a.YU,a.Sq,a.bT,y._,r.j4,r.JD,r.$R,f.D9],styles:["*[_ngcontent-%COMP%]{margin:0;padding:0}.header-md[_ngcontent-%COMP%]:after{background-image:none!important}.modal[_ngcontent-%COMP%]{--border-radius: calc(60 * var(--res))}.input-group[_ngcontent-%COMP%]{margin-top:calc(75 * var(--res))}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{--padding-start: 0;margin-bottom:calc(50 * var(--res));--placeholder-color: $color-six}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-input[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{font-family:Mont Regular;font-weight:400;font-size:calc(42 * var(--res));color:#1e1e1e}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{padding-top:calc(25 * var(--res));padding-bottom:calc(25 * var(--res))}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]{margin-bottom:calc(50 * var(--res));display:flex;align-items:center;justify-content:space-between;border-bottom-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-250, #c8c7cc)));border-bottom-width:.55px;border-bottom-style:solid}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{width:90%;margin-bottom:0;--border-color: transparent;--inner-border-width: 0px 0px 0px 0px;--border-style: none}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-checkbox[_ngcontent-%COMP%]{margin-top:5%}.bg-primary[_ngcontent-%COMP%]{color:#143c5d}.message[_ngcontent-%COMP%]{margin-bottom:calc(75 * var(--res))}.message[_ngcontent-%COMP%]   ion-title[_ngcontent-%COMP%]{font-weight:800;font-size:calc(70 * var(--res));padding-bottom:calc(25 * var(--res))}.message[_ngcontent-%COMP%]   ion-text[_ngcontent-%COMP%]{font-weight:400;color:#757474;display:block;line-height:1.5em!important}.btn--meduim[_ngcontent-%COMP%]{height:calc(130 * var(--res));font-size:calc(50 * var(--res));font-family:var(--mont-bold)}.btn--meduim[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:var(--mont-bold)}.btn--small[_ngcontent-%COMP%]{height:calc(100 * var(--res))}.btn--round[_ngcontent-%COMP%]{--border-radius: 50%;padding-inline-start:0;padding-inline-end:0;--padding-start: 0;--padding-end: 0}.btn--border-primary[_ngcontent-%COMP%]{--background: transparent;--background-activated: #c4e6d0;--border-color: var(--ion-color-primary);--border-style: solid;--border-width: 2px}.btn--border-secondary[_ngcontent-%COMP%]{--border-width: 2px;--background: transparent;--background-activated: #ddabb2;--border-color: var(--ion-color-secondary);--border-style: solid}.btn--upper[_ngcontent-%COMP%]{text-transform:uppercase}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]{overflow:hidden}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar{width:5px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-track{box-shadow:inset 0 0 5px #546e8d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background:#143c5d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover{background:#143c5d}.bottom-sheet-content[_ngcontent-%COMP%]{background:#fdfdfe;height:100%}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-thumbnail[_ngcontent-%COMP%]{margin-left:calc(31.25 * var(--res));background-color:#1e1e1e;--border-radius: 50%;--size: 23}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:Mont Bold;font-size:calc(45 * var(--res));text-align:center;color:#757474}.bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%], .bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]{padding:calc(75 * var(--res))}.font-size-5[_ngcontent-%COMP%]{font-size:calc(42 * var(--res))}.content[_ngcontent-%COMP%]   .account-amount-container[_ngcontent-%COMP%]{padding:calc(41 * var(--res));padding-bottom:0}.content[_ngcontent-%COMP%]   .account-amount-container[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{display:block}.content[_ngcontent-%COMP%]   .account-amount-container[_ngcontent-%COMP%]   .account-amount-value[_ngcontent-%COMP%]{font-family:Mont Bold;font-size:calc(100 * var(--res))}.content[_ngcontent-%COMP%]   .edit-btn-container[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:.5em;justify-content:center;align-items:center;margin-top:1rem;padding-right:calc(41 * var(--res))}.content[_ngcontent-%COMP%]   .edit-btn-container[_ngcontent-%COMP%]   .account[_ngcontent-%COMP%]{height:5rem;width:5rem;color:#143c5d}.content[_ngcontent-%COMP%]   .edit-btn-container[_ngcontent-%COMP%]   .edit-feilds[_ngcontent-%COMP%]{display:flex;justify-content:center;align-items:center}.content[_ngcontent-%COMP%]   .edit-btn-container[_ngcontent-%COMP%]   .edit-feilds[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:Mont Regular;font-weight:400;font-size:calc(42 * var(--res))}.content[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]{padding:calc(41 * var(--res));display:flex;align-items:center;justify-content:space-between;border-bottom-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-250, #c8c7cc)));border-bottom-width:.55px;border-bottom-style:solid}.content[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   .field-info[_ngcontent-%COMP%]{width:90%;margin-bottom:0;display:flex;align-items:center;justify-content:space-between}.content[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   .field-info[_ngcontent-%COMP%]   ion-input[_ngcontent-%COMP%], .content[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   .field-info[_ngcontent-%COMP%]   ion-text[_ngcontent-%COMP%], .content[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   .field-info[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%], .content[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   .field-info[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{font-family:Mont Regular;font-weight:400;font-size:calc(42 * var(--res));color:#1e1e1e}.content[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   .field-info[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{margin-right:calc(37.5 * var(--res));font-family:Mont Bold}.content[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   .field-info[_ngcontent-%COMP%]   ion-input[_ngcontent-%COMP%]{text-align:right;flex-direction:row-reverse}.content[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   .field-info[_ngcontent-%COMP%]   ion-input[_ngcontent-%COMP%], .content[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   .field-info[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%], .content[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   .field-info[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{color:#1e1e1e}.content[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   .field-info[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{padding-top:calc(25 * var(--res));padding-bottom:calc(25 * var(--res))}.content[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   .field-info[_ngcontent-%COMP%]   .password[_ngcontent-%COMP%]{padding-top:10px;padding-bottom:10px;display:flex;align-items:center;justify-content:space-between}.content[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   .field-info[_ngcontent-%COMP%]   .password[_ngcontent-%COMP%]   ion-text[_ngcontent-%COMP%]:last-child{color:#143c5d}.content[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   ion-checkbox[_ngcontent-%COMP%]{margin-top:5%}.content[_ngcontent-%COMP%]   .mbottom250[_ngcontent-%COMP%]{margin-bottom:calc(50 * var(--res))}.content[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]{padding:0 calc(41 * var(--res));margin-top:calc(50 * var(--res))}"]})}}return e})();function vn(e,c){if(1&e){const t=n.RV6();n.j41(0,"ion-icon",22),n.bIt("click",function(){n.eBV(t);const o=n.XpG();return n.Njj(o.changeToEdit())}),n.k0s()}2&e&&n.Y8G("src","/assets/icons/edit-outline.svg")}function hn(e,c){if(1&e){const t=n.RV6();n.j41(0,"ion-icon",22),n.bIt("click",function(){n.eBV(t);const o=n.XpG();return n.Njj(o.undo())}),n.k0s()}2&e&&n.Y8G("src","/assets/icons/undo-outline.svg")}function yn(e,c){1&e&&n.nrm(0,"app-progress-spinner")}function kn(e,c){if(1&e&&(n.j41(0,"div",11),n.nrm(1,"ion-icon",23),n.j41(2,"div",13)(3,"ion-label"),n.EFF(4),n.nI1(5,"translate"),n.k0s(),n.nrm(6,"ion-input",24),n.k0s()()),2&e){const t=n.XpG();n.R7$(4),n.JRh(n.bMT(5,3,"profile.retailer.input-name-label")),n.R7$(2),n.Y8G("readonly",!0)("value",(null==t.commonService.user?null:t.commonService.user.lastName)+" "+(null==t.commonService.user?null:t.commonService.user.firstName))}}function En(e,c){1&e&&(n.j41(0,"div",11),n.nrm(1,"ion-icon",23),n.j41(2,"div",13)(3,"ion-label"),n.EFF(4),n.nI1(5,"translate"),n.k0s(),n.nrm(6,"ion-input",25),n.k0s()()),2&e&&(n.R7$(4),n.JRh(n.bMT(5,1,"profile.retailer.input-lastName-label")))}function Rn(e,c){1&e&&(n.j41(0,"div",11),n.nrm(1,"ion-icon",23),n.j41(2,"div",13)(3,"ion-label"),n.EFF(4),n.nI1(5,"translate"),n.k0s(),n.nrm(6,"ion-input",26),n.k0s()()),2&e&&(n.R7$(4),n.JRh(n.bMT(5,1,"profile.retailer.input-firstName-label")))}function wn(e,c){if(1&e&&(n.j41(0,"div",11),n.nrm(1,"ion-icon",27),n.j41(2,"div",13)(3,"ion-label"),n.EFF(4),n.nI1(5,"translate"),n.k0s(),n.nrm(6,"ion-input",28),n.k0s()()),2&e){const t=n.XpG();n.R7$(4),n.JRh(n.bMT(5,4,"profile.retailer.input-address-label")),n.R7$(2),n.yjJ("value","",null==t.commonService.user||null==t.commonService.user.address?null:t.commonService.user.address.region,",\n          ",null==t.commonService.user||null==t.commonService.user.address?null:t.commonService.user.address.city," - ",null==t.commonService.user||null==t.commonService.user.address?null:t.commonService.user.address.district,"")}}function Sn(e,c){if(1&e&&(n.j41(0,"ion-select-option",28),n.EFF(1),n.k0s()),2&e){const t=c.$implicit;n.FS9("value",t),n.R7$(1),n.SpI("",t," ")}}function In(e,c){if(1&e&&(n.j41(0,"div",29),n.nrm(1,"ion-icon",27),n.j41(2,"div",13)(3,"ion-label"),n.EFF(4),n.nI1(5,"translate"),n.k0s(),n.j41(6,"ion-select",30,31),n.DNE(8,Sn,2,2,"ion-select-option",32),n.k0s()()()),2&e){const t=n.XpG();n.R7$(4),n.JRh(n.bMT(5,2,"profile.retailer.select-region-label")),n.R7$(4),n.Y8G("ngForOf",t.commonService.getRegions())}}function xn(e,c){if(1&e&&(n.j41(0,"ion-select-option",28),n.EFF(1),n.k0s()),2&e){const t=c.$implicit;n.FS9("value",t),n.R7$(1),n.SpI("",t," ")}}function Fn(e,c){if(1&e&&(n.j41(0,"div",29),n.nrm(1,"ion-icon",27),n.j41(2,"div",13)(3,"ion-label"),n.EFF(4),n.nI1(5,"translate"),n.k0s(),n.j41(6,"ion-select",33),n.DNE(7,xn,2,2,"ion-select-option",32),n.k0s()()()),2&e){const t=n.XpG();n.R7$(4),n.JRh(n.bMT(5,2,"profile.retailer.select-city-label")),n.R7$(3),n.Y8G("ngForOf",t.commonService.getCities(t.userForm.get("address").get("region").value))}}function jn(e,c){1&e&&(n.j41(0,"div",29),n.nrm(1,"ion-icon",27),n.j41(2,"div",13)(3,"ion-label"),n.EFF(4),n.nI1(5,"translate"),n.k0s(),n.nrm(6,"ion-input",34),n.k0s()()),2&e&&(n.R7$(4),n.JRh(n.bMT(5,1,"profile.retailer.input-district-label")))}function Tn(e,c){if(1&e&&(n.j41(0,"div",11),n.nrm(1,"ion-icon",35),n.j41(2,"div",13)(3,"ion-label"),n.EFF(4,"Email"),n.k0s(),n.nrm(5,"ion-input",36),n.k0s()()),2&e){const t=n.XpG();n.R7$(5),n.Y8G("readonly",!t.isEdit)}}function Nn(e,c){if(1&e&&n.nrm(0,"ion-input",37),2&e){const t=n.XpG();n.Y8G("readonly",!t.editPassword)}}function $n(e,c){if(1&e){const t=n.RV6();n.j41(0,"div",38),n.bIt("click",function(){n.eBV(t);const o=n.XpG();return n.Njj(o.activeChangepassword())}),n.j41(1,"ion-text"),n.EFF(2,"********"),n.k0s(),n.nrm(3,"ion-icon",39),n.k0s()}}function Gn(e,c){if(1&e){const t=n.RV6();n.j41(0,"ion-button",40),n.bIt("click",function(){n.eBV(t);const o=n.XpG();return n.Njj(o.update())}),n.j41(1,"ion-label"),n.EFF(2),n.nI1(3,"translate"),n.k0s()()}2&e&&(n.R7$(2),n.SpI(" ",n.bMT(3,1,"profile.retailer.save-button-label")," "))}function Dn(e,c){if(1&e){const t=n.RV6();n.j41(0,"ion-button",40),n.bIt("click",function(){n.eBV(t);const o=n.XpG();return n.Njj(o.changePassword())}),n.j41(1,"ion-label"),n.EFF(2),n.nI1(3,"translate"),n.k0s()()}2&e&&(n.R7$(2),n.SpI(" ",n.bMT(3,1,"profile.retailer.save-button-label")," "))}const Un=function(e){return{mbottom250:e}};let Yn=(()=>{class e{constructor(t,i,o,p,O,h,b){this.router=t,this.userService=i,this.commonService=o,this.storageService=p,this.authSvr=O,this.alertController=h,this.translateService=b,this.userCategory=R.s,this.typeOfPassword=!1,this.editPassword=!1,this.userForm=new r.gE({firstName:new r.MJ("",[r.k0.required]),lastName:new r.MJ("",[r.k0.required]),cni:new r.MJ("",[r.k0.required]),nui:new r.MJ("",[r.k0.required]),tel:new r.MJ("",[r.k0.required,r.k0.min(8)]),email:new r.MJ("",[r.k0.required,r.k0.min(3),r.k0.email]),password:new r.MJ("",[r.k0.required]),address:new r.gE({city:new r.MJ("",[r.k0.required]),district:new r.MJ("",[r.k0.required]),region:new r.MJ("",[r.k0.required])})}),this.storageService?.getUserConnected()}ngOnInit(){const t=this.commonService.user;this.userForm.patchValue({firstName:t.firstName,lastName:t.lastName,cni:t.cni,nui:t.nui,tel:t.tel,email:t.email,address:{city:t.address?.city,district:t.address?.district,region:t.address?.region}}),this.balance={date:new Date,amount:5e8}}changeToEdit(){this.isEdit=!0,this.commonService.editUser=!0}undo(){this.isEdit=this.editPassword=!1,this.commonService.editUser=!1}changePassword(){var t=this;return(0,u.A)(function*(){t.isLoading=!0;const i=yield t.userService.changePassword(t.userForm.get("password").value);yield t.commonService.showToast({message:`${i.message}`,color:200!==i?.status?"danger":"success"}),t.editPassword=!1,t.commonService.editUser=!1,t.isLoading=!1})()}activeChangepassword(){this.editPassword=!0,this.isEdit=!1,this.commonService.editUser=!0}update(){var t=this;return(0,u.A)(function*(){t.isLoading=!0;const i={...t.userForm?.value};delete i.address,delete i.nui,delete i.lastName,delete i.password,i.tel=`${i.tel}`;const o=t.commonService.verifyAllFieldsForm(i,t.translateService.currentLang);if(o)return yield t.commonService.showToast({message:`${o}`,color:"danger"}),t.isLoading=!1;if(!((yield t.userService.updateUser({...t.userForm?.value}))instanceof M.yz)){const O=yield t.userService.find(t.commonService.user._id),{accessToken:h,company:b}=t.commonService.user;let k={accessToken:h,...O};b&&(k.company=b),t.storageService.store("USER_INFO",JSON.stringify(k)),t.storageService.getUserConnected(),t.isEdit=!1,t.commonService.editUser=!1}return t.isLoading=!1})()}deleteCurentUser(){var t=this;return(0,u.A)(function*(){return(yield t.userService.updateUser({enable:!1}))instanceof M.yz||t.authSvr.logout(),t.router.navigate(["signin"]),yield t.commonService.showToast({message:t.translateService.currentLang===v.T.French?"Vous vous \xeates d\xe9connect\xe9 de l'application clic cadyst>":"You have logged out of the clic cadyst application",color:"success"}),t.isLoading=!1})()}openModalDeletingUser(){var t=this;return(0,u.A)(function*(){var o;yield(yield t.alertController.create({message:t.translateService.currentLang===v.T.French?"Vous \xeates sur le point de supprimer votre compte clic cadyst.\n Confirmez vous cette action ...":"You are about to delete your clic cadyst account.\n Do you confirm this action ? ",cssClass:"custom-loading",animated:!0,header:"Are you sure?",buttons:[{text:t.translateService.currentLang===v.T.French?"Supprimer":"Delete",cssClass:"alert-button-confirm",handler:(o=(0,u.A)(function*(){yield t.deleteCurentUser()}),function(){return o.apply(this,arguments)})},{text:t.translateService.currentLang===v.T.French?"Annuler":"Cancel",cssClass:"alert-button-cancel"}]})).present()})()}static{this.\u0275fac=function(i){return new(i||e)(n.rXU(P.Ix),n.rXU(s.D),n.rXU(_.h),n.rXU(m.n),n.rXU(C.k),n.rXU(l.hG),n.rXU(d.E))}}static{this.\u0275cmp=n.VBU({type:e,selectors:[["app-particular"]],decls:58,vars:42,consts:[[1,"content"],[1,"edit-btn-container"],["name","person-circle-outline",1,"account"],[1,"edit-feilds"],[3,"click"],["color","primary","class","icon-home",3,"src","click",4,"ngIf"],["name","trash-outline","color","danger",3,"click"],[4,"ngIf"],[3,"formGroup","ngClass"],[1,"input-group"],["class","form-group",4,"ngIf"],[1,"form-group"],["slot","start","src","/assets/icons/1-phone_iphone.svg",1,"icon-elt"],[1,"field-info"],["formControlName","tel","clearInput","",3,"readonly"],["formControlName","cni","clearInput","",3,"readonly"],["formControlName","nui","clearInput","",3,"readonly"],["class","form-group","formGroupName","address",4,"ngIf"],["slot","start","src","/assets/icons/1-https.svg",1,"icon-elt"],["placeholder","Password","formControlName","password","type","password","clearInput","",3,"readonly",4,"ngIf"],["class","password",3,"click",4,"ngIf"],["class","btn mbottom250 btn--meduim btn--upper","type","submit","color","primary","expand","block",3,"click",4,"ngIf"],["color","primary",1,"icon-home",3,"src","click"],["slot","start","src","/assets/icons/1-person.svg",1,"icon-elt"],[3,"readonly","value"],["formControlName","lastName","clearInput",""],["clearInput","","formControlName","firstName"],["src","/assets/icons/1-pin.svg",1,"icon-elt"],[3,"value"],["formGroupName","address",1,"form-group"],["mode","ios","formControlName","region","interface","action-sheet","cancelText","Annuler"],["region",""],[3,"value",4,"ngFor","ngForOf"],["mode","ios","formControlName","city","cancelText","Annuler","interface","action-sheet"],["formControlName","district","clearInput","","placeholder","Entrer votre quartier"],["src","/assets/icons/1-mail.svg",1,"icon-elt"],["formControlName","email","type","email","clearInput","",3,"readonly"],["placeholder","Password","formControlName","password","type","password","clearInput","",3,"readonly"],[1,"password",3,"click"],["color","primary","src","/assets/icons/edit-outline.svg"],["type","submit","color","primary","expand","block",1,"btn","mbottom250","btn--meduim","btn--upper",3,"click"]],template:function(i,o){1&i&&(n.j41(0,"div",0)(1,"div",1),n.nrm(2,"ion-icon",2),n.j41(3,"div",3)(4,"div",3)(5,"ion-label",4),n.bIt("click",function(){return o.commonService.editUser?o.undo():o.changeToEdit()}),n.EFF(6),n.nI1(7,"translate"),n.nI1(8,"translate"),n.k0s(),n.DNE(9,vn,1,1,"ion-icon",5),n.DNE(10,hn,1,1,"ion-icon",5),n.k0s()(),n.j41(11,"div")(12,"ion-label",4),n.bIt("click",function(){return o.openModalDeletingUser()}),n.EFF(13),n.nI1(14,"translate"),n.k0s(),n.j41(15,"ion-icon",6),n.bIt("click",function(){return o.openModalDeletingUser()}),n.k0s()()(),n.DNE(16,yn,1,0,"app-progress-spinner",7),n.j41(17,"form",8)(18,"div",9),n.DNE(19,kn,7,5,"div",10),n.DNE(20,En,7,3,"div",10),n.DNE(21,Rn,7,3,"div",10),n.j41(22,"div",11),n.nrm(23,"ion-icon",12),n.j41(24,"div",13)(25,"ion-label"),n.EFF(26),n.nI1(27,"translate"),n.k0s(),n.nrm(28,"ion-input",14),n.k0s()(),n.j41(29,"div",11),n.nrm(30,"ion-icon",12),n.j41(31,"div",13)(32,"ion-label"),n.EFF(33),n.nI1(34,"translate"),n.k0s(),n.nrm(35,"ion-input",15),n.k0s()(),n.j41(36,"div",11),n.nrm(37,"ion-icon",12),n.j41(38,"div",13)(39,"ion-label"),n.EFF(40),n.nI1(41,"translate"),n.k0s(),n.nrm(42,"ion-input",16),n.k0s()(),n.DNE(43,wn,7,6,"div",10),n.DNE(44,In,9,4,"div",17),n.DNE(45,Fn,8,4,"div",17),n.DNE(46,jn,7,3,"div",17),n.DNE(47,Tn,6,1,"div",10),n.j41(48,"div",11),n.nrm(49,"ion-icon",18),n.j41(50,"div",13)(51,"ion-label"),n.EFF(52),n.nI1(53,"translate"),n.k0s(),n.DNE(54,Nn,1,1,"ion-input",19),n.DNE(55,$n,4,0,"div",20),n.k0s()()()(),n.DNE(56,Gn,4,3,"ion-button",21),n.DNE(57,Dn,4,3,"ion-button",21),n.k0s()),2&i&&(n.R7$(6),n.SpI(" ",o.commonService.editUser?n.bMT(7,26,"profile.retailer.cancel"):n.bMT(8,28,"profile.retailer.edit")," "),n.R7$(3),n.Y8G("ngIf",!o.isEdit&&!o.editPassword),n.R7$(1),n.Y8G("ngIf",o.isEdit||o.editPassword),n.R7$(3),n.SpI(" ",n.bMT(14,30,"profile.disable-button-label")," "),n.R7$(3),n.Y8G("ngIf",o.isLoading),n.R7$(1),n.Y8G("formGroup",o.userForm)("ngClass",n.eq3(40,Un,!o.isEdit)),n.R7$(2),n.Y8G("ngIf",!o.isEdit),n.R7$(1),n.Y8G("ngIf",o.isEdit),n.R7$(1),n.Y8G("ngIf",o.isEdit),n.R7$(5),n.JRh(n.bMT(27,32,"profile.retailer.input-phone-label")),n.R7$(2),n.Y8G("readonly",!o.isEdit),n.R7$(5),n.JRh(n.bMT(34,34,"profile.retailer.cni")),n.R7$(2),n.Y8G("readonly",!o.isEdit),n.R7$(5),n.JRh(n.bMT(41,36,"profile.retailer.nui")),n.R7$(2),n.Y8G("readonly",!o.isEdit),n.R7$(1),n.Y8G("ngIf",!o.isEdit),n.R7$(1),n.Y8G("ngIf",o.isEdit),n.R7$(1),n.Y8G("ngIf",o.isEdit),n.R7$(1),n.Y8G("ngIf",o.isEdit),n.R7$(1),n.Y8G("ngIf",!o.isEdit),n.R7$(5),n.JRh(n.bMT(53,38,"profile.retailer.input-password-label")),n.R7$(2),n.Y8G("ngIf",o.editPassword),n.R7$(1),n.Y8G("ngIf",!o.editPassword),n.R7$(1),n.Y8G("ngIf",o.isEdit),n.R7$(1),n.Y8G("ngIf",o.editPassword))},dependencies:[r.qT,r.BC,r.cb,l.Jm,l.iq,l.$w,l.he,l.Nm,l.Ip,l.IO,l.Je,l.Gw,a.YU,a.Sq,a.bT,y._,r.j4,r.JD,r.$R,f.D9],styles:["*[_ngcontent-%COMP%]{margin:0;padding:0}.header-md[_ngcontent-%COMP%]:after{background-image:none!important}.modal[_ngcontent-%COMP%]{--border-radius: calc(60 * var(--res))}.input-group[_ngcontent-%COMP%]{margin-top:calc(75 * var(--res))}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{--padding-start: 0;margin-bottom:calc(50 * var(--res));--placeholder-color: $color-six}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-input[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{font-family:Mont Regular;font-weight:400;font-size:calc(42 * var(--res));color:#1e1e1e}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{padding-top:calc(25 * var(--res));padding-bottom:calc(25 * var(--res))}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]{margin-bottom:calc(50 * var(--res));display:flex;align-items:center;justify-content:space-between;border-bottom-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-250, #c8c7cc)));border-bottom-width:.55px;border-bottom-style:solid}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{width:90%;margin-bottom:0;--border-color: transparent;--inner-border-width: 0px 0px 0px 0px;--border-style: none}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-checkbox[_ngcontent-%COMP%]{margin-top:5%}.bg-primary[_ngcontent-%COMP%]{color:#143c5d}.message[_ngcontent-%COMP%]{margin-bottom:calc(75 * var(--res))}.message[_ngcontent-%COMP%]   ion-title[_ngcontent-%COMP%]{font-weight:800;font-size:calc(70 * var(--res));padding-bottom:calc(25 * var(--res))}.message[_ngcontent-%COMP%]   ion-text[_ngcontent-%COMP%]{font-weight:400;color:#757474;display:block;line-height:1.5em!important}.btn--meduim[_ngcontent-%COMP%]{height:calc(130 * var(--res));font-size:calc(50 * var(--res));font-family:var(--mont-bold)}.btn--meduim[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:var(--mont-bold)}.btn--small[_ngcontent-%COMP%]{height:calc(100 * var(--res))}.btn--round[_ngcontent-%COMP%]{--border-radius: 50%;padding-inline-start:0;padding-inline-end:0;--padding-start: 0;--padding-end: 0}.btn--border-primary[_ngcontent-%COMP%]{--background: transparent;--background-activated: #c4e6d0;--border-color: var(--ion-color-primary);--border-style: solid;--border-width: 2px}.btn--border-secondary[_ngcontent-%COMP%]{--border-width: 2px;--background: transparent;--background-activated: #ddabb2;--border-color: var(--ion-color-secondary);--border-style: solid}.btn--upper[_ngcontent-%COMP%]{text-transform:uppercase}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]{overflow:hidden}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar{width:5px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-track{box-shadow:inset 0 0 5px #546e8d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background:#143c5d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover{background:#143c5d}.bottom-sheet-content[_ngcontent-%COMP%]{background:#fdfdfe;height:100%}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-thumbnail[_ngcontent-%COMP%]{margin-left:calc(31.25 * var(--res));background-color:#1e1e1e;--border-radius: 50%;--size: 23}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:Mont Bold;font-size:calc(45 * var(--res));text-align:center;color:#757474}.bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%], .bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]{padding:calc(75 * var(--res))}.font-size-5[_ngcontent-%COMP%]{font-size:calc(42 * var(--res))}.content[_ngcontent-%COMP%]   .account-amount-container[_ngcontent-%COMP%]{padding:calc(41 * var(--res));padding-bottom:0}.content[_ngcontent-%COMP%]   .account-amount-container[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{display:block}.content[_ngcontent-%COMP%]   .account-amount-container[_ngcontent-%COMP%]   .account-amount-value[_ngcontent-%COMP%]{font-family:Mont Bold;font-size:calc(100 * var(--res))}.content[_ngcontent-%COMP%]   .edit-btn-container[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:.5em;justify-content:center;align-items:center;margin-top:1rem;padding-right:calc(41 * var(--res))}.content[_ngcontent-%COMP%]   .edit-btn-container[_ngcontent-%COMP%]   .account[_ngcontent-%COMP%]{height:5rem;width:5rem;color:#143c5d}.content[_ngcontent-%COMP%]   .edit-btn-container[_ngcontent-%COMP%]   .edit-feilds[_ngcontent-%COMP%]{display:flex;justify-content:center;align-items:center}.content[_ngcontent-%COMP%]   .edit-btn-container[_ngcontent-%COMP%]   .edit-feilds[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:Mont Regular;font-weight:400;font-size:calc(42 * var(--res))}.content[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]{padding:calc(41 * var(--res));display:flex;align-items:center;justify-content:space-between;border-bottom-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-250, #c8c7cc)));border-bottom-width:.55px;border-bottom-style:solid}.content[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   .field-info[_ngcontent-%COMP%]{width:90%;margin-bottom:0;display:flex;align-items:center;justify-content:space-between}.content[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   .field-info[_ngcontent-%COMP%]   ion-input[_ngcontent-%COMP%], .content[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   .field-info[_ngcontent-%COMP%]   ion-text[_ngcontent-%COMP%], .content[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   .field-info[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%], .content[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   .field-info[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{font-family:Mont Regular;font-weight:400;font-size:calc(42 * var(--res));color:#1e1e1e}.content[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   .field-info[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{margin-right:calc(37.5 * var(--res));font-family:Mont Bold}.content[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   .field-info[_ngcontent-%COMP%]   ion-input[_ngcontent-%COMP%]{text-align:right;flex-direction:row-reverse}.content[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   .field-info[_ngcontent-%COMP%]   ion-input[_ngcontent-%COMP%], .content[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   .field-info[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%], .content[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   .field-info[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{color:#1e1e1e}.content[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   .field-info[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{padding-top:calc(25 * var(--res));padding-bottom:calc(25 * var(--res))}.content[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   .field-info[_ngcontent-%COMP%]   .password[_ngcontent-%COMP%]{padding-top:10px;padding-bottom:10px;display:flex;align-items:center;justify-content:space-between}.content[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   .field-info[_ngcontent-%COMP%]   .password[_ngcontent-%COMP%]   ion-text[_ngcontent-%COMP%]:last-child{color:#143c5d}.content[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   ion-checkbox[_ngcontent-%COMP%]{margin-top:5%}.content[_ngcontent-%COMP%]   .mbottom250[_ngcontent-%COMP%]{margin-bottom:calc(50 * var(--res))}.content[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]{padding:0 calc(41 * var(--res));margin-top:calc(50 * var(--res))}"]})}}return e})();var Jn=g(43556);function An(e,c){if(1&e&&(n.j41(0,"ion-label"),n.EFF(1),n.k0s()),2&e){const t=n.XpG();n.R7$(1),n.JRh(((null==t.commonService||null==t.commonService.user?null:t.commonService.user.lastName)||" ")+" "+((null==t.commonService||null==t.commonService.user?null:t.commonService.user.firstName)||"N/A"))}}function Xn(e,c){if(1&e&&n.nrm(0,"ion-input",18),2&e){const t=n.XpG();n.Y8G("readonly",!t.commonSvr.isEdit)}}const S=function(e){return{active:e}};function zn(e,c){if(1&e&&(n.j41(0,"div",3)(1,"div",4),n.nrm(2,"ion-icon",5),n.j41(3,"ion-label"),n.EFF(4),n.nI1(5,"translate"),n.k0s()(),n.j41(6,"div",6),n.nrm(7,"ion-input",19),n.k0s()()),2&e){const t=n.XpG();n.Y8G("ngClass",n.eq3(5,S,t.commonSvr.isEdit)),n.R7$(4),n.JRh(n.bMT(5,3,"profile.retailer.input-firstName-label")),n.R7$(3),n.Y8G("readonly",!(null!=t.commonSvr&&t.commonSvr.isEdit))}}function Bn(e,c){if(1&e&&(n.j41(0,"ion-label"),n.EFF(1),n.k0s()),2&e){const t=n.XpG(2);n.R7$(1),n.JRh(t.userForm.get("tel").value)}}function Ln(e,c){if(1&e&&n.nrm(0,"ion-input",22),2&e){const t=n.XpG(2);n.Y8G("readonly",!(null!=t.commonSvr&&t.commonSvr.isEdit))}}function Vn(e,c){if(1&e&&(n.j41(0,"div",3)(1,"div",4),n.nrm(2,"ion-icon",20),n.j41(3,"ion-label"),n.EFF(4),n.nI1(5,"translate"),n.k0s()(),n.j41(6,"div",6),n.DNE(7,Bn,2,1,"ion-label",7),n.DNE(8,Ln,1,1,"ion-input",21),n.k0s()()),2&e){const t=n.XpG();n.Y8G("ngClass",n.eq3(6,S,null==t.commonSvr?null:t.commonSvr.isEdit)),n.R7$(4),n.JRh(n.bMT(5,4,"profile.retailer.input-phone-label")),n.R7$(3),n.Y8G("ngIf",!(null!=t.commonSvr&&t.commonSvr.isEdit)),n.R7$(1),n.Y8G("ngIf",null==t.commonSvr?null:t.commonSvr.isEdit)}}function qn(e,c){if(1&e&&(n.j41(0,"div",23)(1,"div",4),n.nrm(2,"ion-icon",24),n.j41(3,"ion-label"),n.EFF(4),n.nI1(5,"translate"),n.k0s()(),n.j41(6,"div",6)(7,"ion-label"),n.EFF(8),n.k0s()()()),2&e){const t=n.XpG();n.R7$(4),n.JRh(n.bMT(5,5,"profile.retailer.input-address-label")),n.R7$(4),n.LHq(" ",(null==t.commonService.user||null==t.commonService.user.address?null:t.commonService.user.address.region)||"N/A","",null!=t.commonService.user&&null!=t.commonService.user.address&&t.commonService.user.address.city?", ":""," ",(null==t.commonService.user||null==t.commonService.user.address?null:t.commonService.user.address.city)||""," ",null!=t.commonService.user&&null!=t.commonService.user.address&&t.commonService.user.address.district?" - "+((null==t.commonService.user||null==t.commonService.user.address?null:t.commonService.user.address.district)||"N/A"):""," ")}}function Kn(e,c){if(1&e&&(n.j41(0,"div",23)(1,"div",4),n.nrm(2,"img",25),n.j41(3,"ion-label"),n.EFF(4),n.nI1(5,"translate"),n.k0s()(),n.j41(6,"div",6)(7,"ion-label"),n.EFF(8),n.k0s()()()),2&e){const t=n.XpG();n.R7$(4),n.JRh(n.bMT(5,2,"profile.retailer.input-company-label")),n.R7$(4),n.SpI(" ",(null==t.commonService.user||null==t.commonService.user.company?null:t.commonService.user.company.name)||"N/A"," ")}}function Wn(e,c){if(1&e&&(n.j41(0,"div",23)(1,"div",4),n.nrm(2,"img",26),n.j41(3,"ion-label"),n.EFF(4),n.nI1(5,"translate"),n.k0s()(),n.j41(6,"div",6)(7,"ion-label"),n.EFF(8),n.k0s()()()),2&e){const t=n.XpG();n.R7$(4),n.JRh(n.bMT(5,2,"profile.retailer.nui")),n.R7$(4),n.SpI(" ",(null==t.commonService.user?null:t.commonService.user.nui)||"N/A"," ")}}function Hn(e,c){if(1&e&&(n.j41(0,"ion-select-option",31),n.EFF(1),n.k0s()),2&e){const t=c.$implicit;n.FS9("value",t),n.R7$(1),n.SpI("",t," ")}}function Zn(e,c){if(1&e&&(n.j41(0,"div",27)(1,"div",4),n.nrm(2,"ion-icon",24),n.j41(3,"ion-label"),n.EFF(4),n.nI1(5,"translate"),n.k0s()(),n.j41(6,"div",6)(7,"ion-select",28,29),n.DNE(9,Hn,2,2,"ion-select-option",30),n.k0s()()()),2&e){const t=n.XpG();n.Y8G("ngClass",n.eq3(5,S,t.commonSvr.isEdit)),n.R7$(4),n.JRh(n.bMT(5,3,"profile.retailer.select-region-label")),n.R7$(5),n.Y8G("ngForOf",t.commonService.getRegions())}}function Qn(e,c){if(1&e&&(n.j41(0,"ion-select-option",31),n.EFF(1),n.k0s()),2&e){const t=c.$implicit;n.FS9("value",t),n.R7$(1),n.SpI("",t," ")}}function nt(e,c){if(1&e&&(n.j41(0,"div",27)(1,"div",4),n.nrm(2,"ion-icon",24),n.j41(3,"ion-label"),n.EFF(4),n.nI1(5,"translate"),n.k0s()(),n.j41(6,"div",6)(7,"ion-select",32),n.DNE(8,Qn,2,2,"ion-select-option",30),n.k0s()()()),2&e){const t=n.XpG();n.Y8G("ngClass",n.eq3(5,S,null==t.commonSvr?null:t.commonSvr.isEdit)),n.R7$(4),n.JRh(n.bMT(5,3,"profile.retailer.select-city-label")),n.R7$(4),n.Y8G("ngForOf",t.commonService.getCities(t.userForm.get("address").get("region").value))}}function tt(e,c){if(1&e&&(n.j41(0,"div",27)(1,"div",4),n.nrm(2,"ion-icon",24),n.j41(3,"ion-label"),n.EFF(4),n.nI1(5,"translate"),n.k0s()(),n.j41(6,"div",6),n.nrm(7,"ion-input",33),n.k0s()()),2&e){const t=n.XpG();n.Y8G("ngClass",n.eq3(4,S,null==t.commonSvr?null:t.commonSvr.isEdit)),n.R7$(4),n.JRh(n.bMT(5,2,"profile.retailer.input-district-label"))}}function et(e,c){if(1&e&&(n.j41(0,"ion-label"),n.EFF(1),n.k0s()),2&e){const t=n.XpG();n.R7$(1),n.JRh(t.userForm.get("email").value)}}function ot(e,c){if(1&e&&n.nrm(0,"ion-input",34),2&e){const t=n.XpG();n.Y8G("readonly",!(null!=t.commonSvr&&t.commonSvr.isEdit))}}function it(e,c){if(1&e&&(n.j41(0,"ion-label"),n.EFF(1),n.k0s()),2&e){const t=n.XpG();n.R7$(1),n.JRh(t.userForm.get("cni").value||"N/A")}}function rt(e,c){if(1&e&&n.nrm(0,"ion-input",35),2&e){const t=n.XpG();n.Y8G("readonly",!(null!=t.commonSvr&&t.commonSvr.isEdit))}}function ct(e,c){if(1&e){const t=n.RV6();n.j41(0,"ion-button",36),n.bIt("click",function(){n.eBV(t);const o=n.XpG();return n.Njj(o.update())}),n.j41(1,"ion-label"),n.EFF(2),n.nI1(3,"translate"),n.k0s()()}2&e&&(n.R7$(2),n.SpI(" ",n.bMT(3,1,"profile.retailer.save-button-label")," "))}function at(e,c){if(1&e){const t=n.RV6();n.j41(0,"ion-button",37),n.bIt("click",function(){n.eBV(t);const o=n.XpG();return n.Njj(o.cancel())}),n.j41(1,"ion-label"),n.EFF(2),n.nI1(3,"translate"),n.k0s()()}2&e&&(n.R7$(2),n.SpI(" ",n.bMT(3,1,"profile.cancel")," "))}const st=function(e){return{"active-height":e}},lt=function(e,c){return[e,c]};let dt=(()=>{class e{constructor(t,i,o,p,O,h,b){this.commonSvr=t,this.userService=i,this.commonService=o,this.storageService=p,this.companyService=O,this.translateService=h,this.formBuilder=b,this.userCategory=R.s,this.storageService?.getUserConnected()}ngOnInit(){this.initForm(),this.loadUserData()}initForm(){this.userForm=this.formBuilder.group({firstName:["",r.k0.required],lastName:["",r.k0.required],cni:["",r.k0.required],nui:["",r.k0.required],tel:["",[r.k0.required,r.k0.minLength(8)]],email:["",[r.k0.required,r.k0.email]],socialReason:["",r.k0.required],password:[""],address:this.formBuilder.group({city:["",r.k0.required],district:["",r.k0.required],region:["",r.k0.required]})})}loadUserData(){var t=this;return(0,u.A)(function*(){const i=t.commonService.user;t.userForm.patchValue({firstName:i?.firstName,lastName:i?.lastName,cni:i?.cni,nui:i?.nui,tel:i?.tel,email:i?.email,socialReason:i?.company?.name,address:{city:i?.address?.city,district:i?.address?.district,region:i?.address?.region}}),t.company=yield t.companyService.find(i?.company?._id),t.commonService.isEdit=!1})()}update(){var t=this;return(0,u.A)(function*(){t.isLoading=!0;const i={...t.userForm?.value};delete i.address,delete i.nui,delete i.lastName,delete i.socialReason,i.tel=`${i.tel}`,""===i.password&&delete i.password;const o=t.commonService.verifyAllFieldsForm(i,t.translateService.currentLang);return o?(yield t.commonService.showToast({message:`${o}`,color:"danger"}),t.isLoading=!1,!1):(yield t.userService.updateUser({...t.userForm?.value}),t.isLoading=!1,!0)})()}cancel(){this.commonService.isEdit=!1,this.loadUserData()}static{this.\u0275fac=function(i){return new(i||e)(n.rXU(_.h),n.rXU(s.D),n.rXU(_.h),n.rXU(m.n),n.rXU(Jn.B),n.rXU(d.E),n.rXU(r.ok))}}static{this.\u0275cmp=n.VBU({type:e,selectors:[["app-user-company"]],decls:38,vars:35,consts:[[1,"content",3,"ngClass"],[3,"formGroup"],[1,"input-group"],[1,"form-group",3,"ngClass"],[1,"icon-label"],["slot","start","src","/assets/icons/ProfileBlue.svg",1,"icon-elt"],[1,"field-info"],[4,"ngIf"],["formControlName","firstName","type","text","clearInput","",3,"readonly",4,"ngIf"],["class","form-group",3,"ngClass",4,"ngIf"],["class","form-group",4,"ngIf"],["class","form-group","formGroupName","address",3,"ngClass",4,"ngIf"],["slot","start","src","/assets/icons/MessageBlue.svg",1,"icon-elt"],["formControlName","email","type","email","clearInput","",3,"readonly",4,"ngIf"],["slot","start","src","/assets/icons/ShieldDoneBlue.svg",1,"icon-elt"],["formControlName","cni","type","number","clearInput","",3,"readonly",4,"ngIf"],["class","btn mbottom250 btn--meduim btn--upper","type","submit","color","primary","expand","block",3,"click",4,"ngIf"],["class","btn btn--meduim btn--upper","type","submit","color","tertiary","expand","block",3,"click",4,"ngIf"],["formControlName","firstName","type","text","clearInput","",3,"readonly"],["formControlName","lastName","type","text","clearInput","",3,"readonly"],["slot","start","src","/assets/icons/CallingBlue.svg",1,"icon-elt"],["formControlName","tel","type","number","clearInput","",3,"readonly",4,"ngIf"],["formControlName","tel","type","number","clearInput","",3,"readonly"],[1,"form-group"],["slot","start","src","/assets/icons/LocationBlue.svg",1,"icon-elt"],["src","../../../../assets/icons/companie.png","alt","Company Icon",1,"icon-elt"],["src","../../../../assets/icons/statues.png","alt","Company Icon",1,"icon-elt"],["formGroupName","address",1,"form-group",3,"ngClass"],["mode","ios","formControlName","region","interface","action-sheet","cancelText","Annuler"],["region",""],[3,"value",4,"ngFor","ngForOf"],[3,"value"],["mode","ios","formControlName","city","cancelText","Annuler","interface","action-sheet"],["formControlName","district","clearInput","","placeholder","Entrer votre quartier"],["formControlName","email","type","email","clearInput","",3,"readonly"],["formControlName","cni","type","number","clearInput","",3,"readonly"],["type","submit","color","primary","expand","block",1,"btn","mbottom250","btn--meduim","btn--upper",3,"click"],["type","submit","color","tertiary","expand","block",1,"btn","btn--meduim","btn--upper",3,"click"]],template:function(i,o){1&i&&(n.j41(0,"div",0)(1,"form",1)(2,"div",2)(3,"div",3)(4,"div",4),n.nrm(5,"ion-icon",5),n.j41(6,"ion-label"),n.EFF(7),n.nI1(8,"translate"),n.k0s()(),n.j41(9,"div",6),n.DNE(10,An,2,1,"ion-label",7),n.DNE(11,Xn,1,1,"ion-input",8),n.k0s()(),n.DNE(12,zn,8,7,"div",9),n.DNE(13,Vn,9,8,"div",9),n.DNE(14,qn,9,7,"div",10),n.DNE(15,Kn,9,4,"div",10),n.DNE(16,Wn,9,4,"div",10),n.DNE(17,Zn,10,7,"div",11),n.DNE(18,nt,9,7,"div",11),n.DNE(19,tt,8,6,"div",11),n.j41(20,"div",3)(21,"div",4),n.nrm(22,"ion-icon",12),n.j41(23,"ion-label"),n.EFF(24,"E-mail"),n.k0s()(),n.j41(25,"div",6),n.DNE(26,et,2,1,"ion-label",7),n.DNE(27,ot,1,1,"ion-input",13),n.k0s()(),n.j41(28,"div",3)(29,"div",4),n.nrm(30,"ion-icon",14),n.j41(31,"ion-label"),n.EFF(32,"CNI"),n.k0s()(),n.j41(33,"div",6),n.DNE(34,it,2,1,"ion-label",7),n.DNE(35,rt,1,1,"ion-input",15),n.k0s()(),n.DNE(36,ct,4,3,"ion-button",16),n.DNE(37,at,4,3,"ion-button",17),n.k0s()()()),2&i&&(n.Y8G("ngClass",n.eq3(24,st,null==o.commonSvr?null:o.commonSvr.isEdit)),n.R7$(1),n.Y8G("formGroup",o.userForm),n.R7$(2),n.Y8G("ngClass",n.eq3(26,S,null==o.commonSvr?null:o.commonSvr.isEdit)),n.R7$(4),n.JRh(n.bMT(8,22,"profile.retailer.input-name-label")),n.R7$(3),n.Y8G("ngIf",!o.commonSvr.isEdit),n.R7$(1),n.Y8G("ngIf",null==o.commonSvr?null:o.commonSvr.isEdit),n.R7$(1),n.Y8G("ngIf",o.commonSvr.isEdit),n.R7$(1),n.Y8G("ngIf",!(null!=o.commonSvr&&o.commonSvr.isEdit&&n.l_i(28,lt,o.userCategory.Particular,o.userCategory.CompanyUser).includes(null==o.commonService.user?null:o.commonService.user.category))),n.R7$(1),n.Y8G("ngIf",!o.commonSvr.isEdit),n.R7$(1),n.Y8G("ngIf",!o.commonSvr.isEdit),n.R7$(1),n.Y8G("ngIf",!o.commonSvr.isEdit),n.R7$(1),n.Y8G("ngIf",o.commonSvr.isEdit),n.R7$(1),n.Y8G("ngIf",o.commonSvr.isEdit),n.R7$(1),n.Y8G("ngIf",o.commonSvr.isEdit),n.R7$(1),n.Y8G("ngClass",n.eq3(31,S,null==o.commonSvr?null:o.commonSvr.isEdit)),n.R7$(6),n.Y8G("ngIf",!o.commonSvr.isEdit),n.R7$(1),n.Y8G("ngIf",null==o.commonSvr?null:o.commonSvr.isEdit),n.R7$(1),n.Y8G("ngClass",n.eq3(33,S,o.commonSvr.isEdit)),n.R7$(6),n.Y8G("ngIf",!o.commonSvr.isEdit),n.R7$(1),n.Y8G("ngIf",null==o.commonSvr?null:o.commonSvr.isEdit),n.R7$(1),n.Y8G("ngIf",null==o.commonService?null:o.commonService.isEdit),n.R7$(1),n.Y8G("ngIf",null==o.commonService?null:o.commonService.isEdit))},dependencies:[r.qT,r.BC,r.cb,l.Jm,l.iq,l.$w,l.he,l.Nm,l.Ip,l.su,l.Je,l.Gw,a.YU,a.Sq,a.bT,r.j4,r.JD,r.$R,f.D9],styles:["*[_ngcontent-%COMP%]{margin:0;padding:0}.header-md[_ngcontent-%COMP%]:after{background-image:none!important}.modal[_ngcontent-%COMP%]{--border-radius: calc(60 * var(--res))}.input-group[_ngcontent-%COMP%]{margin-top:calc(75 * var(--res))}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{--padding-start: 0;margin-bottom:calc(50 * var(--res));--placeholder-color: $color-six}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-input[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{font-family:Mont Regular;font-weight:400;font-size:calc(42 * var(--res));color:#1e1e1e}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{padding-top:calc(25 * var(--res));padding-bottom:calc(25 * var(--res))}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]{margin-bottom:calc(50 * var(--res));display:flex;align-items:center;justify-content:space-between;border-bottom-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-250, #c8c7cc)));border-bottom-width:.55px;border-bottom-style:solid}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{width:90%;margin-bottom:0;--border-color: transparent;--inner-border-width: 0px 0px 0px 0px;--border-style: none}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-checkbox[_ngcontent-%COMP%]{margin-top:5%}.bg-primary[_ngcontent-%COMP%]{color:#143c5d}.message[_ngcontent-%COMP%]{margin-bottom:calc(75 * var(--res))}.message[_ngcontent-%COMP%]   ion-title[_ngcontent-%COMP%]{font-weight:800;font-size:calc(70 * var(--res));padding-bottom:calc(25 * var(--res))}.message[_ngcontent-%COMP%]   ion-text[_ngcontent-%COMP%]{font-weight:400;color:#757474;display:block;line-height:1.5em!important}.btn--meduim[_ngcontent-%COMP%]{height:calc(130 * var(--res));font-size:calc(50 * var(--res));font-family:var(--mont-bold)}.btn--meduim[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:var(--mont-bold)}.btn--small[_ngcontent-%COMP%]{height:calc(100 * var(--res))}.btn--round[_ngcontent-%COMP%]{--border-radius: 50%;padding-inline-start:0;padding-inline-end:0;--padding-start: 0;--padding-end: 0}.btn--border-primary[_ngcontent-%COMP%]{--background: transparent;--background-activated: #c4e6d0;--border-color: var(--ion-color-primary);--border-style: solid;--border-width: 2px}.btn--border-secondary[_ngcontent-%COMP%]{--border-width: 2px;--background: transparent;--background-activated: #ddabb2;--border-color: var(--ion-color-secondary);--border-style: solid}.btn--upper[_ngcontent-%COMP%]{text-transform:uppercase}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]{overflow:hidden}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar{width:5px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-track{box-shadow:inset 0 0 5px #546e8d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background:#143c5d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover{background:#143c5d}.bottom-sheet-content[_ngcontent-%COMP%]{background:#fdfdfe;height:100%}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-thumbnail[_ngcontent-%COMP%]{margin-left:calc(31.25 * var(--res));background-color:#1e1e1e;--border-radius: 50%;--size: 23}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:Mont Bold;font-size:calc(45 * var(--res));text-align:center;color:#757474}.bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%], .bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]{padding:calc(75 * var(--res))}.font-size-5[_ngcontent-%COMP%]{font-size:calc(42 * var(--res))}ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]{--padding-start: var(--space-5);--padding-end: var(--space-5);--padding-top: var(--space-5);--border-color: transparent}ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{display:flex;flex-direction:row;justify-content:space-between;font-size:var(--main-title);text-align:start}ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{font-family:Mont Regular;font-weight:100}ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{display:block}ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   .notifications[_ngcontent-%COMP%]{position:fixed;text-align:right;bottom:9%;right:4%;z-index:1}ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   .notifications[_ngcontent-%COMP%]   .badge[_ngcontent-%COMP%]{position:absolute;right:0;top:0;width:10px;height:10px;border-radius:50%;background:rgb(173,5,5)}ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   .notifications[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{margin-top:3px;font-size:23px;color:#737397}.content[_ngcontent-%COMP%]{margin:0 0 2em;padding:0 1em 1em;border-radius:10px;box-shadow:#d3d3d3 0 4px 16px}.content[_ngcontent-%COMP%]   .account-amount-container[_ngcontent-%COMP%]{padding-bottom:0;text-align:center}.content[_ngcontent-%COMP%]   .account-amount-container[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{display:block}.content[_ngcontent-%COMP%]   .account-amount-container[_ngcontent-%COMP%]   .account-amount-value[_ngcontent-%COMP%]{font-family:Mont Bold;font-size:calc(100 * var(--res))}.content[_ngcontent-%COMP%]   .edit-btn-container[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:flex-end;margin-top:1rem;padding-right:calc(41 * var(--res))}.content[_ngcontent-%COMP%]   .edit-btn-container-retail[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:.5em;justify-content:center;align-items:center;margin-bottom:1rem}.content[_ngcontent-%COMP%]   .edit-btn-container-retail[_ngcontent-%COMP%]   .login[_ngcontent-%COMP%]{position:relative;padding-right:0;cursor:pointer}.content[_ngcontent-%COMP%]   .edit-btn-container-retail[_ngcontent-%COMP%]   .login[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]{display:flex;align-items:center;text-decoration:none;font-weight:700}.content[_ngcontent-%COMP%]   .edit-btn-container-retail[_ngcontent-%COMP%]   .login[_ngcontent-%COMP%]   .login__icon[_ngcontent-%COMP%]{box-sizing:border-box;background-color:var(--ion-color-primary);border-radius:50%;width:40px;height:40px;display:flex;justify-content:center;align-items:center;z-index:1;transition:.3s ease}.content[_ngcontent-%COMP%]   .edit-btn-container-retail[_ngcontent-%COMP%]   .login[_ngcontent-%COMP%]   .login__icon[_ngcontent-%COMP%]   .avatar[_ngcontent-%COMP%]{max-width:100%;max-height:100%;border-radius:50%}.content[_ngcontent-%COMP%]   .edit-btn-container-retail[_ngcontent-%COMP%]   .login__text[_ngcontent-%COMP%]{border:solid 2px var(--ion-color-primary);text-align:right;font-size:14px;border-radius:1.5rem;margin-left:-20px;padding:.4rem 1rem .4rem 2rem;transition:.3s ease}.content[_ngcontent-%COMP%]   .edit-btn-container-retail[_ngcontent-%COMP%]   .account[_ngcontent-%COMP%]{height:5rem;width:5rem;color:#143c5d}.content[_ngcontent-%COMP%]   .edit-btn-container-retail[_ngcontent-%COMP%]   .edit-feilds[_ngcontent-%COMP%]{display:flex;justify-content:center;align-items:center}.content[_ngcontent-%COMP%]   .edit-btn-container-retail[_ngcontent-%COMP%]   .edit-feilds[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:Mont Regular;font-weight:400;font-size:calc(42 * var(--res))}.content[_ngcontent-%COMP%]   .edit-btn-container-retail[_ngcontent-%COMP%]   .edit-feilds[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{color:#143c5d}.content[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:.3em;padding:calc(41 * var(--res)) 0px 6px 0px;justify-content:space-between;border-bottom-color:#000;border-bottom-width:1px;border-bottom-style:dotted}.content[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   .icon-label[_ngcontent-%COMP%]{display:flex;gap:.3em;align-items:center}.content[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   .icon-label[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{color:#0b305c;font-size:14px;font-family:Mont Bold}.content[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   .field-info[_ngcontent-%COMP%]{width:100%;margin-bottom:0;display:flex;align-items:center;justify-content:space-between}.content[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   .field-info[_ngcontent-%COMP%]   ion-input[_ngcontent-%COMP%], .content[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   .field-info[_ngcontent-%COMP%]   ion-text[_ngcontent-%COMP%], .content[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   .field-info[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{font-family:Mont Regular;font-weight:400;font-size:calc(42 * var(--res));color:#1e1e1e}.content[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   .field-info[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-size:13px;margin-right:calc(37.5 * var(--res));font-family:Mont Bold}.content[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   .field-info[_ngcontent-%COMP%]   ion-input[_ngcontent-%COMP%], .content[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   .field-info[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{height:30px;width:100%;background-color:#ebebeb}.content[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   .field-info[_ngcontent-%COMP%]   ion-input[_ngcontent-%COMP%], .content[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   .field-info[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%], .content[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   .field-info[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{color:#1e1e1e}.content[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   .field-info[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{padding-top:calc(25 * var(--res));padding-bottom:calc(25 * var(--res))}.content[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   .field-info[_ngcontent-%COMP%]   .password[_ngcontent-%COMP%]{padding-top:10px;padding-bottom:10px;display:flex;align-items:center;justify-content:space-between}.content[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   .field-info[_ngcontent-%COMP%]   .password[_ngcontent-%COMP%]   ion-text[_ngcontent-%COMP%]:last-child{color:#143c5d}.content[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   ion-checkbox[_ngcontent-%COMP%]{margin-top:5%}.content[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .active[_ngcontent-%COMP%]{border-bottom-color:#419cfb;border-bottom-style:groove;padding:calc(41 * var(--res)) 0px 0px 0px}.content[_ngcontent-%COMP%]   .show-balance-details[_ngcontent-%COMP%]{margin-top:1em;font-weight:bolder}.content[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]{margin-top:calc(50 * var(--res))}.content[_ngcontent-%COMP%]   .skeleton[_ngcontent-%COMP%]{width:90%;margin-bottom:.5rem}.content[_ngcontent-%COMP%]   .skeleton[_ngcontent-%COMP%]   ion-skeleton-text[_ngcontent-%COMP%]{border-radius:10px;width:100%;height:100%}.active-height[_ngcontent-%COMP%]{height:36em}"]})}}return e})();var gt=g(11244);function mt(e,c){1&e&&n.nrm(0,"app-retailer")}function pt(e,c){1&e&&n.nrm(0,"app-employee")}function ut(e,c){1&e&&n.nrm(0,"app-particular")}function _t(e,c){1&e&&n.nrm(0,"app-user-company")}function ft(e,c){if(1&e){const t=n.RV6();n.j41(0,"ion-button",22),n.bIt("click",function(){n.eBV(t);const o=n.XpG();return n.Njj(o.saveProfilePicture())}),n.j41(1,"ion-label"),n.EFF(2),n.nI1(3,"translate"),n.k0s()()}2&e&&(n.R7$(2),n.SpI(" ",n.bMT(3,1,"profile.save-profile-picture")," "))}function Ct(e,c){if(1&e){const t=n.RV6();n.j41(0,"ion-button",23),n.bIt("click",function(){n.eBV(t);const o=n.XpG();return n.Njj(o.signOut())}),n.j41(1,"ion-label"),n.EFF(2),n.nI1(3,"translate"),n.k0s()()}2&e&&(n.R7$(2),n.SpI(" ",n.bMT(3,1,"profile.logout-button-label")," "))}const A=function(e,c){return[e,c]},Mt=[{path:"",component:(()=>{class e{constructor(t,i,o,p,O,h,b,k,bt){this.router=t,this.location=i,this.commonSvr=o,this.authSvr=p,this.translateService=O,this.userService=h,this.storageSrv=b,this.imageCompress=k,this.modalCtrl=bt,this.isDefaultUser=!1,this.isContentShown=!1,this.isAccountClient=!1,this.isLoading=!1,this.currentDate=new Date,this.points=0,this.userCategory=R.s,this.selectedFile=null,this.attachment={file:"",name:"",contentType:""},this.LIMIT_SIZE=15e5,this.isApprove=!1,this.balance={amount:null,date:null}}ngOnInit(){this.user=this.storageSrv.getUserConnected()}back(){this.location.back()}showToast(t,i){var o=this;return(0,u.A)(function*(){yield o.commonSvr.showToast({color:t,message:i})})()}signOut(){var t=this;return(0,u.A)(function*(){try{t.authSvr.logout(),t.commonSvr.user=null,yield t.showToast("success",t.getLogoutMessage())}catch{yield t.showToast("danger","Une erreur s'est produite. Veuillez v\xe9rifier votre connexion internet.")}})()}edit(){this.commonSvr.isEdit=!0}setAttachment(t){var i=this;return(0,u.A)(function*(){try{i.selectedFile=i.getFileFromDataSet(t),i.attachment.file=yield i.getFileDataUrl(i.selectedFile);const o=yield i.getFileSize(i.selectedFile,i.attachment.file);i.validateFileSize(o,i.LIMIT_SIZE),i.attachment.name=i.selectedFile.name,i.attachment.contentType=i.selectedFile.type}catch(o){i.handleError(o)}})()}preventAlert(){var t=this;return(0,u.A)(function*(){const i=yield t.modalCtrl.create({component:F.y,cssClass:"modalClass",componentProps:{dataModal:{confirmButton:t.translateService.currentLang===v.T.French?"Oui":"Yes",cancelButton:t.translateService.currentLang===v.T.French?"Non":"No",text:t.getModalText(),handler:()=>{document.getElementById("file")?.click(),t.isApprove=!0,t.modalCtrl.dismiss()}}}});yield i.present(),yield i.onDidDismiss(),t.modalCtrl.dismiss()})()}saveProfilePicture(){var t=this;return(0,u.A)(function*(){if(t.attachment?.file){t.isLoading=!0;try{const i=yield t.userService.updateUser({profilePicture:t.attachment.file});if(!("data"in i))throw new Error("Update failed");t.user=i.data,t.commonSvr.user=i.data}catch(i){console.error("Error updating profile picture:",i)}finally{t.isLoading=!1}}})()}getFileFromDataSet(t){const o=t.target.files?.[0];if(!o)throw new Error("Aucun fichier s\xe9lectionn\xe9");return o}getFileDataUrl(t){return(0,u.A)(function*(){return new Promise((i,o)=>{const p=new FileReader;p.onload=()=>i(p.result),p.onerror=O=>o(O),p.readAsDataURL(t)})})()}getFileSize(t,i){var o=this;return(0,u.A)(function*(){if("application/pdf"===t.type)return t.size;const p=yield o.imageCompress.compressFile(i,x._k.Up);return o.imageCompress.byteCount(p)})()}validateFileSize(t,i){if(t>i)throw new Error("Veuillez choisir un fichier de moins de 1,5 MB SVP !")}handleError(t){this.selectedFile=null,this.showToast("danger",t.message)}getLogoutMessage(){return this.translateService.currentLang===v.T.French?"Vous vous \xeates d\xe9connect\xe9 de l'application Clic Cadyst":"You have logged out of the clic cadyst application"}getModalText(){return this.translateService.currentLang===v.T.French?"Acc\xe8s \xe0 la cam\xe9ra pour la mise \xe0 jour de votre photo de profil. Les donn\xe9es sont stock\xe9es localement et ne sont pas partag\xe9es sans votre consentement.":"Camera access for updating your profile picture. Data is stored locally and not shared without your consent."}static{this.\u0275fac=function(i){return new(i||e)(n.rXU(P.Ix),n.rXU(a.aZ),n.rXU(_.h),n.rXU(C.k),n.rXU(d.E),n.rXU(s.D),n.rXU(m.n),n.rXU(x.ep),n.rXU(l.W3))}}static{this.\u0275cmp=n.VBU({type:e,selectors:[["app-my-account"]],decls:36,vars:24,consts:[[1,"header1"],[1,"header_nav"],[1,"header"],[1,"header-arrow"],["slot","start","src","/assets/icons/arrow-white.svg",3,"click"],[1,"title"],[1,"header_nav--profil"],[1,"img_profil",3,"click"],["alt","profil image",1,"header-img",3,"src"],[1,"upload-overlay"],["name","camera"],["id","file","type","file","accept","image/*",2,"display","none",3,"change"],[1,"profil_info"],[1,"profil_info_bigText"],[1,"update"],[1,"link",3,"click"],["src","assets/icons/Edit.svg",3,"click"],[1,"ion-padding"],["id","container"],[4,"ngIf"],["class","btn btn--medium btn--upper","type","button","color","primary","expand","block",3,"click",4,"ngIf"],["class","btn btn--medium btn--upper","type","submit","color","primary","expand","block",3,"click",4,"ngIf"],["type","button","color","primary","expand","block",1,"btn","btn--medium","btn--upper",3,"click"],["type","submit","color","primary","expand","block",1,"btn","btn--medium","btn--upper",3,"click"]],template:function(i,o){1&i&&(n.j41(0,"ion-header")(1,"div",0)(2,"div",1)(3,"ion-toolbar",2)(4,"div",3)(5,"div")(6,"ion-img",4),n.bIt("click",function(){return o.back()}),n.k0s()(),n.j41(7,"div")(8,"ion-title",5),n.EFF(9),n.nI1(10,"translate"),n.k0s()()()(),n.j41(11,"div",6)(12,"div",7),n.bIt("click",function(){return o.preventAlert()}),n.nrm(13,"img",8),n.j41(14,"div",9),n.nrm(15,"ion-icon",10),n.k0s()(),n.j41(16,"input",11),n.bIt("change",function(O){return o.setAttachment(O)}),n.k0s(),n.j41(17,"div",12)(18,"div",13),n.EFF(19),n.nI1(20,"capitalize"),n.nI1(21,"translate"),n.k0s(),n.j41(22,"div",14)(23,"a",15),n.bIt("click",function(){return o.edit()}),n.j41(24,"u"),n.EFF(25),n.nI1(26,"translate"),n.k0s()(),n.j41(27,"ion-img",16),n.bIt("click",function(){return o.edit()}),n.k0s()()()()()()(),n.j41(28,"ion-content",17)(29,"div",18),n.DNE(30,mt,1,0,"app-retailer",19),n.DNE(31,pt,1,0,"app-employee",19),n.DNE(32,ut,1,0,"app-particular",19),n.DNE(33,_t,1,0,"app-user-company",19),n.k0s(),n.DNE(34,ft,4,3,"ion-button",20),n.DNE(35,Ct,4,3,"ion-button",21),n.k0s()),2&i&&(n.R7$(9),n.JRh(n.bMT(10,10,"profile.account-manage")),n.R7$(4),n.Y8G("src",(null==o.attachment?null:o.attachment.file)||(null==o.user?null:o.user.profilePicture)||"assets/icons/Profil2.png",n.B4B),n.R7$(6),n.JRh(n.bMT(20,12,n.bMT(21,14,"profile.client-account"))),n.R7$(6),n.JRh(n.bMT(26,16,"profile.edit-profile")),n.R7$(5),n.Y8G("ngIf",(null==o.commonSvr||null==o.commonSvr.user?null:o.commonSvr.user.category)===(null==o.userCategory?null:o.userCategory.Retailer)),n.R7$(1),n.Y8G("ngIf",(null==o.commonSvr||null==o.commonSvr.user?null:o.commonSvr.user.category)===(null==o.userCategory?null:o.userCategory.EmployeeLapasta)),n.R7$(1),n.Y8G("ngIf",n.l_i(18,A,o.userCategory.Commercial,o.userCategory.DonutAnimator).includes(null==o.commonSvr||null==o.commonSvr.user?null:o.commonSvr.user.category)),n.R7$(1),n.Y8G("ngIf",n.l_i(21,A,null==o.userCategory?null:o.userCategory.Particular,o.userCategory.CompanyUser).includes(null==o.commonSvr||null==o.commonSvr.user?null:o.commonSvr.user.category)),n.R7$(1),n.Y8G("ngIf",o.attachment.file),n.R7$(1),n.Y8G("ngIf",!(null!=o.commonSvr&&o.commonSvr.isEdit)))},dependencies:[l.Jm,l.W9,l.eU,l.iq,l.KW,l.he,l.BC,l.ai,a.bT,nn,bn,Yn,dt,gt.F,f.D9],styles:["*[_ngcontent-%COMP%]{margin:0;padding:0}.header-md[_ngcontent-%COMP%]:after{background-image:none!important}.modal[_ngcontent-%COMP%]{--border-radius: calc(60 * var(--res))}.input-group[_ngcontent-%COMP%]{margin-top:calc(75 * var(--res))}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{--padding-start: 0;margin-bottom:calc(50 * var(--res));--placeholder-color: $color-six}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-input[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{font-family:Mont Regular;font-weight:400;font-size:calc(42 * var(--res));color:#1e1e1e}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{padding-top:calc(25 * var(--res));padding-bottom:calc(25 * var(--res))}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]{margin-bottom:calc(50 * var(--res));display:flex;align-items:center;justify-content:space-between;border-bottom-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-250, #c8c7cc)));border-bottom-width:.55px;border-bottom-style:solid}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{width:90%;margin-bottom:0;--border-color: transparent;--inner-border-width: 0px 0px 0px 0px;--border-style: none}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-checkbox[_ngcontent-%COMP%]{margin-top:5%}.bg-primary[_ngcontent-%COMP%]{color:#143c5d}.message[_ngcontent-%COMP%]{margin-bottom:calc(75 * var(--res))}.message[_ngcontent-%COMP%]   ion-title[_ngcontent-%COMP%]{font-weight:800;font-size:calc(70 * var(--res));padding-bottom:calc(25 * var(--res))}.message[_ngcontent-%COMP%]   ion-text[_ngcontent-%COMP%]{font-weight:400;color:#757474;display:block;line-height:1.5em!important}.btn--meduim[_ngcontent-%COMP%]{height:calc(130 * var(--res));font-size:calc(50 * var(--res));font-family:var(--mont-bold)}.btn--meduim[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:var(--mont-bold)}.btn--small[_ngcontent-%COMP%]{height:calc(100 * var(--res))}.btn--round[_ngcontent-%COMP%]{--border-radius: 50%;padding-inline-start:0;padding-inline-end:0;--padding-start: 0;--padding-end: 0}.btn--border-primary[_ngcontent-%COMP%]{--background: transparent;--background-activated: #c4e6d0;--border-color: var(--ion-color-primary);--border-style: solid;--border-width: 2px}.btn--border-secondary[_ngcontent-%COMP%]{--border-width: 2px;--background: transparent;--background-activated: #ddabb2;--border-color: var(--ion-color-secondary);--border-style: solid}.btn--upper[_ngcontent-%COMP%]{text-transform:uppercase}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]{overflow:hidden}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar{width:5px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-track{box-shadow:inset 0 0 5px #546e8d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background:#143c5d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover{background:#143c5d}.bottom-sheet-content[_ngcontent-%COMP%]{background:#fdfdfe;height:100%}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-thumbnail[_ngcontent-%COMP%]{margin-left:calc(31.25 * var(--res));background-color:#1e1e1e;--border-radius: 50%;--size: 23}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:Mont Bold;font-size:calc(45 * var(--res));text-align:center;color:#757474}.bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%], .bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]{padding:calc(75 * var(--res))}.font-size-5[_ngcontent-%COMP%]{font-size:calc(42 * var(--res))}ion-header[_ngcontent-%COMP%]   .header1[_ngcontent-%COMP%]{position:relative;height:125px;width:100%;border-radius:0 0 15px 15px;background:url(bg.084da384672e9d5b.png) center no-repeat;background-position:center;background-repeat:no-repeat;background-size:cover}ion-header[_ngcontent-%COMP%]   .header1[_ngcontent-%COMP%]   .header_nav[_ngcontent-%COMP%]{position:absolute;padding:1rem;height:100%;width:100%}ion-header[_ngcontent-%COMP%]   .header1[_ngcontent-%COMP%]   .header_nav[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]{height:1.3em;padding:0 0 1em;--background: transparent}ion-header[_ngcontent-%COMP%]   .header1[_ngcontent-%COMP%]   .header_nav[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   .header-arrow[_ngcontent-%COMP%]{display:flex;gap:.5em;width:100%;align-items:center}ion-header[_ngcontent-%COMP%]   .header1[_ngcontent-%COMP%]   .header_nav[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   .header-arrow[_ngcontent-%COMP%]   ion-title[_ngcontent-%COMP%]{font-family:Mont Regular;font-size:1.4rem;color:var(--ion-color-primary-contrast)}ion-header[_ngcontent-%COMP%]   .img_profil[_ngcontent-%COMP%]{height:40px;width:40px;border-radius:50%;overflow:hidden}ion-header[_ngcontent-%COMP%]   .img_profil[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{object-fit:cover}ion-header[_ngcontent-%COMP%]   .profil_info[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:8px;color:#fff}ion-header[_ngcontent-%COMP%]   .profil_info[_ngcontent-%COMP%]   div[_ngcontent-%COMP%]:nth-child(1){width:120px;height:29px;font-weight:800;font-size:18px;line-height:1.813;white-space:nowrap}ion-header[_ngcontent-%COMP%]   .profil_info[_ngcontent-%COMP%]   .update[_ngcontent-%COMP%]{display:flex;gap:.7em;align-items:flex-end;align-self:flex-start}ion-header[_ngcontent-%COMP%]   .profil_info[_ngcontent-%COMP%]   .update[_ngcontent-%COMP%]   .link[_ngcontent-%COMP%]{font-family:var(--mont-semibold);font-size:.85rem;color:#419cfb}ion-header[_ngcontent-%COMP%]   .header_nav--profil[_ngcontent-%COMP%]{display:flex;align-items:center;gap:12px}ion-header[_ngcontent-%COMP%]   .home-tabset[_ngcontent-%COMP%]{width:105px}ion-header[_ngcontent-%COMP%]   .icons_profil[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:flex-end;gap:20px;width:100px}ion-content[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]{padding:0}ion-content[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]{padding:0 calc(41 * var(--res));position:fixed;z-index:5;bottom:2%;width:100%}"]})}}return e})()}];let Pt=(()=>{class e{static{this.\u0275fac=function(i){return new(i||e)}}static{this.\u0275mod=n.$C({type:e})}static{this.\u0275inj=n.G2t({imports:[P.iI.forChild(Mt),P.iI]})}}return e})(),Ot=(()=>{class e{static{this.\u0275fac=function(i){return new(i||e)}}static{this.\u0275mod=n.$C({type:e})}static{this.\u0275inj=n.G2t({imports:[r.YN,l.bv,r.YN,a.MD,E.G,f.h,r.X1,Pt]})}}return e})()},43556:(Y,w,g)=>{g.d(w,{B:()=>F});var l=g(73308),a=g(94934),f=g(45312),E=g(26409),P=(g(99987),g(2978)),u=g(33607),v=g(82571),R=g(14599),x=g(74657);let F=(()=>{class n{constructor(C,d,s,m,M){this.baseUrl=C,this.http=d,this.commonSrv=s,this.storageSrv=m,this.translateService=M,this.base_url=`${this.baseUrl.getOrigin()}${f.c.basePath}`,this.base_url+="companies"}create(C){var d=this;return(0,l.A)(function*(){try{return delete C._id,yield(0,a.s)(d.http.post(d.base_url,C))}catch(s){return d.commonSrv.getError("Echec de cr\xe9ation de la compagnie",s)}})()}getCompanies(C){var d=this;return(0,l.A)(function*(){try{let s=new E.Nl;const{category:m,city:M,limit:y,name:I,regionCom:j,solToId:T,tel:N,users:$,offset:G,enable:J=!0,projection:D,isLoyaltyProgDistributor:U}=C;return void 0!==m&&(s=s.append("category",m)),M&&(s=s.append("address.city",M)),I&&(s=s.append("name",I)),T&&(s=s.append("erpSoldToId",T)),N&&(s=s.append("tel",`${N}`)),D&&(s=s.append("projection",`${D}`)),$&&(s=s.append("users",`${$}`)),j&&(s=s.append("address.commercialRegion",j)),U&&(s=s.append("isLoyaltyProgDistributor",U)),void 0!==y&&(s=s.append("limit",y)),void 0!==G&&(s=s.append("offset",G)),s=s.set("enable",J),yield(0,a.s)(d.http.get(d.base_url,{params:s}))}catch(s){const M={message:d.commonSrv.getError("",s).message,color:"danger"};return yield d.commonSrv.showToast(M),s}})()}getParticularCompanies(C){var d=this;return(0,l.A)(function*(){let s=new E.Nl;const{limit:m,offset:M,enable:y=!0,commercialRegion:I}=C;return void 0!==m&&(s=s.append("limit",m)),void 0!==M&&(s=s.append("offset",M)),I&&(s=s.append("address.commercialRegion",I)),s=s.set("enable",y),yield(0,a.s)(d.http.get(d.base_url+"/particular-suppliers",{params:s}))})()}find(C){var d=this;return(0,l.A)(function*(){try{return yield(0,a.s)(d.http.get(d.base_url+"/"+C))}catch{return d.commonSrv.initCompany()}})()}getBalance(C){var d=this;return(0,l.A)(function*(){try{let s=new E.Nl;const{company:m}=d.storageSrv.getUserConnected();return s=s.set("_id",m?m?._id:C?.companyId),yield(0,a.s)(d.http.get(`${d.base_url}/balance`,{params:s}))}catch(s){return yield d.commonSrv.showToast({message:"Une erreur est survenue lors de la r\xe9cup\xe9ration de votre solde",color:"danger"}),s}})()}getUsersCompany(C,d){var s=this;return(0,l.A)(function*(){try{let m=new E.Nl;const{email:M,enable:y=!0}=d;return M&&(m=m.append("email",M)),m=m.append("enable",y),yield(0,a.s)(s.http.get(`${s.base_url}/${C}/users`,{params:m}))}catch(m){return yield s.commonSrv.showToast({message:"Une erreur est survenue lors de la r\xe9cup\xe9ration de vos informations",color:"danger"}),m}})()}static{this.\u0275fac=function(d){return new(d||n)(P.KVO(u.K),P.KVO(E.Qq),P.KVO(v.h),P.KVO(R.n),P.KVO(x.c$))}}static{this.\u0275prov=P.jDH({token:n,factory:n.\u0275fac,providedIn:"root"})}}return n})()},91285:(Y,w,g)=>{g.d(w,{y:()=>n});var l=g(99987),a=g(2978),f=g(77897),E=g(62049),r=g(82571),P=g(37222),u=g(56610);function v(_,C){if(1&_){const d=a.RV6();a.j41(0,"div",6)(1,"label",7),a.EFF(2),a.k0s(),a.j41(3,"ion-item",8)(4,"ion-textarea",9),a.bIt("ngModelChange",function(m){a.eBV(d);const M=a.XpG();return a.Njj(M.annulationMessage=m)}),a.k0s()()()}if(2&_){const d=a.XpG();a.R7$(2),a.JRh(null==d.dataModal?null:d.dataModal.message),a.R7$(2),a.Y8G("ngModel",d.annulationMessage)}}function R(_,C){if(1&_){const d=a.RV6();a.j41(0,"ion-button",10),a.bIt("click",function(){a.eBV(d);const m=a.XpG();return a.Njj(m.cancel())}),a.j41(1,"ion-label"),a.EFF(2),a.nI1(3,"titlecase"),a.k0s()()}if(2&_){const d=a.XpG();a.R7$(2),a.JRh(a.bMT(3,1,null==d.dataModal?null:d.dataModal.cancelButton))}}const x=function(_){return{"annulation-mode":_}},F=function(_){return{"single-button":_}};let n=(()=>{class _{constructor(d,s,m){this.modalCtrl=d,this.translateService=s,this.commonSrv=m,this.annulationMessage=""}ngOnInit(){}cancel(){return this.modalCtrl.dismiss(null,"annuler")}confirm(){let d=this.annulationMessage;this.dataModal.isAnnulation&&!this.annulationMessage.trim()&&(d=this.translateService.currentLang===l.T.French?"\xc0 la demande du client":"At the customers request"),this.dataModal.handler(d),this.modalCtrl.dismiss(this.dataModal.isAnnulation?{message:d}:null,"confirm")}static{this.\u0275fac=function(s){return new(s||_)(a.rXU(f.W3),a.rXU(E.E),a.rXU(r.h))}}static{this.\u0275cmp=a.VBU({type:_,selectors:[["app-base-modal"]],inputs:{dataModal:"dataModal"},decls:12,vars:12,consts:[["id","container",1,"scroller-container",3,"ngClass"],[1,"contain-text"],["class","message-container",4,"ngIf"],[1,"btn-validate",3,"ngClass"],["fill","solid","class","cancel",3,"click",4,"ngIf"],["fill","solid","color","primary",1,"yes",3,"click"],[1,"message-container"],[1,"message"],[1,"message-input"],["rows","4",1,"custom-textarea",3,"ngModel","ngModelChange"],["fill","solid",1,"cancel",3,"click"]],template:function(s,m){1&s&&(a.j41(0,"section",0)(1,"div",1)(2,"label"),a.EFF(3),a.nrm(4,"span"),a.k0s()(),a.DNE(5,v,5,2,"div",2),a.j41(6,"div",3),a.DNE(7,R,4,3,"ion-button",4),a.j41(8,"ion-button",5),a.bIt("click",function(){return m.confirm()}),a.j41(9,"ion-label"),a.EFF(10),a.nI1(11,"titlecase"),a.k0s()()()()),2&s&&(a.Y8G("ngClass",a.eq3(8,x,null==m.dataModal?null:m.dataModal.isAnnulation)),a.R7$(3),a.SpI(" ",null==m.dataModal?null:m.dataModal.text," "),a.R7$(2),a.Y8G("ngIf",null==m.dataModal?null:m.dataModal.isAnnulation),a.R7$(1),a.Y8G("ngClass",a.eq3(10,F,null==m.dataModal?null:m.dataModal.isAnnulation)),a.R7$(1),a.Y8G("ngIf",!(null!=m.dataModal&&m.dataModal.isAnnulation)),a.R7$(3),a.JRh(a.bMT(11,6,null==m.dataModal?null:m.dataModal.confirmButton)))},dependencies:[P.BC,P.vS,f.Jm,f.uz,f.he,f.nc,f.Gw,u.YU,u.bT,u.PV],styles:['*[_ngcontent-%COMP%]{margin:0;padding:0}.header-md[_ngcontent-%COMP%]:after{background-image:none!important}.modal[_ngcontent-%COMP%]{--border-radius: calc(60 * var(--res))}.input-group[_ngcontent-%COMP%]{margin-top:calc(75 * var(--res))}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{--padding-start: 0;margin-bottom:calc(50 * var(--res));--placeholder-color: $color-six}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-input[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{font-family:Mont Regular;font-weight:400;font-size:calc(42 * var(--res));color:#1e1e1e}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{padding-top:calc(25 * var(--res));padding-bottom:calc(25 * var(--res))}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]{margin-bottom:calc(50 * var(--res));display:flex;align-items:center;justify-content:space-between;border-bottom-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-250, #c8c7cc)));border-bottom-width:.55px;border-bottom-style:solid}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{width:90%;margin-bottom:0;--border-color: transparent;--inner-border-width: 0px 0px 0px 0px;--border-style: none}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-checkbox[_ngcontent-%COMP%]{margin-top:5%}.bg-primary[_ngcontent-%COMP%]{color:#143c5d}.message[_ngcontent-%COMP%]{margin-bottom:calc(75 * var(--res))}.message[_ngcontent-%COMP%]   ion-title[_ngcontent-%COMP%]{font-weight:800;font-size:calc(70 * var(--res));padding-bottom:calc(25 * var(--res))}.message[_ngcontent-%COMP%]   ion-text[_ngcontent-%COMP%]{font-weight:400;color:#757474;display:block;line-height:1.5em!important}.btn--meduim[_ngcontent-%COMP%]{height:calc(130 * var(--res));font-size:calc(50 * var(--res));font-family:var(--mont-bold)}.btn--meduim[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:var(--mont-bold)}.btn--small[_ngcontent-%COMP%]{height:calc(100 * var(--res))}.btn--round[_ngcontent-%COMP%]{--border-radius: 50%;padding-inline-start:0;padding-inline-end:0;--padding-start: 0;--padding-end: 0}.btn--border-primary[_ngcontent-%COMP%]{--background: transparent;--background-activated: #c4e6d0;--border-color: var(--ion-color-primary);--border-style: solid;--border-width: 2px}.btn--border-secondary[_ngcontent-%COMP%]{--border-width: 2px;--background: transparent;--background-activated: #ddabb2;--border-color: var(--ion-color-secondary);--border-style: solid}.btn--upper[_ngcontent-%COMP%]{text-transform:uppercase}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]{overflow:hidden}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar{width:5px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-track{box-shadow:inset 0 0 5px #546e8d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background:#143c5d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover{background:#143c5d}.bottom-sheet-content[_ngcontent-%COMP%]{background:#fdfdfe;height:100%}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-thumbnail[_ngcontent-%COMP%]{margin-left:calc(31.25 * var(--res));background-color:#1e1e1e;--border-radius: 50%;--size: 23}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:Mont Bold;font-size:calc(45 * var(--res));text-align:center;color:#757474}.bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%], .bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]{padding:calc(75 * var(--res))}.font-size-5[_ngcontent-%COMP%]{font-size:calc(42 * var(--res))}#container[_ngcontent-%COMP%]{height:100%;padding:var(--container-padding);background-color:#fff;border-top-left-radius:calc(50 * var(--res));border-top-right-radius:calc(50 * var(--res));display:flex;flex-direction:column}#container[_ngcontent-%COMP%]   .contain-text[_ngcontent-%COMP%]{text-align:center;font-family:var(--mont-regular);color:#143c5d;line-height:1;margin-bottom:var(--container-margin)}#container[_ngcontent-%COMP%]   .contain-text[_ngcontent-%COMP%]   label[_ngcontent-%COMP%]{color:"#0B305C";font-size:20px;text-align:center;display:block;margin-bottom:16px;font-family:var(--mont-bold)}#container[_ngcontent-%COMP%]   .contain-text[_ngcontent-%COMP%]   label[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{color:#143c5d}#container[_ngcontent-%COMP%]   .message-container[_ngcontent-%COMP%]{margin:14px 0}#container[_ngcontent-%COMP%]   .message-container[_ngcontent-%COMP%]   .message[_ngcontent-%COMP%]{font-size:14px;font-weight:400;display:block;margin-bottom:8px;text-align:center;font-family:var(--mont-regular)}#container[_ngcontent-%COMP%]   .message-container[_ngcontent-%COMP%]   .message-input[_ngcontent-%COMP%]{--background: #f8f9fa;--border-radius: 8px;--padding-start: 12px;--padding-end: 12px;border:1px solid rgba(20,60,93,.5);border-radius:8px}#container[_ngcontent-%COMP%]   .message-container[_ngcontent-%COMP%]   .message-input[_ngcontent-%COMP%]   .custom-textarea[_ngcontent-%COMP%]{--padding-top: 8px;--padding-bottom: 8px;min-height:100px;font-size:14px}#container[_ngcontent-%COMP%]   .btn-validate[_ngcontent-%COMP%]{padding:0 calc(41 * var(--res));display:flex;flex-direction:row;justify-content:center;margin-top:13px;gap:1em}#container[_ngcontent-%COMP%]   .btn-validate.single-button[_ngcontent-%COMP%]{gap:0}#container[_ngcontent-%COMP%]   .btn-validate.single-button[_ngcontent-%COMP%]   .yes[_ngcontent-%COMP%]{width:100%;max-width:none}#container[_ngcontent-%COMP%]   .btn-validate[_ngcontent-%COMP%]   .cancel[_ngcontent-%COMP%]{--background: #fff;width:120px;border-radius:10px;border:1px solid #143c5d;font-family:var(--mont-semibold);color:#143c5d;font-size:10px}#container[_ngcontent-%COMP%]   .btn-validate[_ngcontent-%COMP%]   .yes[_ngcontent-%COMP%]{width:120px;border-radius:10px;border:1px solid #143c5d;font-family:var(--mont-semibold);font-size:10px}#container[_ngcontent-%COMP%]   .btn-validate[_ngcontent-%COMP%]   .yes[disabled][_ngcontent-%COMP%]{opacity:.5}#container.annulation-mode[_ngcontent-%COMP%]   .btn-validate[_ngcontent-%COMP%]{padding:0 16px}#container.annulation-mode[_ngcontent-%COMP%]   .btn-validate[_ngcontent-%COMP%]   .yes[_ngcontent-%COMP%]{width:100%;font-size:16px}']})}}return _})()},11244:(Y,w,g)=>{g.d(w,{F:()=>a});var l=g(2978);let a=(()=>{class f{transform(r){return console.log(),`${r?.slice(0,1)?.toLocaleUpperCase()+r?.slice(1)?.toLocaleLowerCase()}`}static{this.\u0275fac=function(P){return new(P||f)}}static{this.\u0275pipe=l.EJ8({name:"capitalize",type:f,pure:!0})}}return f})()}}]);