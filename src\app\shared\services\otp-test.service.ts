import { Injectable } from '@angular/core';
import { Platform } from '@ionic/angular';

@Injectable({
  providedIn: 'root'
})
export class OtpTestService {
  
  constructor(private platform: Platform) {}

  /**
   * Simule la réception d'un SMS OTP pour les tests en développement
   * @param callback Fonction appelée avec le code OTP simulé
   * @param delay Délai avant simulation (en ms)
   */
  simulateOtpReceived(callback: (otp: string) => void, delay: number = 3000): void {
    console.log('[OTP-TEST] Simulation d\'un SMS OTP dans', delay, 'ms...');
    console.log('[OTP-TEST] Plateforme:', this.platform.platforms());

    setTimeout(() => {
      // Générer un code OTP aléatoire de 6 chiffres
      const otp = Math.floor(100000 + Math.random() * 900000).toString();
      console.log('[OTP-TEST] SMS simulé reçu avec le code:', otp);
      callback(otp);
    }, delay);
  }

  /**
   * Simule différents formats de SMS OTP
   */
  simulateVariousOtpFormats(callback: (otp: string) => void): void {
    const formats = [
      '123456',                                    // Code simple
      'Votre code OTP: 234567',                  // Format avec texte
      'Code de vérification: "345678"',          // Avec guillemets
      'PIN: 456789 pour votre connexion',        // Format PIN
      'Utilisez le code 567890 pour vous connecter' // Code dans phrase
    ];

    const randomFormat = formats[Math.floor(Math.random() * formats.length)];
    console.log('[OTP-TEST] SMS simulé:', randomFormat);
    
    // Extraire le code du format choisi
    const match = randomFormat.match(/\d{6}/);
    if (match) {
      setTimeout(() => callback(match[0]), 2000);
    }
  }

  /**
   * Teste l'extraction d'OTP depuis le presse-papiers
   */
  async testClipboardOtp(callback: (otp: string) => void): Promise<void> {
    try {
      if (navigator.clipboard && navigator.clipboard.writeText) {
        const testOtp = '123456';
        await navigator.clipboard.writeText(`Votre code OTP: ${testOtp}`);
        console.log('[OTP-TEST] Code OTP copié dans le presse-papiers pour test');
        
        setTimeout(() => {
          callback(testOtp);
        }, 1000);
      }
    } catch (error) {
      console.error('[OTP-TEST] Erreur lors du test presse-papiers:', error);
    }
  }

  /**
   * Affiche les instructions de test pour l'utilisateur
   */
  showTestInstructions(): void {
    console.log(`
    🧪 INSTRUCTIONS DE TEST OTP AUTO-FILL:
    
    1. En mode développement (navigateur):
       - Un code OTP sera automatiquement simulé après 3 secondes
       
    2. Sur Android (device/émulateur):
       - Envoyez-vous un SMS avec un code à 6 chiffres
       - L'application devrait détecter automatiquement le code
       
    3. Test manuel:
       - Copiez un code dans le presse-papiers
       - L'application peut le détecter selon la plateforme
       
    4. Formats de SMS supportés:
       - "123456"
       - "Code: 123456"
       - "OTP: 123456"
       - "Votre code de vérification: 123456"
    `);
  }
}
