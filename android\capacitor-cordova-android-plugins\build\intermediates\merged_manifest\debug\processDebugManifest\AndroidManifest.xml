<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:amazon="http://schemas.amazon.com/apk/res/android"
    xmlns:android="http://schemas.android.com/apk/res/android"
    package="capacitor.cordova.android.plugins" >

    <uses-sdk android:minSdkVersion="23" />

    <uses-feature
        android:name="android.hardware.telephony"
        android:required="false" />

    <application>
        <receiver
            android:name="com.andreszs.smsretriever.SMSBroadcastReceiver"
            android:exported="true"
            android:permission="com.google.android.gms.auth.api.phone.permission.SEND" >
            <intent-filter>
                <action android:name="com.google.android.gms.auth.api.phone.SMS_RETRIEVED" />
            </intent-filter>
        </receiver>
    </application>

</manifest>