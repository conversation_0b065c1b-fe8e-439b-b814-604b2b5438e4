<div class="recap-container" [ngStyle]="{'box-shadow': isPoint && 'none'}">
  <div class="padding-horizontal" *ngIf="!isPoint">
    <ion-label class="title recap-title">{{"order-new-page.third-step.title" | translate}}</ion-label>
  </div>

  <ul class="product-list">
    <li class="list-elt head">
      <ion-label class="col"><strong>{{"order-new-page.third-step.product" | translate}}</strong></ion-label>
      <ion-label class="col"><strong>{{"order-new-page.third-step.qte" | translate}} ({{"reseller-new-page.detail.bags"
          | translate}})</strong></ion-label>
      <ion-label class="col"
        *ngIf="!isCompanyCategoryBaker && !isPoint && (commonSrv?.user?.category !== userCategory.Particular && commonSrv?.user?.category !== userCategory.Commercial)"><strong>{{"order-new-page.third-step.unitPrice"
          | translate}}</strong></ion-label>
      <ion-label class="col" *ngIf="isPoint"><strong>Points</strong></ion-label>
    </li>

    <div *ngFor="let item of cart?.items">
      <li class="list-elt">
        <div class="col product">
          <ion-label>{{item?.product?.label | capitalize}}</ion-label>
          <ion-label>{{item?.packaging?.label}}</ion-label>
        </div>
        <ion-label class="col">
          <ion-img src="{{item?.product?.image}}"></ion-img>
        </ion-label>
        <ion-label class="col">{{item?.quantity}}</ion-label>
        <ion-label class="col"
          *ngIf="!isCompanyCategoryBaker && !isPoint && [userCategory.Particular, userCategory.Commercial, userCategory.DonutAnimator ].includes(commonSrv?.user?.category)">{{item?.unitPrice
          | number: '': 'fr'}} XAF</ion-label>
        <ion-label class="col" *ngIf="isPoint">{{getPointsForItem(item) | number: '': 'fr'}} PTS
        </ion-label>
      </li>
      <li class=" list-elt line">
      </li>
    </div>

    <li class="list-elt footer">
      <ion-label class="col fextrabold"><strong>TOTAL {{"order-new-page.third-step.flour" |
          translate}}</strong></ion-label>
      <ion-label class="col fextrabold"><strong>{{cart?.items | getTotalQuantityOfProductsInCart}}
          {{"order-new-page.third-step.parcel" | translate}}</strong></ion-label>
      <ion-label class="col fextrabold"
        *ngIf="!isCompanyCategoryBaker && !isPoint && (commonSrv?.user?.category !== userCategory.Particular && commonSrv?.user?.category !== userCategory.Commercial)"><strong>{{
          orderPrice?.cartAmount
          | number: '': 'fr' }} XAF</strong></ion-label>
      <ion-label class="col fextrabold" *ngIf="isPoint"><strong>{{ getTotalPoints() | number: '': 'fr' }} PTS</strong></ion-label>
    </li>
  </ul>

  <div class="divider"></div>

  <div class="padding-horizontal"
    *ngIf="shippingInfo?.totalShippingPrice && !isCompanyCategoryBaker && commonSrv?.user?.category !== userCategory.Particular">
    <ion-label class="title recap-title">{{"order-new-page.third-step.title-shipping" | translate}}</ion-label>
  </div>

  <li class="padding-horizontal sub-title" *ngIf="cart?.renderType === renderType.RENDU">
    <!-- <ion-text class="fbold">TVA (19.25%)</ion-text> -->
    <ion-text>{{"order.detail.delivery.date" | translate}}</ion-text>
    <ion-text>{{ (shipping?.deliveryDate | date : 'dd/MM/yyyy': 'fr') || 'N/A' }}</ion-text>
  </li>
  <ul class="product-list"
    *ngIf="shippingInfo?.totalShippingPrice && !isCompanyCategoryBaker && (commonSrv?.user?.category !== userCategory.Particular || commonSrv?.user?.category !== userCategory.Commercial)">
    <li class="list-elt head">
      <ion-label class="col equal"><strong>{{("order-new-page.second-step.bag-off" | translate) |
          titlecase}}(50Kg)</strong></ion-label>
      <ion-label class="col equal"><strong>{{"order-new-page.third-step.unitCost" | translate}}</strong></ion-label>
      <ion-label class="col"><strong>TOTAL</strong></ion-label>
    </li>
    <div>
      <li class="list-elt">
        <div class="col equal">{{shippingInfo?.totalNumberOfBags | number: '': 'fr' }} </div>
        <ion-label class="col equal">{{shippingInfo?.amount | number: '': 'fr' }} </ion-label>
        <ion-label class="col">{{shippingInfo?.totalShippingPrice | number: '': 'fr' }} XAF</ion-label>
      </li>
    </div>
  </ul>

  <div class="product-container">
    <app-item-view [item]="item" *ngFor="let item of itemsLimited; trackBy: commonSrv?.trackByFn"></app-item-view>
  </div>
  <ion-text class="see-more" color="secondary" *ngIf="cart?.items?.length > 3 && !isPoint" (click)="openListOfItems()">
    {{"order-new-page.third-step.seeMore" | translate}}
    <ion-icon name="arrow-forward-outline"></ion-icon>
  </ion-text>
  <ion-text class="see-more" color="secondary" *ngIf="!isPoint">
    {{"order-new-page.third-step.total-tons" | translate}} : {{getTotalQuantityOrder(itemsLimited) / 20 }} T
  </ion-text>
  <ion-text class="see-more" color="secondary" *ngIf="isPoint">
    TOTAL POINT(S) : {{getTotalPoints() | number: '': 'fr'}} Pt(s)
  </ion-text>

  <ul class="detail-order padding-horizontal"
    *ngIf="!isCompanyCategoryBaker && ![userCategory.Particular, userCategory.Commercial, userCategory.DonutAnimator].includes(commonSrv?.user?.category) ">
    <div class="border">
      <li>
        <ion-text class="fbold">{{ 'order-new-page.third-step.amount-ht' | translate }}</ion-text>
        <ion-text class="fbold">{{ orderPrice?.HT | number: '': 'fr' }} XAF</ion-text>
      </li>
      <li>
        <ion-text>{{ 'order-new-page.third-step.taxes' | translate }}</ion-text>
        <ion-text>{{ orderPrice?.VAT | number: '': 'fr' }} XAF</ion-text>
      </li>
      <li>
        <ion-text class="fbold">{{ 'order-new-page.third-step.cart-amount' | translate }}</ion-text>
        <ion-text class="fbold">{{ orderPrice?.cartAmount | number: '': 'fr' }} XAF</ion-text>
      </li>
      <li *ngFor="let disc of orderPrice?.discount | keyvalue; trackBy: commonSrv?.trackByFn">
        <ion-text>{{disc?.key }}</ion-text>
        <ion-text class="positive">- {{ disc?.value | number: '': 'fr' }} XAF</ion-text>
      </li>

      <li *ngIf="orderPrice?.shipping && orderPrice?.shipping !== 0">
        <ion-text>{{ 'order-new-page.third-step.shipping-costs' | translate }}</ion-text>
        <ion-text>{{ orderPrice?.shipping | number: '': 'fr' }} XAF</ion-text>
      </li>
      <li>
        <ion-text class="fextrabold">{{ 'order-new-page.third-step.total-ttc' | translate }}</ion-text>
        <ion-text class="fextrabold" color="secondary">
          {{ orderPrice?.TTC | number: '': 'fr' }} XAF
        </ion-text>
      </li>
    </div>
  </ul>

  <!-- <div *ngIf="(isLoading || !isDiscount) && commonSrv.user?.authorizations.includes(promoCodeAction.VIEW) "
    class="type-truck">
    <label class="title">Voulez-vous utiliser un code promo?</label>
    <ion-toggle (ionChange)="codePromoInfosHandle($event)" [checked]="isCodePromoActive"></ion-toggle>
  </div> -->
  <ul
    *ngIf="(isDiscount && orderPrice.discount) && ![userCategory.Particular, userCategory.Commercial,userCategory.DonutAnimator].includes(commonSrv?.user?.category)"
    class="detail-order padding-horizontal discount-details">
    <li>
      <ion-text class="fbold">Nouveau TTC</ion-text>
      <ion-text *ngIf="orderPrice" class="fextrabold"> {{ orderPrice?.TTC | number : '' : 'fr' }} XAF</ion-text>
    </li>
    <li>
      <ion-text class="fbold">Reduction</ion-text>
      <ion-text *ngIf="orderPrice" class="fextrabold"> {{ orderPrice?.discount?.promoCode | number : '' : 'fr' }}
        XAF</ion-text>
    </li>
    <button class="cancel-discount" (click)="cancelDiscount()">Annuler le code promo</button>
  </ul>
  <div class="padding-horizontal"
    *ngIf="!choiceDelivery && [userCategory?.CompanyUser ].includes(commonSrv?.user?.category)">
    <ion-label class="title recap-title">{{"order-new-page.third-step.carrier-title" | translate}}</ion-label>
  </div>

  <ul class="detail-order padding-horizontal"
    *ngIf="!choiceDelivery && [userCategory?.CompanyUser].includes(commonSrv?.user?.category)">
    <div class="border">
      <li>
        <ion-text class="title">{{ "order-new-page.first-step.driver-name" | translate | capitalize | truncateString:12
          }} :</ion-text>
        <ion-text class="value">{{ carrierOrderInformation?.name ||carrierInformation?.name || 'N/A' }}</ion-text>
      </li>
      <li>
        <ion-text class="title">{{ "bottom-sheet-validation.tel" | translate | capitalize }} :</ion-text>
        <ion-text class="value">{{ carrierOrderInformation?.phone || carrierInformation?.phone || 'N/A' }}</ion-text>
      <li>
        <ion-text class="title">{{ "order-new-page.first-step.driver-id" | translate | capitalize | truncateString:18 }}
          :</ion-text>
        <ion-text class="value">{{ carrierOrderInformation?.idCard || carrierInformation?.idCard || 'N/A' }}</ion-text>
      </li>
      <li>
        <ion-text class="title">{{ "order-new-page.first-step.driver-category" | translate | capitalize |
          truncateString:16 }} :</ion-text>
        <ion-text class="value">{{ carrierOrderInformation?.vehicleCategory || carrierInformation?.vehicleCategory ||
          'N/A' | truncateString:12 }}</ion-text>
      </li>
      <li>
        <ion-text class="title">{{ "order-new-page.first-step.driver-vehicle" | translate | capitalize | truncateString:
          15 }} :</ion-text>
        <ion-text class="value">{{ carrierOrderInformation?.vehiclePlate || carrierInformation?.vehiclePlate || 'N/A'
          }}</ion-text>
      </li>
      <li>
        <ion-text class="title">{{ "order-new-page.first-step.driver-license" | translate | capitalize |
          truncateString:15 }} :</ion-text>
        <ion-text class="value">{{ carrierOrderInformation?.driverLicense || carrierInformation?.driverLicense || 'N/A'
          }}</ion-text>
      </li>
      <li>
        <ion-text class="title"> {{"order-new-page.first-step.delivery-location" | translate | capitalize |
          truncateString:15}} :</ion-text>
        <ion-text class="value">{{ customerDeliveryDestination || 'N/A' }}</ion-text>
      </li>
    </div>
  </ul>
</div>