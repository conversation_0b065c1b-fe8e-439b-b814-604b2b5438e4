-- Merging decision tree log ---
provider#androidx.core.content.FileProvider
INJECTED from C:\Users\<USER>\Desktop\Projet LONDO TECH\LA PASTA\la-pasta-mobile\android\app\src\main\AndroidManifest.xml:25:9-31:20
	android:grantUriPermissions
		ADDED from C:\Users\<USER>\Desktop\Projet LONDO TECH\LA PASTA\la-pasta-mobile\android\app\src\main\AndroidManifest.xml:29:13-47
	android:authorities
		INJECTED from C:\Users\<USER>\Desktop\Projet LONDO TECH\LA PASTA\la-pasta-mobile\android\app\src\main\AndroidManifest.xml
		ADDED from C:\Users\<USER>\Desktop\Projet LONDO TECH\LA PASTA\la-pasta-mobile\android\app\src\main\AndroidManifest.xml:27:13-64
	android:exported
		ADDED from C:\Users\<USER>\Desktop\Projet LONDO TECH\LA PASTA\la-pasta-mobile\android\app\src\main\AndroidManifest.xml:28:13-37
	android:name
		ADDED from C:\Users\<USER>\Desktop\Projet LONDO TECH\LA PASTA\la-pasta-mobile\android\app\src\main\AndroidManifest.xml:26:13-62
manifest
ADDED from C:\Users\<USER>\Desktop\Projet LONDO TECH\LA PASTA\la-pasta-mobile\android\app\src\main\AndroidManifest.xml:2:1-46:12
INJECTED from C:\Users\<USER>\Desktop\Projet LONDO TECH\LA PASTA\la-pasta-mobile\android\app\src\main\AndroidManifest.xml:2:1-46:12
INJECTED from C:\Users\<USER>\Desktop\Projet LONDO TECH\LA PASTA\la-pasta-mobile\android\app\src\main\AndroidManifest.xml:2:1-46:12
INJECTED from C:\Users\<USER>\Desktop\Projet LONDO TECH\LA PASTA\la-pasta-mobile\android\app\src\main\AndroidManifest.xml:2:1-46:12
MERGED from [:capacitor-mlkit-barcode-scanning] C:\Users\<USER>\Desktop\Projet LONDO TECH\LA PASTA\la-pasta-mobile\node_modules\@capacitor-mlkit\barcode-scanning\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:capacitor-app] C:\Users\<USER>\Desktop\Projet LONDO TECH\LA PASTA\la-pasta-mobile\node_modules\@capacitor\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:capacitor-camera] C:\Users\<USER>\Desktop\Projet LONDO TECH\LA PASTA\la-pasta-mobile\node_modules\@capacitor\camera\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-13:12
MERGED from [:capacitor-geolocation] C:\Users\<USER>\Desktop\Projet LONDO TECH\LA PASTA\la-pasta-mobile\node_modules\@capacitor\geolocation\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:capacitor-android] C:\Users\<USER>\Desktop\Projet LONDO TECH\LA PASTA\la-pasta-mobile\node_modules\@capacitor\android\capacitor\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:capacitor-cordova-android-plugins] C:\Users\<USER>\Desktop\Projet LONDO TECH\LA PASTA\la-pasta-mobile\android\capacitor-cordova-android-plugins\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-23:12
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c11b56a366e16dbf3cf5030eb16512bb\transformed\jetified-appcompat-resources-1.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.camera:camera-view:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8686ad6457215cf3e729f8f9398d03e3\transformed\jetified-camera-view-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\945fa3d50eeded1ef19658fca0a2b0ed\transformed\material-1.12.0\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.mlkit:barcode-scanning:17.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bbf4359ae6fe5117f4f4f2d0078eecf\transformed\jetified-barcode-scanning-17.3.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-code-scanner:16.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\809000ee9483f32dd44add99df2c7cb4\transformed\jetified-play-services-code-scanner-16.1.0\AndroidManifest.xml:2:1-23:12
MERGED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d55bd8fa6ecdc0a194a8fd32ba400784\transformed\jetified-play-services-mlkit-barcode-scanning-18.3.1\AndroidManifest.xml:2:1-18:12
MERGED from [com.google.mlkit:barcode-scanning-common:17.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8b92c10a4d64eeb41d7cd74f8bd1ee08\transformed\jetified-barcode-scanning-common-17.0.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b063b32a14ce5f5b3194ab73cfc4ee14\transformed\jetified-vision-common-17.3.0\AndroidManifest.xml:2:1-18:12
MERGED from [com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a5a91bd578d149a6c95bbcc3efd16a9d\transformed\jetified-common-18.11.0\AndroidManifest.xml:2:1-26:12
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8b36869effc731647c2277216923cf4f\transformed\constraintlayout-2.0.1\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2cf62e072a2b5b58d074505e6258e35d\transformed\appcompat-1.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20bfa38b8097c2e16311622c17bc8491\transformed\coordinatorlayout-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.core:core-splashscreen:1.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dff148f4042a0d21e144a502e46c29ee\transformed\jetified-core-splashscreen-1.0.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.gms:play-services-auth:21.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c08f3e536f5af5cebd7b0f2a9c8eb2d1\transformed\jetified-play-services-auth-21.2.0\AndroidManifest.xml:17:1-40:12
MERGED from [com.google.android.gms:play-services-auth-api-phone:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\63de66f36c31d59182f8f377404e8cff\transformed\jetified-play-services-auth-api-phone-18.1.0\AndroidManifest.xml:2:1-6:12
MERGED from [io.ionic.libs:iongeolocation-android:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a6576c2c17594dad09737e495f0c6b9\transformed\jetified-iongeolocation-android-1.0.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-location:21.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dc07be8d86bc45e01fcf98f6fcfc1ed3\transformed\jetified-play-services-location-21.3.0\AndroidManifest.xml:2:1-8:12
MERGED from [com.google.android.gms:play-services-auth-base:18.0.10] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d723a3c58d6b2f81c91873185bc12831\transformed\jetified-play-services-auth-base-18.0.10\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-fido:20.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\be404c093f6b34d42cf7d1e7fcb12f45\transformed\jetified-play-services-fido-20.0.1\AndroidManifest.xml:2:1-10:12
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\28141fbb4925ebb9bc097857c92d6d27\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.mlkit:vision-interfaces:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9a6dc87ddd5c10b8bc588980ddd5d441\transformed\jetified-vision-interfaces-16.3.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ae9ca3e727414881ef14d800b97bc389\transformed\jetified-emoji2-views-helper-1.3.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\543aa4d8cde29bfa9a5da326fa5b8380\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.webkit:webkit:1.12.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e1c76e2fe55f3535ab45dbd228d4fb29\transformed\webkit-1.12.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.camera:camera-camera2:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8bcc53a3834ebaf77da2288f604a904c\transformed\jetified-camera-camera2-1.1.0\AndroidManifest.xml:17:1-38:12
MERGED from [androidx.camera:camera-lifecycle:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8a4c964426c70d05775a8b2176efc5da\transformed\jetified-camera-lifecycle-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.camera:camera-core:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\97eaf6809b81bfd23a7279edcdf74ac7\transformed\jetified-camera-core-1.1.0\AndroidManifest.xml:17:1-38:12
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7e9e800edbded6d5bbfc17ee9d64ede7\transformed\transition-1.5.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e842251be9ddcb7de6cb939b5733fd97\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e9a9bfa0516b4a036cc191a74b0b965a\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dc3df7c8c37a8848dab522b0ab2c2e76\transformed\loader-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2d69a97326794d3edb183c7e99d68234\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6efb25ac1868e901121fb137de071f9a\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.activity:activity-ktx:1.9.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c4c9bd491c09227ce5efaceaa54efabb\transformed\jetified-activity-ktx-1.9.3\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.core:core-ktx:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3bebeae163ca76362a715c293a45aaf4\transformed\jetified-core-ktx-1.15.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\76615b94e9940687e89933d4afb081d1\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0d3af2e6159c29b73ff064d48ce7c71d\transformed\drawerlayout-1.1.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2fd7b19c05ac6df2130c61f79ed9c75d\transformed\jetified-viewpager2-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\28e7b1792d1cdcff7bf27709b8f15dee\transformed\recyclerview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cfbca8b2089c9692833971460f9f59ac\transformed\customview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e218b8c118997427953592d90c2fa2e8\transformed\core-1.15.0\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c29d2f3f518fcf79a21a2e1a09538b7e\transformed\jetified-lifecycle-viewmodel-ktx-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f1c1167d9b54a36d7bb6615354af0e5\transformed\jetified-lifecycle-runtime-ktx-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\acfbc11e919c8c9a914cb45056e6bac8\transformed\lifecycle-viewmodel-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\900e43aff56f75a168826022aabdc33d\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\470b12e0a3da5e0343e1069480b44ea8\transformed\lifecycle-livedata-core-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0763fd4d23f1722ee96679c9117c7ae7\transformed\lifecycle-livedata-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3be6ade46fd06acc1d07bcab870d36b4\transformed\lifecycle-runtime-2.6.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b06e433ecdf349db09786d98d126aaed\transformed\jetified-lifecycle-viewmodel-savedstate-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4f75e71e070c07fea476289e65f78a37\transformed\jetified-savedstate-ktx-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73212289890cd8344d93bc39155522c6\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4cd12d2b992842edee61ed3d342a9313\transformed\jetified-play-services-tasks-18.2.0\AndroidManifest.xml:2:1-5:12
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ad29408dc46c7a7882a72a73e429c1d5\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.fragment:fragment:1.8.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\65d79c4a22703368e61b9ba3c5adc9ca\transformed\fragment-1.8.4\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity:1.9.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fb7bff88ae3c124681b3c5315e0965b\transformed\jetified-activity-1.9.3\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f2a226698dc38541b3e28cbe42764386\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bb57c83c4e4395aacfd7e9ff476c67e8\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.exifinterface:exifinterface:1.3.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c556d96b83bca5df3ca71d2e14bc0fbc\transformed\exifinterface-1.3.7\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5f5fb8ab546355d878e03be984ccdc65\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f1f73d6c7ff79b14cda1437c4b5ee86a\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4a3011e93b65bf797eb779714a515e36\transformed\jetified-tracing-1.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7bdf44d8c457a9d550de94e7018fb51f\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\06c45ee83264f0a53a1a2872f349892b\transformed\jetified-transport-backend-cct-2.3.3\AndroidManifest.xml:15:1-38:12
MERGED from [com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a48f688fa386ce7aed0a486b8b5ba154\transformed\jetified-transport-runtime-2.2.6\AndroidManifest.xml:15:1-41:12
MERGED from [com.google.android.datatransport:transport-api:2.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6bb886c7ab3b92899a469b0fbc07cb2c\transformed\jetified-transport-api-2.2.1\AndroidManifest.xml:15:1-22:12
MERGED from [com.google.firebase:firebase-components:16.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\614ae7eb9ba7b31fc1366be97ea3cb53\transformed\jetified-firebase-components-16.1.0\AndroidManifest.xml:15:1-23:12
MERGED from [com.google.firebase:firebase-encoders-json:17.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d8a7deec6c863e15ef763c401c22e79\transformed\jetified-firebase-encoders-json-17.1.0\AndroidManifest.xml:15:1-23:12
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f1a3628cb91fdb1aa9b9c2531cff8fcb\transformed\cardview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2e0f289aa4483338394bb5c319030078\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9200b6dfda72546e7e7f226dc49882f0\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d29630f4f10e19a8e91088cb9f659866\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\61a7139421f24e376021e74fa4cbf528\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d9498bfef8c45ff127d7b7c473b3abab\transformed\jetified-annotation-experimental-1.4.1\AndroidManifest.xml:2:1-7:12
MERGED from [org.apache.cordova:framework:10.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9784ddf63c6cadb3e3bddfa95d23b3fc\transformed\jetified-framework-10.1.1\AndroidManifest.xml:20:1-27:12
MERGED from [com.google.android.odml:image:1.0.0-beta1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\60e6d164477c28ec9c9ea378fe6a87dd\transformed\jetified-image-1.0.0-beta1\AndroidManifest.xml:2:1-9:12
	package
		INJECTED from C:\Users\<USER>\Desktop\Projet LONDO TECH\LA PASTA\la-pasta-mobile\android\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from C:\Users\<USER>\Desktop\Projet LONDO TECH\LA PASTA\la-pasta-mobile\android\app\src\main\AndroidManifest.xml
	android:versionCode
		INJECTED from C:\Users\<USER>\Desktop\Projet LONDO TECH\LA PASTA\la-pasta-mobile\android\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from C:\Users\<USER>\Desktop\Projet LONDO TECH\LA PASTA\la-pasta-mobile\android\app\src\main\AndroidManifest.xml:2:11-69
application
ADDED from C:\Users\<USER>\Desktop\Projet LONDO TECH\LA PASTA\la-pasta-mobile\android\app\src\main\AndroidManifest.xml:3:5-32:19
INJECTED from C:\Users\<USER>\Desktop\Projet LONDO TECH\LA PASTA\la-pasta-mobile\android\app\src\main\AndroidManifest.xml:3:5-32:19
MERGED from [:capacitor-cordova-android-plugins] C:\Users\<USER>\Desktop\Projet LONDO TECH\LA PASTA\la-pasta-mobile\android\capacitor-cordova-android-plugins\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:5-21:19
MERGED from [:capacitor-cordova-android-plugins] C:\Users\<USER>\Desktop\Projet LONDO TECH\LA PASTA\la-pasta-mobile\android\capacitor-cordova-android-plugins\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:5-21:19
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\945fa3d50eeded1ef19658fca0a2b0ed\transformed\material-1.12.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\945fa3d50eeded1ef19658fca0a2b0ed\transformed\material-1.12.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.gms:play-services-code-scanner:16.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\809000ee9483f32dd44add99df2c7cb4\transformed\jetified-play-services-code-scanner-16.1.0\AndroidManifest.xml:8:5-21:19
MERGED from [com.google.android.gms:play-services-code-scanner:16.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\809000ee9483f32dd44add99df2c7cb4\transformed\jetified-play-services-code-scanner-16.1.0\AndroidManifest.xml:8:5-21:19
MERGED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d55bd8fa6ecdc0a194a8fd32ba400784\transformed\jetified-play-services-mlkit-barcode-scanning-18.3.1\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d55bd8fa6ecdc0a194a8fd32ba400784\transformed\jetified-play-services-mlkit-barcode-scanning-18.3.1\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b063b32a14ce5f5b3194ab73cfc4ee14\transformed\jetified-vision-common-17.3.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b063b32a14ce5f5b3194ab73cfc4ee14\transformed\jetified-vision-common-17.3.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a5a91bd578d149a6c95bbcc3efd16a9d\transformed\jetified-common-18.11.0\AndroidManifest.xml:8:5-24:19
MERGED from [com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a5a91bd578d149a6c95bbcc3efd16a9d\transformed\jetified-common-18.11.0\AndroidManifest.xml:8:5-24:19
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8b36869effc731647c2277216923cf4f\transformed\constraintlayout-2.0.1\AndroidManifest.xml:9:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8b36869effc731647c2277216923cf4f\transformed\constraintlayout-2.0.1\AndroidManifest.xml:9:5-20
MERGED from [com.google.android.gms:play-services-auth:21.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c08f3e536f5af5cebd7b0f2a9c8eb2d1\transformed\jetified-play-services-auth-21.2.0\AndroidManifest.xml:22:5-38:19
MERGED from [com.google.android.gms:play-services-auth:21.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c08f3e536f5af5cebd7b0f2a9c8eb2d1\transformed\jetified-play-services-auth-21.2.0\AndroidManifest.xml:22:5-38:19
MERGED from [com.google.android.gms:play-services-location:21.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dc07be8d86bc45e01fcf98f6fcfc1ed3\transformed\jetified-play-services-location-21.3.0\AndroidManifest.xml:6:5-20
MERGED from [com.google.android.gms:play-services-location:21.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dc07be8d86bc45e01fcf98f6fcfc1ed3\transformed\jetified-play-services-location-21.3.0\AndroidManifest.xml:6:5-20
MERGED from [com.google.android.gms:play-services-fido:20.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\be404c093f6b34d42cf7d1e7fcb12f45\transformed\jetified-play-services-fido-20.0.1\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.android.gms:play-services-fido:20.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\be404c093f6b34d42cf7d1e7fcb12f45\transformed\jetified-play-services-fido-20.0.1\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\28141fbb4925ebb9bc097857c92d6d27\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:4:5-6:19
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\28141fbb4925ebb9bc097857c92d6d27\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:4:5-6:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\543aa4d8cde29bfa9a5da326fa5b8380\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\543aa4d8cde29bfa9a5da326fa5b8380\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.camera:camera-camera2:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8bcc53a3834ebaf77da2288f604a904c\transformed\jetified-camera-camera2-1.1.0\AndroidManifest.xml:25:5-36:19
MERGED from [androidx.camera:camera-camera2:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8bcc53a3834ebaf77da2288f604a904c\transformed\jetified-camera-camera2-1.1.0\AndroidManifest.xml:25:5-36:19
MERGED from [androidx.camera:camera-core:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\97eaf6809b81bfd23a7279edcdf74ac7\transformed\jetified-camera-core-1.1.0\AndroidManifest.xml:25:5-36:19
MERGED from [androidx.camera:camera-core:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\97eaf6809b81bfd23a7279edcdf74ac7\transformed\jetified-camera-core-1.1.0\AndroidManifest.xml:25:5-36:19
MERGED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e218b8c118997427953592d90c2fa2e8\transformed\core-1.15.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e218b8c118997427953592d90c2fa2e8\transformed\core-1.15.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\900e43aff56f75a168826022aabdc33d\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\900e43aff56f75a168826022aabdc33d\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:23:5-33:19
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4cd12d2b992842edee61ed3d342a9313\transformed\jetified-play-services-tasks-18.2.0\AndroidManifest.xml:4:5-20
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4cd12d2b992842edee61ed3d342a9313\transformed\jetified-play-services-tasks-18.2.0\AndroidManifest.xml:4:5-20
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ad29408dc46c7a7882a72a73e429c1d5\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:5:5-7:19
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ad29408dc46c7a7882a72a73e429c1d5\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:5:5-7:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bb57c83c4e4395aacfd7e9ff476c67e8\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bb57c83c4e4395aacfd7e9ff476c67e8\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5f5fb8ab546355d878e03be984ccdc65\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5f5fb8ab546355d878e03be984ccdc65\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7bdf44d8c457a9d550de94e7018fb51f\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7bdf44d8c457a9d550de94e7018fb51f\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\06c45ee83264f0a53a1a2872f349892b\transformed\jetified-transport-backend-cct-2.3.3\AndroidManifest.xml:28:5-36:19
MERGED from [com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\06c45ee83264f0a53a1a2872f349892b\transformed\jetified-transport-backend-cct-2.3.3\AndroidManifest.xml:28:5-36:19
MERGED from [com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a48f688fa386ce7aed0a486b8b5ba154\transformed\jetified-transport-runtime-2.2.6\AndroidManifest.xml:25:5-39:19
MERGED from [com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a48f688fa386ce7aed0a486b8b5ba154\transformed\jetified-transport-runtime-2.2.6\AndroidManifest.xml:25:5-39:19
MERGED from [com.google.android.odml:image:1.0.0-beta1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\60e6d164477c28ec9c9ea378fe6a87dd\transformed\jetified-image-1.0.0-beta1\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.odml:image:1.0.0-beta1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\60e6d164477c28ec9c9ea378fe6a87dd\transformed\jetified-image-1.0.0-beta1\AndroidManifest.xml:7:5-20
	android:extractNativeLibs
		INJECTED from C:\Users\<USER>\Desktop\Projet LONDO TECH\LA PASTA\la-pasta-mobile\android\app\src\main\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e218b8c118997427953592d90c2fa2e8\transformed\core-1.15.0\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from C:\Users\<USER>\Desktop\Projet LONDO TECH\LA PASTA\la-pasta-mobile\android\app\src\main\AndroidManifest.xml:8:9-35
	android:label
		ADDED from C:\Users\<USER>\Desktop\Projet LONDO TECH\LA PASTA\la-pasta-mobile\android\app\src\main\AndroidManifest.xml:6:9-41
	android:roundIcon
		ADDED from C:\Users\<USER>\Desktop\Projet LONDO TECH\LA PASTA\la-pasta-mobile\android\app\src\main\AndroidManifest.xml:7:9-54
	android:icon
		ADDED from C:\Users\<USER>\Desktop\Projet LONDO TECH\LA PASTA\la-pasta-mobile\android\app\src\main\AndroidManifest.xml:5:9-43
	android:allowBackup
		ADDED from C:\Users\<USER>\Desktop\Projet LONDO TECH\LA PASTA\la-pasta-mobile\android\app\src\main\AndroidManifest.xml:4:9-35
	android:theme
		ADDED from C:\Users\<USER>\Desktop\Projet LONDO TECH\LA PASTA\la-pasta-mobile\android\app\src\main\AndroidManifest.xml:9:9-40
meta-data#com.google.mlkit.vision.DEPENDENCIES
ADDED from C:\Users\<USER>\Desktop\Projet LONDO TECH\LA PASTA\la-pasta-mobile\android\app\src\main\AndroidManifest.xml:11:9-101
	android:value
		ADDED from C:\Users\<USER>\Desktop\Projet LONDO TECH\LA PASTA\la-pasta-mobile\android\app\src\main\AndroidManifest.xml:11:72-98
	android:name
		ADDED from C:\Users\<USER>\Desktop\Projet LONDO TECH\LA PASTA\la-pasta-mobile\android\app\src\main\AndroidManifest.xml:11:20-71
activity#com.clickcadyst.mobile.MainActivity
ADDED from C:\Users\<USER>\Desktop\Projet LONDO TECH\LA PASTA\la-pasta-mobile\android\app\src\main\AndroidManifest.xml:12:9-23:20
	android:label
		ADDED from C:\Users\<USER>\Desktop\Projet LONDO TECH\LA PASTA\la-pasta-mobile\android\app\src\main\AndroidManifest.xml:15:13-56
	android:launchMode
		ADDED from C:\Users\<USER>\Desktop\Projet LONDO TECH\LA PASTA\la-pasta-mobile\android\app\src\main\AndroidManifest.xml:17:13-44
	android:exported
		ADDED from C:\Users\<USER>\Desktop\Projet LONDO TECH\LA PASTA\la-pasta-mobile\android\app\src\main\AndroidManifest.xml:18:13-36
	android:configChanges
		ADDED from C:\Users\<USER>\Desktop\Projet LONDO TECH\LA PASTA\la-pasta-mobile\android\app\src\main\AndroidManifest.xml:13:13-140
	android:theme
		ADDED from C:\Users\<USER>\Desktop\Projet LONDO TECH\LA PASTA\la-pasta-mobile\android\app\src\main\AndroidManifest.xml:16:13-62
	android:name
		ADDED from C:\Users\<USER>\Desktop\Projet LONDO TECH\LA PASTA\la-pasta-mobile\android\app\src\main\AndroidManifest.xml:14:13-41
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\Desktop\Projet LONDO TECH\LA PASTA\la-pasta-mobile\android\app\src\main\AndroidManifest.xml:19:13-22:29
action#android.intent.action.MAIN
ADDED from C:\Users\<USER>\Desktop\Projet LONDO TECH\LA PASTA\la-pasta-mobile\android\app\src\main\AndroidManifest.xml:20:17-69
	android:name
		ADDED from C:\Users\<USER>\Desktop\Projet LONDO TECH\LA PASTA\la-pasta-mobile\android\app\src\main\AndroidManifest.xml:20:25-66
category#android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\Desktop\Projet LONDO TECH\LA PASTA\la-pasta-mobile\android\app\src\main\AndroidManifest.xml:21:17-77
	android:name
		ADDED from C:\Users\<USER>\Desktop\Projet LONDO TECH\LA PASTA\la-pasta-mobile\android\app\src\main\AndroidManifest.xml:21:27-74
uses-permission#android.permission.INTERNET
ADDED from C:\Users\<USER>\Desktop\Projet LONDO TECH\LA PASTA\la-pasta-mobile\android\app\src\main\AndroidManifest.xml:36:5-67
MERGED from [com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\06c45ee83264f0a53a1a2872f349892b\transformed\jetified-transport-backend-cct-2.3.3\AndroidManifest.xml:26:5-67
MERGED from [com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\06c45ee83264f0a53a1a2872f349892b\transformed\jetified-transport-backend-cct-2.3.3\AndroidManifest.xml:26:5-67
	android:name
		ADDED from C:\Users\<USER>\Desktop\Projet LONDO TECH\LA PASTA\la-pasta-mobile\android\app\src\main\AndroidManifest.xml:36:22-64
uses-permission#android.permission.CAMERA
ADDED from C:\Users\<USER>\Desktop\Projet LONDO TECH\LA PASTA\la-pasta-mobile\android\app\src\main\AndroidManifest.xml:37:5-65
	android:name
		ADDED from C:\Users\<USER>\Desktop\Projet LONDO TECH\LA PASTA\la-pasta-mobile\android\app\src\main\AndroidManifest.xml:37:22-62
uses-permission#android.permission.FLASHLIGHT
ADDED from C:\Users\<USER>\Desktop\Projet LONDO TECH\LA PASTA\la-pasta-mobile\android\app\src\main\AndroidManifest.xml:38:5-69
	android:name
		ADDED from C:\Users\<USER>\Desktop\Projet LONDO TECH\LA PASTA\la-pasta-mobile\android\app\src\main\AndroidManifest.xml:38:22-66
uses-permission#android.permission.ACCESS_COARSE_LOCATION
ADDED from C:\Users\<USER>\Desktop\Projet LONDO TECH\LA PASTA\la-pasta-mobile\android\app\src\main\AndroidManifest.xml:39:5-81
	android:name
		ADDED from C:\Users\<USER>\Desktop\Projet LONDO TECH\LA PASTA\la-pasta-mobile\android\app\src\main\AndroidManifest.xml:39:22-78
uses-permission#android.permission.ACCESS_FINE_LOCATION
ADDED from C:\Users\<USER>\Desktop\Projet LONDO TECH\LA PASTA\la-pasta-mobile\android\app\src\main\AndroidManifest.xml:40:5-79
	android:name
		ADDED from C:\Users\<USER>\Desktop\Projet LONDO TECH\LA PASTA\la-pasta-mobile\android\app\src\main\AndroidManifest.xml:40:22-76
uses-permission#android.permission.RECEIVE_SMS
ADDED from C:\Users\<USER>\Desktop\Projet LONDO TECH\LA PASTA\la-pasta-mobile\android\app\src\main\AndroidManifest.xml:43:5-70
	android:name
		ADDED from C:\Users\<USER>\Desktop\Projet LONDO TECH\LA PASTA\la-pasta-mobile\android\app\src\main\AndroidManifest.xml:43:22-67
uses-permission#android.permission.READ_SMS
ADDED from C:\Users\<USER>\Desktop\Projet LONDO TECH\LA PASTA\la-pasta-mobile\android\app\src\main\AndroidManifest.xml:44:5-67
	android:name
		ADDED from C:\Users\<USER>\Desktop\Projet LONDO TECH\LA PASTA\la-pasta-mobile\android\app\src\main\AndroidManifest.xml:44:22-64
uses-permission#com.google.android.gms.permission.AD_ID
ADDED from C:\Users\<USER>\Desktop\Projet LONDO TECH\LA PASTA\la-pasta-mobile\android\app\src\main\AndroidManifest.xml:45:5-79
	android:name
		ADDED from C:\Users\<USER>\Desktop\Projet LONDO TECH\LA PASTA\la-pasta-mobile\android\app\src\main\AndroidManifest.xml:45:22-76
meta-data#android.support.FILE_PROVIDER_PATHS
ADDED from C:\Users\<USER>\Desktop\Projet LONDO TECH\LA PASTA\la-pasta-mobile\android\app\src\main\AndroidManifest.xml:30:13-112
	android:resource
		ADDED from C:\Users\<USER>\Desktop\Projet LONDO TECH\LA PASTA\la-pasta-mobile\android\app\src\main\AndroidManifest.xml:30:75-109
	android:name
		ADDED from C:\Users\<USER>\Desktop\Projet LONDO TECH\LA PASTA\la-pasta-mobile\android\app\src\main\AndroidManifest.xml:30:24-74
uses-sdk
INJECTED from C:\Users\<USER>\Desktop\Projet LONDO TECH\LA PASTA\la-pasta-mobile\android\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from C:\Users\<USER>\Desktop\Projet LONDO TECH\LA PASTA\la-pasta-mobile\android\app\src\main\AndroidManifest.xml
INJECTED from C:\Users\<USER>\Desktop\Projet LONDO TECH\LA PASTA\la-pasta-mobile\android\app\src\main\AndroidManifest.xml
MERGED from [:capacitor-mlkit-barcode-scanning] C:\Users\<USER>\Desktop\Projet LONDO TECH\LA PASTA\la-pasta-mobile\node_modules\@capacitor-mlkit\barcode-scanning\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:capacitor-mlkit-barcode-scanning] C:\Users\<USER>\Desktop\Projet LONDO TECH\LA PASTA\la-pasta-mobile\node_modules\@capacitor-mlkit\barcode-scanning\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:capacitor-app] C:\Users\<USER>\Desktop\Projet LONDO TECH\LA PASTA\la-pasta-mobile\node_modules\@capacitor\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:capacitor-app] C:\Users\<USER>\Desktop\Projet LONDO TECH\LA PASTA\la-pasta-mobile\node_modules\@capacitor\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:capacitor-camera] C:\Users\<USER>\Desktop\Projet LONDO TECH\LA PASTA\la-pasta-mobile\node_modules\@capacitor\camera\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:capacitor-camera] C:\Users\<USER>\Desktop\Projet LONDO TECH\LA PASTA\la-pasta-mobile\node_modules\@capacitor\camera\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:capacitor-geolocation] C:\Users\<USER>\Desktop\Projet LONDO TECH\LA PASTA\la-pasta-mobile\node_modules\@capacitor\geolocation\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:capacitor-geolocation] C:\Users\<USER>\Desktop\Projet LONDO TECH\LA PASTA\la-pasta-mobile\node_modules\@capacitor\geolocation\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:capacitor-android] C:\Users\<USER>\Desktop\Projet LONDO TECH\LA PASTA\la-pasta-mobile\node_modules\@capacitor\android\capacitor\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:capacitor-android] C:\Users\<USER>\Desktop\Projet LONDO TECH\LA PASTA\la-pasta-mobile\node_modules\@capacitor\android\capacitor\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:capacitor-cordova-android-plugins] C:\Users\<USER>\Desktop\Projet LONDO TECH\LA PASTA\la-pasta-mobile\android\capacitor-cordova-android-plugins\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:6:5-44
MERGED from [:capacitor-cordova-android-plugins] C:\Users\<USER>\Desktop\Projet LONDO TECH\LA PASTA\la-pasta-mobile\android\capacitor-cordova-android-plugins\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:6:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c11b56a366e16dbf3cf5030eb16512bb\transformed\jetified-appcompat-resources-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c11b56a366e16dbf3cf5030eb16512bb\transformed\jetified-appcompat-resources-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-view:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8686ad6457215cf3e729f8f9398d03e3\transformed\jetified-camera-view-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.camera:camera-view:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8686ad6457215cf3e729f8f9398d03e3\transformed\jetified-camera-view-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\945fa3d50eeded1ef19658fca0a2b0ed\transformed\material-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\945fa3d50eeded1ef19658fca0a2b0ed\transformed\material-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.mlkit:barcode-scanning:17.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bbf4359ae6fe5117f4f4f2d0078eecf\transformed\jetified-barcode-scanning-17.3.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.mlkit:barcode-scanning:17.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bbf4359ae6fe5117f4f4f2d0078eecf\transformed\jetified-barcode-scanning-17.3.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-code-scanner:16.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\809000ee9483f32dd44add99df2c7cb4\transformed\jetified-play-services-code-scanner-16.1.0\AndroidManifest.xml:6:5-44
MERGED from [com.google.android.gms:play-services-code-scanner:16.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\809000ee9483f32dd44add99df2c7cb4\transformed\jetified-play-services-code-scanner-16.1.0\AndroidManifest.xml:6:5-44
MERGED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d55bd8fa6ecdc0a194a8fd32ba400784\transformed\jetified-play-services-mlkit-barcode-scanning-18.3.1\AndroidManifest.xml:6:5-44
MERGED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d55bd8fa6ecdc0a194a8fd32ba400784\transformed\jetified-play-services-mlkit-barcode-scanning-18.3.1\AndroidManifest.xml:6:5-44
MERGED from [com.google.mlkit:barcode-scanning-common:17.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8b92c10a4d64eeb41d7cd74f8bd1ee08\transformed\jetified-barcode-scanning-common-17.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.mlkit:barcode-scanning-common:17.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8b92c10a4d64eeb41d7cd74f8bd1ee08\transformed\jetified-barcode-scanning-common-17.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b063b32a14ce5f5b3194ab73cfc4ee14\transformed\jetified-vision-common-17.3.0\AndroidManifest.xml:6:5-44
MERGED from [com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b063b32a14ce5f5b3194ab73cfc4ee14\transformed\jetified-vision-common-17.3.0\AndroidManifest.xml:6:5-44
MERGED from [com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a5a91bd578d149a6c95bbcc3efd16a9d\transformed\jetified-common-18.11.0\AndroidManifest.xml:6:5-44
MERGED from [com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a5a91bd578d149a6c95bbcc3efd16a9d\transformed\jetified-common-18.11.0\AndroidManifest.xml:6:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8b36869effc731647c2277216923cf4f\transformed\constraintlayout-2.0.1\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8b36869effc731647c2277216923cf4f\transformed\constraintlayout-2.0.1\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2cf62e072a2b5b58d074505e6258e35d\transformed\appcompat-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2cf62e072a2b5b58d074505e6258e35d\transformed\appcompat-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20bfa38b8097c2e16311622c17bc8491\transformed\coordinatorlayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20bfa38b8097c2e16311622c17bc8491\transformed\coordinatorlayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core-splashscreen:1.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dff148f4042a0d21e144a502e46c29ee\transformed\jetified-core-splashscreen-1.0.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core-splashscreen:1.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dff148f4042a0d21e144a502e46c29ee\transformed\jetified-core-splashscreen-1.0.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.gms:play-services-auth:21.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c08f3e536f5af5cebd7b0f2a9c8eb2d1\transformed\jetified-play-services-auth-21.2.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-auth:21.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c08f3e536f5af5cebd7b0f2a9c8eb2d1\transformed\jetified-play-services-auth-21.2.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-auth-api-phone:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\63de66f36c31d59182f8f377404e8cff\transformed\jetified-play-services-auth-api-phone-18.1.0\AndroidManifest.xml:4:5-44
MERGED from [com.google.android.gms:play-services-auth-api-phone:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\63de66f36c31d59182f8f377404e8cff\transformed\jetified-play-services-auth-api-phone-18.1.0\AndroidManifest.xml:4:5-44
MERGED from [io.ionic.libs:iongeolocation-android:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a6576c2c17594dad09737e495f0c6b9\transformed\jetified-iongeolocation-android-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [io.ionic.libs:iongeolocation-android:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a6576c2c17594dad09737e495f0c6b9\transformed\jetified-iongeolocation-android-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-location:21.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dc07be8d86bc45e01fcf98f6fcfc1ed3\transformed\jetified-play-services-location-21.3.0\AndroidManifest.xml:4:5-44
MERGED from [com.google.android.gms:play-services-location:21.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dc07be8d86bc45e01fcf98f6fcfc1ed3\transformed\jetified-play-services-location-21.3.0\AndroidManifest.xml:4:5-44
MERGED from [com.google.android.gms:play-services-auth-base:18.0.10] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d723a3c58d6b2f81c91873185bc12831\transformed\jetified-play-services-auth-base-18.0.10\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-auth-base:18.0.10] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d723a3c58d6b2f81c91873185bc12831\transformed\jetified-play-services-auth-base-18.0.10\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-fido:20.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\be404c093f6b34d42cf7d1e7fcb12f45\transformed\jetified-play-services-fido-20.0.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-fido:20.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\be404c093f6b34d42cf7d1e7fcb12f45\transformed\jetified-play-services-fido-20.0.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\28141fbb4925ebb9bc097857c92d6d27\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\28141fbb4925ebb9bc097857c92d6d27\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.mlkit:vision-interfaces:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9a6dc87ddd5c10b8bc588980ddd5d441\transformed\jetified-vision-interfaces-16.3.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.mlkit:vision-interfaces:16.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9a6dc87ddd5c10b8bc588980ddd5d441\transformed\jetified-vision-interfaces-16.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ae9ca3e727414881ef14d800b97bc389\transformed\jetified-emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ae9ca3e727414881ef14d800b97bc389\transformed\jetified-emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\543aa4d8cde29bfa9a5da326fa5b8380\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\543aa4d8cde29bfa9a5da326fa5b8380\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.webkit:webkit:1.12.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e1c76e2fe55f3535ab45dbd228d4fb29\transformed\webkit-1.12.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.webkit:webkit:1.12.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e1c76e2fe55f3535ab45dbd228d4fb29\transformed\webkit-1.12.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-camera2:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8bcc53a3834ebaf77da2288f604a904c\transformed\jetified-camera-camera2-1.1.0\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.camera:camera-camera2:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8bcc53a3834ebaf77da2288f604a904c\transformed\jetified-camera-camera2-1.1.0\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.camera:camera-lifecycle:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8a4c964426c70d05775a8b2176efc5da\transformed\jetified-camera-lifecycle-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.camera:camera-lifecycle:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8a4c964426c70d05775a8b2176efc5da\transformed\jetified-camera-lifecycle-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.camera:camera-core:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\97eaf6809b81bfd23a7279edcdf74ac7\transformed\jetified-camera-core-1.1.0\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.camera:camera-core:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\97eaf6809b81bfd23a7279edcdf74ac7\transformed\jetified-camera-core-1.1.0\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7e9e800edbded6d5bbfc17ee9d64ede7\transformed\transition-1.5.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7e9e800edbded6d5bbfc17ee9d64ede7\transformed\transition-1.5.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e842251be9ddcb7de6cb939b5733fd97\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e842251be9ddcb7de6cb939b5733fd97\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e9a9bfa0516b4a036cc191a74b0b965a\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e9a9bfa0516b4a036cc191a74b0b965a\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dc3df7c8c37a8848dab522b0ab2c2e76\transformed\loader-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.loader:loader:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dc3df7c8c37a8848dab522b0ab2c2e76\transformed\loader-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2d69a97326794d3edb183c7e99d68234\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2d69a97326794d3edb183c7e99d68234\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6efb25ac1868e901121fb137de071f9a\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6efb25ac1868e901121fb137de071f9a\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.activity:activity-ktx:1.9.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c4c9bd491c09227ce5efaceaa54efabb\transformed\jetified-activity-ktx-1.9.3\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.9.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c4c9bd491c09227ce5efaceaa54efabb\transformed\jetified-activity-ktx-1.9.3\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3bebeae163ca76362a715c293a45aaf4\transformed\jetified-core-ktx-1.15.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3bebeae163ca76362a715c293a45aaf4\transformed\jetified-core-ktx-1.15.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\76615b94e9940687e89933d4afb081d1\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\76615b94e9940687e89933d4afb081d1\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0d3af2e6159c29b73ff064d48ce7c71d\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0d3af2e6159c29b73ff064d48ce7c71d\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2fd7b19c05ac6df2130c61f79ed9c75d\transformed\jetified-viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2fd7b19c05ac6df2130c61f79ed9c75d\transformed\jetified-viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\28e7b1792d1cdcff7bf27709b8f15dee\transformed\recyclerview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\28e7b1792d1cdcff7bf27709b8f15dee\transformed\recyclerview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cfbca8b2089c9692833971460f9f59ac\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cfbca8b2089c9692833971460f9f59ac\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e218b8c118997427953592d90c2fa2e8\transformed\core-1.15.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e218b8c118997427953592d90c2fa2e8\transformed\core-1.15.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c29d2f3f518fcf79a21a2e1a09538b7e\transformed\jetified-lifecycle-viewmodel-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c29d2f3f518fcf79a21a2e1a09538b7e\transformed\jetified-lifecycle-viewmodel-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f1c1167d9b54a36d7bb6615354af0e5\transformed\jetified-lifecycle-runtime-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f1c1167d9b54a36d7bb6615354af0e5\transformed\jetified-lifecycle-runtime-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\acfbc11e919c8c9a914cb45056e6bac8\transformed\lifecycle-viewmodel-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\acfbc11e919c8c9a914cb45056e6bac8\transformed\lifecycle-viewmodel-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\900e43aff56f75a168826022aabdc33d\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\900e43aff56f75a168826022aabdc33d\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\470b12e0a3da5e0343e1069480b44ea8\transformed\lifecycle-livedata-core-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\470b12e0a3da5e0343e1069480b44ea8\transformed\lifecycle-livedata-core-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0763fd4d23f1722ee96679c9117c7ae7\transformed\lifecycle-livedata-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0763fd4d23f1722ee96679c9117c7ae7\transformed\lifecycle-livedata-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3be6ade46fd06acc1d07bcab870d36b4\transformed\lifecycle-runtime-2.6.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3be6ade46fd06acc1d07bcab870d36b4\transformed\lifecycle-runtime-2.6.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b06e433ecdf349db09786d98d126aaed\transformed\jetified-lifecycle-viewmodel-savedstate-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b06e433ecdf349db09786d98d126aaed\transformed\jetified-lifecycle-viewmodel-savedstate-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4f75e71e070c07fea476289e65f78a37\transformed\jetified-savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4f75e71e070c07fea476289e65f78a37\transformed\jetified-savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73212289890cd8344d93bc39155522c6\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73212289890cd8344d93bc39155522c6\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4cd12d2b992842edee61ed3d342a9313\transformed\jetified-play-services-tasks-18.2.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4cd12d2b992842edee61ed3d342a9313\transformed\jetified-play-services-tasks-18.2.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ad29408dc46c7a7882a72a73e429c1d5\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ad29408dc46c7a7882a72a73e429c1d5\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:3:5-44
MERGED from [androidx.fragment:fragment:1.8.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\65d79c4a22703368e61b9ba3c5adc9ca\transformed\fragment-1.8.4\AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment:1.8.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\65d79c4a22703368e61b9ba3c5adc9ca\transformed\fragment-1.8.4\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity:1.9.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fb7bff88ae3c124681b3c5315e0965b\transformed\jetified-activity-1.9.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.9.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fb7bff88ae3c124681b3c5315e0965b\transformed\jetified-activity-1.9.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f2a226698dc38541b3e28cbe42764386\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f2a226698dc38541b3e28cbe42764386\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bb57c83c4e4395aacfd7e9ff476c67e8\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bb57c83c4e4395aacfd7e9ff476c67e8\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.exifinterface:exifinterface:1.3.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c556d96b83bca5df3ca71d2e14bc0fbc\transformed\exifinterface-1.3.7\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.exifinterface:exifinterface:1.3.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c556d96b83bca5df3ca71d2e14bc0fbc\transformed\exifinterface-1.3.7\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5f5fb8ab546355d878e03be984ccdc65\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5f5fb8ab546355d878e03be984ccdc65\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f1f73d6c7ff79b14cda1437c4b5ee86a\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f1f73d6c7ff79b14cda1437c4b5ee86a\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4a3011e93b65bf797eb779714a515e36\transformed\jetified-tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4a3011e93b65bf797eb779714a515e36\transformed\jetified-tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7bdf44d8c457a9d550de94e7018fb51f\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7bdf44d8c457a9d550de94e7018fb51f\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\06c45ee83264f0a53a1a2872f349892b\transformed\jetified-transport-backend-cct-2.3.3\AndroidManifest.xml:19:5-21:41
MERGED from [com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\06c45ee83264f0a53a1a2872f349892b\transformed\jetified-transport-backend-cct-2.3.3\AndroidManifest.xml:19:5-21:41
MERGED from [com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a48f688fa386ce7aed0a486b8b5ba154\transformed\jetified-transport-runtime-2.2.6\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a48f688fa386ce7aed0a486b8b5ba154\transformed\jetified-transport-runtime-2.2.6\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-api:2.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6bb886c7ab3b92899a469b0fbc07cb2c\transformed\jetified-transport-api-2.2.1\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-api:2.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6bb886c7ab3b92899a469b0fbc07cb2c\transformed\jetified-transport-api-2.2.1\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.firebase:firebase-components:16.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\614ae7eb9ba7b31fc1366be97ea3cb53\transformed\jetified-firebase-components-16.1.0\AndroidManifest.xml:19:5-21:41
MERGED from [com.google.firebase:firebase-components:16.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\614ae7eb9ba7b31fc1366be97ea3cb53\transformed\jetified-firebase-components-16.1.0\AndroidManifest.xml:19:5-21:41
MERGED from [com.google.firebase:firebase-encoders-json:17.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d8a7deec6c863e15ef763c401c22e79\transformed\jetified-firebase-encoders-json-17.1.0\AndroidManifest.xml:19:5-21:41
MERGED from [com.google.firebase:firebase-encoders-json:17.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d8a7deec6c863e15ef763c401c22e79\transformed\jetified-firebase-encoders-json-17.1.0\AndroidManifest.xml:19:5-21:41
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f1a3628cb91fdb1aa9b9c2531cff8fcb\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f1a3628cb91fdb1aa9b9c2531cff8fcb\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2e0f289aa4483338394bb5c319030078\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2e0f289aa4483338394bb5c319030078\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9200b6dfda72546e7e7f226dc49882f0\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9200b6dfda72546e7e7f226dc49882f0\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d29630f4f10e19a8e91088cb9f659866\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d29630f4f10e19a8e91088cb9f659866\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\61a7139421f24e376021e74fa4cbf528\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\61a7139421f24e376021e74fa4cbf528\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d9498bfef8c45ff127d7b7c473b3abab\transformed\jetified-annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d9498bfef8c45ff127d7b7c473b3abab\transformed\jetified-annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [org.apache.cordova:framework:10.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9784ddf63c6cadb3e3bddfa95d23b3fc\transformed\jetified-framework-10.1.1\AndroidManifest.xml:25:5-44
MERGED from [org.apache.cordova:framework:10.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9784ddf63c6cadb3e3bddfa95d23b3fc\transformed\jetified-framework-10.1.1\AndroidManifest.xml:25:5-44
MERGED from [com.google.android.odml:image:1.0.0-beta1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\60e6d164477c28ec9c9ea378fe6a87dd\transformed\jetified-image-1.0.0-beta1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.odml:image:1.0.0-beta1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\60e6d164477c28ec9c9ea378fe6a87dd\transformed\jetified-image-1.0.0-beta1\AndroidManifest.xml:5:5-44
	android:targetSdkVersion
		INJECTED from C:\Users\<USER>\Desktop\Projet LONDO TECH\LA PASTA\la-pasta-mobile\android\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from C:\Users\<USER>\Desktop\Projet LONDO TECH\LA PASTA\la-pasta-mobile\android\app\src\main\AndroidManifest.xml
queries
ADDED from [:capacitor-camera] C:\Users\<USER>\Desktop\Projet LONDO TECH\LA PASTA\la-pasta-mobile\node_modules\@capacitor\camera\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-11:15
intent#action:name:android.media.action.IMAGE_CAPTURE
ADDED from [:capacitor-camera] C:\Users\<USER>\Desktop\Projet LONDO TECH\LA PASTA\la-pasta-mobile\node_modules\@capacitor\camera\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-10:18
action#android.media.action.IMAGE_CAPTURE
ADDED from [:capacitor-camera] C:\Users\<USER>\Desktop\Projet LONDO TECH\LA PASTA\la-pasta-mobile\node_modules\@capacitor\camera\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-73
	android:name
		ADDED from [:capacitor-camera] C:\Users\<USER>\Desktop\Projet LONDO TECH\LA PASTA\la-pasta-mobile\node_modules\@capacitor\camera\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:21-70
uses-feature#android.hardware.telephony
ADDED from [:capacitor-cordova-android-plugins] C:\Users\<USER>\Desktop\Projet LONDO TECH\LA PASTA\la-pasta-mobile\android\capacitor-cordova-android-plugins\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-10:36
	android:required
		ADDED from [:capacitor-cordova-android-plugins] C:\Users\<USER>\Desktop\Projet LONDO TECH\LA PASTA\la-pasta-mobile\android\capacitor-cordova-android-plugins\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:9-33
	android:name
		ADDED from [:capacitor-cordova-android-plugins] C:\Users\<USER>\Desktop\Projet LONDO TECH\LA PASTA\la-pasta-mobile\android\capacitor-cordova-android-plugins\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:9-50
receiver#com.andreszs.smsretriever.SMSBroadcastReceiver
ADDED from [:capacitor-cordova-android-plugins] C:\Users\<USER>\Desktop\Projet LONDO TECH\LA PASTA\la-pasta-mobile\android\capacitor-cordova-android-plugins\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:9-20:20
	android:exported
		ADDED from [:capacitor-cordova-android-plugins] C:\Users\<USER>\Desktop\Projet LONDO TECH\LA PASTA\la-pasta-mobile\android\capacitor-cordova-android-plugins\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:13-36
	android:permission
		ADDED from [:capacitor-cordova-android-plugins] C:\Users\<USER>\Desktop\Projet LONDO TECH\LA PASTA\la-pasta-mobile\android\capacitor-cordova-android-plugins\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:13-87
	android:name
		ADDED from [:capacitor-cordova-android-plugins] C:\Users\<USER>\Desktop\Projet LONDO TECH\LA PASTA\la-pasta-mobile\android\capacitor-cordova-android-plugins\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-74
intent-filter#action:name:com.google.android.gms.auth.api.phone.SMS_RETRIEVED
ADDED from [:capacitor-cordova-android-plugins] C:\Users\<USER>\Desktop\Projet LONDO TECH\LA PASTA\la-pasta-mobile\android\capacitor-cordova-android-plugins\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:17:13-19:29
action#com.google.android.gms.auth.api.phone.SMS_RETRIEVED
ADDED from [:capacitor-cordova-android-plugins] C:\Users\<USER>\Desktop\Projet LONDO TECH\LA PASTA\la-pasta-mobile\android\capacitor-cordova-android-plugins\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:18:17-94
	android:name
		ADDED from [:capacitor-cordova-android-plugins] C:\Users\<USER>\Desktop\Projet LONDO TECH\LA PASTA\la-pasta-mobile\android\capacitor-cordova-android-plugins\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:18:25-91
activity#com.google.mlkit.vision.codescanner.internal.GmsBarcodeScanningDelegateActivity
ADDED from [com.google.android.gms:play-services-code-scanner:16.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\809000ee9483f32dd44add99df2c7cb4\transformed\jetified-play-services-code-scanner-16.1.0\AndroidManifest.xml:15:9-20:20
	android:screenOrientation
		ADDED from [com.google.android.gms:play-services-code-scanner:16.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\809000ee9483f32dd44add99df2c7cb4\transformed\jetified-play-services-code-scanner-16.1.0\AndroidManifest.xml:18:13-49
	android:exported
		ADDED from [com.google.android.gms:play-services-code-scanner:16.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\809000ee9483f32dd44add99df2c7cb4\transformed\jetified-play-services-code-scanner-16.1.0\AndroidManifest.xml:17:13-37
	tools:ignore
		ADDED from [com.google.android.gms:play-services-code-scanner:16.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\809000ee9483f32dd44add99df2c7cb4\transformed\jetified-play-services-code-scanner-16.1.0\AndroidManifest.xml:19:13-42
	android:name
		ADDED from [com.google.android.gms:play-services-code-scanner:16.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\809000ee9483f32dd44add99df2c7cb4\transformed\jetified-play-services-code-scanner-16.1.0\AndroidManifest.xml:16:13-107
service#com.google.mlkit.common.internal.MlKitComponentDiscoveryService
ADDED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d55bd8fa6ecdc0a194a8fd32ba400784\transformed\jetified-play-services-mlkit-barcode-scanning-18.3.1\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b063b32a14ce5f5b3194ab73cfc4ee14\transformed\jetified-vision-common-17.3.0\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b063b32a14ce5f5b3194ab73cfc4ee14\transformed\jetified-vision-common-17.3.0\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a5a91bd578d149a6c95bbcc3efd16a9d\transformed\jetified-common-18.11.0\AndroidManifest.xml:15:9-23:19
MERGED from [com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a5a91bd578d149a6c95bbcc3efd16a9d\transformed\jetified-common-18.11.0\AndroidManifest.xml:15:9-23:19
	android:exported
		ADDED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d55bd8fa6ecdc0a194a8fd32ba400784\transformed\jetified-play-services-mlkit-barcode-scanning-18.3.1\AndroidManifest.xml:11:13-37
	tools:targetApi
		ADDED from [com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a5a91bd578d149a6c95bbcc3efd16a9d\transformed\jetified-common-18.11.0\AndroidManifest.xml:19:13-32
	android:directBootAware
		ADDED from [com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a5a91bd578d149a6c95bbcc3efd16a9d\transformed\jetified-common-18.11.0\AndroidManifest.xml:17:13-43
	android:name
		ADDED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d55bd8fa6ecdc0a194a8fd32ba400784\transformed\jetified-play-services-mlkit-barcode-scanning-18.3.1\AndroidManifest.xml:10:13-91
meta-data#com.google.firebase.components:com.google.mlkit.vision.barcode.internal.BarcodeRegistrar
ADDED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d55bd8fa6ecdc0a194a8fd32ba400784\transformed\jetified-play-services-mlkit-barcode-scanning-18.3.1\AndroidManifest.xml:12:13-14:85
	android:value
		ADDED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d55bd8fa6ecdc0a194a8fd32ba400784\transformed\jetified-play-services-mlkit-barcode-scanning-18.3.1\AndroidManifest.xml:14:17-82
	android:name
		ADDED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d55bd8fa6ecdc0a194a8fd32ba400784\transformed\jetified-play-services-mlkit-barcode-scanning-18.3.1\AndroidManifest.xml:13:17-120
meta-data#com.google.firebase.components:com.google.mlkit.vision.common.internal.VisionCommonRegistrar
ADDED from [com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b063b32a14ce5f5b3194ab73cfc4ee14\transformed\jetified-vision-common-17.3.0\AndroidManifest.xml:12:13-14:85
	android:value
		ADDED from [com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b063b32a14ce5f5b3194ab73cfc4ee14\transformed\jetified-vision-common-17.3.0\AndroidManifest.xml:14:17-82
	android:name
		ADDED from [com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b063b32a14ce5f5b3194ab73cfc4ee14\transformed\jetified-vision-common-17.3.0\AndroidManifest.xml:13:17-124
provider#com.google.mlkit.common.internal.MlKitInitProvider
ADDED from [com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a5a91bd578d149a6c95bbcc3efd16a9d\transformed\jetified-common-18.11.0\AndroidManifest.xml:9:9-13:38
	android:authorities
		ADDED from [com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a5a91bd578d149a6c95bbcc3efd16a9d\transformed\jetified-common-18.11.0\AndroidManifest.xml:11:13-69
	android:exported
		ADDED from [com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a5a91bd578d149a6c95bbcc3efd16a9d\transformed\jetified-common-18.11.0\AndroidManifest.xml:12:13-37
	android:initOrder
		ADDED from [com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a5a91bd578d149a6c95bbcc3efd16a9d\transformed\jetified-common-18.11.0\AndroidManifest.xml:13:13-35
	android:name
		ADDED from [com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a5a91bd578d149a6c95bbcc3efd16a9d\transformed\jetified-common-18.11.0\AndroidManifest.xml:10:13-78
meta-data#com.google.firebase.components:com.google.mlkit.common.internal.CommonComponentRegistrar
ADDED from [com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a5a91bd578d149a6c95bbcc3efd16a9d\transformed\jetified-common-18.11.0\AndroidManifest.xml:20:13-22:85
	android:value
		ADDED from [com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a5a91bd578d149a6c95bbcc3efd16a9d\transformed\jetified-common-18.11.0\AndroidManifest.xml:22:17-82
	android:name
		ADDED from [com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a5a91bd578d149a6c95bbcc3efd16a9d\transformed\jetified-common-18.11.0\AndroidManifest.xml:21:17-120
activity#com.google.android.gms.auth.api.signin.internal.SignInHubActivity
ADDED from [com.google.android.gms:play-services-auth:21.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c08f3e536f5af5cebd7b0f2a9c8eb2d1\transformed\jetified-play-services-auth-21.2.0\AndroidManifest.xml:23:9-27:75
	android:excludeFromRecents
		ADDED from [com.google.android.gms:play-services-auth:21.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c08f3e536f5af5cebd7b0f2a9c8eb2d1\transformed\jetified-play-services-auth-21.2.0\AndroidManifest.xml:25:13-46
	android:exported
		ADDED from [com.google.android.gms:play-services-auth:21.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c08f3e536f5af5cebd7b0f2a9c8eb2d1\transformed\jetified-play-services-auth-21.2.0\AndroidManifest.xml:26:13-37
	android:theme
		ADDED from [com.google.android.gms:play-services-auth:21.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c08f3e536f5af5cebd7b0f2a9c8eb2d1\transformed\jetified-play-services-auth-21.2.0\AndroidManifest.xml:27:13-72
	android:name
		ADDED from [com.google.android.gms:play-services-auth:21.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c08f3e536f5af5cebd7b0f2a9c8eb2d1\transformed\jetified-play-services-auth-21.2.0\AndroidManifest.xml:24:13-93
service#com.google.android.gms.auth.api.signin.RevocationBoundService
ADDED from [com.google.android.gms:play-services-auth:21.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c08f3e536f5af5cebd7b0f2a9c8eb2d1\transformed\jetified-play-services-auth-21.2.0\AndroidManifest.xml:33:9-37:51
	android:exported
		ADDED from [com.google.android.gms:play-services-auth:21.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c08f3e536f5af5cebd7b0f2a9c8eb2d1\transformed\jetified-play-services-auth-21.2.0\AndroidManifest.xml:35:13-36
	android:visibleToInstantApps
		ADDED from [com.google.android.gms:play-services-auth:21.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c08f3e536f5af5cebd7b0f2a9c8eb2d1\transformed\jetified-play-services-auth-21.2.0\AndroidManifest.xml:37:13-48
	android:permission
		ADDED from [com.google.android.gms:play-services-auth:21.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c08f3e536f5af5cebd7b0f2a9c8eb2d1\transformed\jetified-play-services-auth-21.2.0\AndroidManifest.xml:36:13-107
	android:name
		ADDED from [com.google.android.gms:play-services-auth:21.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c08f3e536f5af5cebd7b0f2a9c8eb2d1\transformed\jetified-play-services-auth-21.2.0\AndroidManifest.xml:34:13-89
activity#com.google.android.gms.common.api.GoogleApiActivity
ADDED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\28141fbb4925ebb9bc097857c92d6d27\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:9-173
	android:exported
		ADDED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\28141fbb4925ebb9bc097857c92d6d27\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:146-170
	android:theme
		ADDED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\28141fbb4925ebb9bc097857c92d6d27\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:86-145
	android:name
		ADDED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\28141fbb4925ebb9bc097857c92d6d27\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:19-85
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\543aa4d8cde29bfa9a5da326fa5b8380\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\900e43aff56f75a168826022aabdc33d\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\900e43aff56f75a168826022aabdc33d\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bb57c83c4e4395aacfd7e9ff476c67e8\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bb57c83c4e4395aacfd7e9ff476c67e8\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5f5fb8ab546355d878e03be984ccdc65\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5f5fb8ab546355d878e03be984ccdc65\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\543aa4d8cde29bfa9a5da326fa5b8380\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\543aa4d8cde29bfa9a5da326fa5b8380\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\543aa4d8cde29bfa9a5da326fa5b8380\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\543aa4d8cde29bfa9a5da326fa5b8380\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\543aa4d8cde29bfa9a5da326fa5b8380\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\543aa4d8cde29bfa9a5da326fa5b8380\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\543aa4d8cde29bfa9a5da326fa5b8380\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:30:17-75
service#androidx.camera.core.impl.MetadataHolderService
ADDED from [androidx.camera:camera-camera2:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8bcc53a3834ebaf77da2288f604a904c\transformed\jetified-camera-camera2-1.1.0\AndroidManifest.xml:26:9-35:19
MERGED from [androidx.camera:camera-core:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\97eaf6809b81bfd23a7279edcdf74ac7\transformed\jetified-camera-core-1.1.0\AndroidManifest.xml:31:9-35:78
MERGED from [androidx.camera:camera-core:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\97eaf6809b81bfd23a7279edcdf74ac7\transformed\jetified-camera-core-1.1.0\AndroidManifest.xml:31:9-35:78
	tools:node
		ADDED from [androidx.camera:camera-camera2:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8bcc53a3834ebaf77da2288f604a904c\transformed\jetified-camera-camera2-1.1.0\AndroidManifest.xml:31:13-31
	android:enabled
		ADDED from [androidx.camera:camera-camera2:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8bcc53a3834ebaf77da2288f604a904c\transformed\jetified-camera-camera2-1.1.0\AndroidManifest.xml:28:13-36
	android:exported
		ADDED from [androidx.camera:camera-camera2:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8bcc53a3834ebaf77da2288f604a904c\transformed\jetified-camera-camera2-1.1.0\AndroidManifest.xml:29:13-37
	tools:ignore
		ADDED from [androidx.camera:camera-camera2:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8bcc53a3834ebaf77da2288f604a904c\transformed\jetified-camera-camera2-1.1.0\AndroidManifest.xml:30:13-75
	android:name
		ADDED from [androidx.camera:camera-camera2:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8bcc53a3834ebaf77da2288f604a904c\transformed\jetified-camera-camera2-1.1.0\AndroidManifest.xml:27:13-75
meta-data#androidx.camera.core.impl.MetadataHolderService.DEFAULT_CONFIG_PROVIDER
ADDED from [androidx.camera:camera-camera2:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8bcc53a3834ebaf77da2288f604a904c\transformed\jetified-camera-camera2-1.1.0\AndroidManifest.xml:32:13-34:89
	android:value
		ADDED from [androidx.camera:camera-camera2:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8bcc53a3834ebaf77da2288f604a904c\transformed\jetified-camera-camera2-1.1.0\AndroidManifest.xml:34:17-86
	android:name
		ADDED from [androidx.camera:camera-camera2:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8bcc53a3834ebaf77da2288f604a904c\transformed\jetified-camera-camera2-1.1.0\AndroidManifest.xml:33:17-103
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e218b8c118997427953592d90c2fa2e8\transformed\core-1.15.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e218b8c118997427953592d90c2fa2e8\transformed\core-1.15.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e218b8c118997427953592d90c2fa2e8\transformed\core-1.15.0\AndroidManifest.xml:23:9-81
permission#com.clickcadyst.mobile.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e218b8c118997427953592d90c2fa2e8\transformed\core-1.15.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e218b8c118997427953592d90c2fa2e8\transformed\core-1.15.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e218b8c118997427953592d90c2fa2e8\transformed\core-1.15.0\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e218b8c118997427953592d90c2fa2e8\transformed\core-1.15.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e218b8c118997427953592d90c2fa2e8\transformed\core-1.15.0\AndroidManifest.xml:26:22-94
uses-permission#com.clickcadyst.mobile.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e218b8c118997427953592d90c2fa2e8\transformed\core-1.15.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e218b8c118997427953592d90c2fa2e8\transformed\core-1.15.0\AndroidManifest.xml:26:22-94
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\900e43aff56f75a168826022aabdc33d\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\900e43aff56f75a168826022aabdc33d\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\900e43aff56f75a168826022aabdc33d\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
meta-data#com.google.android.gms.version
ADDED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ad29408dc46c7a7882a72a73e429c1d5\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:9-122
	android:value
		ADDED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ad29408dc46c7a7882a72a73e429c1d5\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:66-119
	android:name
		ADDED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ad29408dc46c7a7882a72a73e429c1d5\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:20-65
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bb57c83c4e4395aacfd7e9ff476c67e8\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bb57c83c4e4395aacfd7e9ff476c67e8\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bb57c83c4e4395aacfd7e9ff476c67e8\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bb57c83c4e4395aacfd7e9ff476c67e8\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bb57c83c4e4395aacfd7e9ff476c67e8\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bb57c83c4e4395aacfd7e9ff476c67e8\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bb57c83c4e4395aacfd7e9ff476c67e8\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bb57c83c4e4395aacfd7e9ff476c67e8\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bb57c83c4e4395aacfd7e9ff476c67e8\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bb57c83c4e4395aacfd7e9ff476c67e8\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bb57c83c4e4395aacfd7e9ff476c67e8\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bb57c83c4e4395aacfd7e9ff476c67e8\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bb57c83c4e4395aacfd7e9ff476c67e8\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bb57c83c4e4395aacfd7e9ff476c67e8\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bb57c83c4e4395aacfd7e9ff476c67e8\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bb57c83c4e4395aacfd7e9ff476c67e8\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bb57c83c4e4395aacfd7e9ff476c67e8\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bb57c83c4e4395aacfd7e9ff476c67e8\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bb57c83c4e4395aacfd7e9ff476c67e8\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bb57c83c4e4395aacfd7e9ff476c67e8\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bb57c83c4e4395aacfd7e9ff476c67e8\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from [com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\06c45ee83264f0a53a1a2872f349892b\transformed\jetified-transport-backend-cct-2.3.3\AndroidManifest.xml:25:5-79
MERGED from [com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a48f688fa386ce7aed0a486b8b5ba154\transformed\jetified-transport-runtime-2.2.6\AndroidManifest.xml:22:5-79
MERGED from [com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a48f688fa386ce7aed0a486b8b5ba154\transformed\jetified-transport-runtime-2.2.6\AndroidManifest.xml:22:5-79
	android:name
		ADDED from [com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\06c45ee83264f0a53a1a2872f349892b\transformed\jetified-transport-backend-cct-2.3.3\AndroidManifest.xml:25:22-76
service#com.google.android.datatransport.runtime.backends.TransportBackendDiscovery
ADDED from [com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\06c45ee83264f0a53a1a2872f349892b\transformed\jetified-transport-backend-cct-2.3.3\AndroidManifest.xml:29:9-35:19
MERGED from [com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a48f688fa386ce7aed0a486b8b5ba154\transformed\jetified-transport-runtime-2.2.6\AndroidManifest.xml:36:9-38:40
MERGED from [com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a48f688fa386ce7aed0a486b8b5ba154\transformed\jetified-transport-runtime-2.2.6\AndroidManifest.xml:36:9-38:40
	android:exported
		ADDED from [com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\06c45ee83264f0a53a1a2872f349892b\transformed\jetified-transport-backend-cct-2.3.3\AndroidManifest.xml:31:13-37
	android:name
		ADDED from [com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\06c45ee83264f0a53a1a2872f349892b\transformed\jetified-transport-backend-cct-2.3.3\AndroidManifest.xml:30:13-103
meta-data#backend:com.google.android.datatransport.cct.CctBackendFactory
ADDED from [com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\06c45ee83264f0a53a1a2872f349892b\transformed\jetified-transport-backend-cct-2.3.3\AndroidManifest.xml:32:13-34:39
	android:value
		ADDED from [com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\06c45ee83264f0a53a1a2872f349892b\transformed\jetified-transport-backend-cct-2.3.3\AndroidManifest.xml:34:17-36
	android:name
		ADDED from [com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\06c45ee83264f0a53a1a2872f349892b\transformed\jetified-transport-backend-cct-2.3.3\AndroidManifest.xml:33:17-94
service#com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService
ADDED from [com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a48f688fa386ce7aed0a486b8b5ba154\transformed\jetified-transport-runtime-2.2.6\AndroidManifest.xml:26:9-30:19
	android:exported
		ADDED from [com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a48f688fa386ce7aed0a486b8b5ba154\transformed\jetified-transport-runtime-2.2.6\AndroidManifest.xml:28:13-37
	android:permission
		ADDED from [com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a48f688fa386ce7aed0a486b8b5ba154\transformed\jetified-transport-runtime-2.2.6\AndroidManifest.xml:29:13-69
	android:name
		ADDED from [com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a48f688fa386ce7aed0a486b8b5ba154\transformed\jetified-transport-runtime-2.2.6\AndroidManifest.xml:27:13-117
receiver#com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver
ADDED from [com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a48f688fa386ce7aed0a486b8b5ba154\transformed\jetified-transport-runtime-2.2.6\AndroidManifest.xml:32:9-34:40
	android:exported
		ADDED from [com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a48f688fa386ce7aed0a486b8b5ba154\transformed\jetified-transport-runtime-2.2.6\AndroidManifest.xml:34:13-37
	android:name
		ADDED from [com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a48f688fa386ce7aed0a486b8b5ba154\transformed\jetified-transport-runtime-2.2.6\AndroidManifest.xml:33:13-132
