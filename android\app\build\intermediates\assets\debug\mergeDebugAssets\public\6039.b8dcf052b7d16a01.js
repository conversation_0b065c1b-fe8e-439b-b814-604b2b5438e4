"use strict";(self.webpackChunkapp=self.webpackChunkapp||[]).push([[6039],{76039:(k,f,c)=>{c.r(f),c.d(f,{FidelityProgramPageModule:()=>X});var p=c(56610),g=c(37222),r=c(77897),M=c(77575),u=c(73308),n=c(2978),w=c(26409),O=c(13217),y=c(82571),v=c(74657),x=c(14599),R=c(66866);function h(e,m){1&e&&n.nrm(0,"ion-spinner",16)}let I=(()=>{class e{constructor(t,o,i,d,P){this.modalCtrl=t,this.fidelityProgramService=o,this.commonService=i,this.translateService=d,this.storageSrv=P}ngOnInit(){this.phoneForm=new g.gE({phoneNumber:new g.MJ("",[g.k0.required,g.k0.minLength(12)])})}sendReferral(){var t=this;return(0,u.A)(function*(){let o=t.phoneForm.get("phoneNumber").value;if(o=o.replace(/\s+/g,""),!/^\d{9}$/.test(o))return void(yield t.commonService.showToast({message:"fr"===t.translateService.currentLang?"Veuillez saisir un num\xe9ro de t\xe9l\xe9phone valide (9 chiffres)":"Please enter a valid phone number (9 digits)",color:"danger"}));const i=t.storageSrv.getUserConnected();if(!i||!i._id)throw new Error("Utilisateur non connect\xe9");const d=yield t.fidelityProgramService.sendReferralInvitation(i._id,+o);return!d||d instanceof w.yz?t.isLoading=!1:(yield t.commonService.showToast({message:"fr"===t.translateService.currentLang?"Invitation de parrainage envoy\xe9e avec succ\xe8s":"Referral invitation sent successfully",color:"success"}),t.isLoading=!1,t.closeModal())})()}closeModal(){this.modalCtrl.dismiss()}static{this.\u0275fac=function(o){return new(o||e)(n.rXU(r.W3),n.rXU(O._),n.rXU(y.h),n.rXU(v.c$),n.rXU(x.n))}}static{this.\u0275cmp=n.VBU({type:e,selectors:[["app-send-sponsor"]],decls:28,vars:15,consts:[[1,"ion-text-center","ion-padding"],["slot","end",3,"click"],["src","assets/icons/close-btn.svg",3,"click"],["color","primary",1,"title"],[1,"ion-padding"],[1,"content-wrapper",3,"formGroup"],[1,"instruction-text"],[1,"input-group"],[1,"country-code"],["src","../../../../assets/icons/image 4.svg",1,"flag"],["src","../../../../assets/icons/Layer 2.png"],[1,"number-input"],["type","tel","formControlName","phoneNumber","appPhoneFormat","","placeholder","655 84 75 95"],["name","bubbles",4,"ngIf"],["expand","block",3,"disabled","click"],[1,"see-more"],["name","bubbles"]],template:function(o,i){1&o&&(n.j41(0,"ion-header")(1,"ion-toolbar",0)(2,"ion-thumbnail",1),n.bIt("click",function(){return i.closeModal()}),n.j41(3,"ion-img",2),n.bIt("click",function(){return i.closeModal()}),n.k0s()(),n.j41(4,"ion-title",3),n.EFF(5),n.nI1(6,"translate"),n.k0s()()(),n.j41(7,"ion-content",4)(8,"div",5)(9,"p",6),n.EFF(10),n.nI1(11,"translate"),n.k0s(),n.j41(12,"div",7)(13,"ion-item")(14,"div",8),n.nrm(15,"ion-img",9),n.j41(16,"span"),n.EFF(17,"237"),n.k0s(),n.nrm(18,"ion-img",10),n.k0s()(),n.j41(19,"ion-item",11),n.nrm(20,"ion-input",12),n.k0s()(),n.DNE(21,h,1,0,"ion-spinner",13),n.j41(22,"ion-button",14),n.bIt("click",function(){return i.sendReferral()}),n.EFF(23),n.nI1(24,"translate"),n.k0s(),n.j41(25,"span",15),n.EFF(26),n.nI1(27,"translate"),n.k0s()()()),2&o&&(n.R7$(5),n.SpI(" ",n.bMT(6,7,"bottom-sheet.other.add-referral")," "),n.R7$(3),n.Y8G("formGroup",i.phoneForm),n.R7$(2),n.JRh(n.bMT(11,9,"fidelity-page.input-referral")),n.R7$(11),n.Y8G("ngIf",i.isLoading),n.R7$(1),n.Y8G("disabled",!i.phoneForm.valid),n.R7$(1),n.SpI(" ",n.bMT(24,11,"fidelity-page.referal")," "),n.R7$(3),n.JRh(n.bMT(27,13,"fidelity-page.see-more")))},dependencies:[g.BC,g.cb,r.Jm,r.W9,r.eU,r.KW,r.$w,r.uz,r.w2,r.Zx,r.BC,r.ai,r.Gw,p.bT,g.j4,g.JD,R.v,v.D9],styles:["*[_ngcontent-%COMP%]{margin:0;padding:0}.header-md[_ngcontent-%COMP%]:after{background-image:none!important}.modal[_ngcontent-%COMP%]{--border-radius: calc(60 * var(--res))}.input-group[_ngcontent-%COMP%]{margin-top:calc(75 * var(--res))}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{--padding-start: 0;margin-bottom:calc(50 * var(--res));--placeholder-color: $color-six}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-input[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{font-family:Mont Regular;font-weight:400;font-size:calc(42 * var(--res));color:#1e1e1e}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{padding-top:calc(25 * var(--res));padding-bottom:calc(25 * var(--res))}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]{margin-bottom:calc(50 * var(--res));display:flex;align-items:center;justify-content:space-between;border-bottom-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-250, #c8c7cc)));border-bottom-width:.55px;border-bottom-style:solid}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{width:90%;margin-bottom:0;--border-color: transparent;--inner-border-width: 0px 0px 0px 0px;--border-style: none}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-checkbox[_ngcontent-%COMP%]{margin-top:5%}.bg-primary[_ngcontent-%COMP%]{color:#143c5d}.message[_ngcontent-%COMP%]{margin-bottom:calc(75 * var(--res))}.message[_ngcontent-%COMP%]   ion-title[_ngcontent-%COMP%]{font-weight:800;font-size:calc(70 * var(--res));padding-bottom:calc(25 * var(--res))}.message[_ngcontent-%COMP%]   ion-text[_ngcontent-%COMP%]{font-weight:400;color:#757474;display:block;line-height:1.5em!important}.btn--meduim[_ngcontent-%COMP%]{height:calc(130 * var(--res));font-size:calc(50 * var(--res));font-family:var(--mont-bold)}.btn--meduim[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:var(--mont-bold)}.btn--small[_ngcontent-%COMP%]{height:calc(100 * var(--res))}.btn--round[_ngcontent-%COMP%]{--border-radius: 50%;padding-inline-start:0;padding-inline-end:0;--padding-start: 0;--padding-end: 0}.btn--border-primary[_ngcontent-%COMP%]{--background: transparent;--background-activated: #c4e6d0;--border-color: var(--ion-color-primary);--border-style: solid;--border-width: 2px}.btn--border-secondary[_ngcontent-%COMP%]{--border-width: 2px;--background: transparent;--background-activated: #ddabb2;--border-color: var(--ion-color-secondary);--border-style: solid}.btn--upper[_ngcontent-%COMP%]{text-transform:uppercase}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]{overflow:hidden}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar{width:5px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-track{box-shadow:inset 0 0 5px #546e8d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background:#143c5d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover{background:#143c5d}.bottom-sheet-content[_ngcontent-%COMP%]{background:#fdfdfe;height:100%}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-thumbnail[_ngcontent-%COMP%]{margin-left:calc(31.25 * var(--res));background-color:#1e1e1e;--border-radius: 50%;--size: 23}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:Mont Bold;font-size:calc(45 * var(--res));text-align:center;color:#757474}.bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%], .bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]{padding:calc(75 * var(--res))}.font-size-5[_ngcontent-%COMP%]{font-size:calc(42 * var(--res))}ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]{--background: rgba(187, 215, 243, .61);--color: $color-primary;--border-width: 0;--padding-bottom: 0;padding:0}ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-title[_ngcontent-%COMP%]{font-family:Mont Bold;color:#143c5d;font-size:calc(40 * var(--res))}ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-thumbnail[_ngcontent-%COMP%]{z-index:10;position:absolute;right:calc(41 * var(--res));height:max-content;width:max-content}ion-content[_ngcontent-%COMP%]   .content-wrapper[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:1rem}ion-content[_ngcontent-%COMP%]   .see-more[_ngcontent-%COMP%]{font-family:var(--mont-regular);color:var(--ion-color-tertiary-shade);font-size:12px;display:flex;align-items:center;justify-content:center;margin-top:2rem}ion-content[_ngcontent-%COMP%]   .instruction-text[_ngcontent-%COMP%]{text-align:center;color:var(--clr-primary-900);font-family:var(--mont-regular);font-size:14px}ion-content[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]{margin-top:0;gap:1rem;align-self:center;width:80%;display:flex;align-items:center;justify-content:center}ion-content[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .country-code[_ngcontent-%COMP%], ion-content[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   ion-input[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;gap:5px;padding:12px 16px;height:52px;font-size:16px;border-radius:8px}ion-content[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .country-code[_ngcontent-%COMP%]{font-weight:600}ion-content[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .country-code[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{font-size:14px;font-family:var(--mont-regular);color:var(--ion-color-medium-shade)}ion-content[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .country-code[_ngcontent-%COMP%]   ion-img.flag[_ngcontent-%COMP%]{width:30px}ion-content[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   ion-input[_ngcontent-%COMP%]{flex:1;align-items:center;display:flex;height:52px;font-size:15px;border-radius:8px;justify-content:center;text-align:center}ion-content[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%]{margin-top:0;--background: var(--clr-primary-900);color:var(--ion-color-tertiary-contrast);height:56px;font-family:var(--mont-bold);font-size:18px}"]})}}return e})();var s=c(58133);let l=(()=>{class e{constructor(){this.view="total",this.points=0,this.viewChange=new n.bkB}onViewChange(t){const o=t.detail.value;this.view=o,this.viewChange.emit(o)}static{this.\u0275fac=function(o){return new(o||e)}}static{this.\u0275cmp=n.VBU({type:e,selectors:[["app-points-selector"]],inputs:{view:"view",points:"points"},outputs:{viewChange:"viewChange"},decls:12,vars:10,consts:[[1,"points-selector"],["interface","popover",1,"points-dropdown",3,"ngModel","ngModelChange","ionChange"],["value","pending"],["value","total"],[1,"selected-points-value"]],template:function(o,i){1&o&&(n.j41(0,"div",0)(1,"ion-select",1),n.bIt("ngModelChange",function(P){return i.view=P})("ionChange",function(P){return i.onViewChange(P)}),n.j41(2,"ion-select-option",2),n.EFF(3),n.nI1(4,"translate"),n.k0s(),n.j41(5,"ion-select-option",3),n.EFF(6),n.nI1(7,"translate"),n.k0s()(),n.j41(8,"div",4)(9,"span"),n.EFF(10),n.nI1(11,"number"),n.k0s()()()),2&o&&(n.R7$(1),n.Y8G("ngModel",i.view),n.R7$(2),n.JRh(n.bMT(4,4,"fidelity-page.waiting")),n.R7$(3),n.JRh(n.bMT(7,6,"fidelity-page.total-points")),n.R7$(4),n.SpI("",n.bMT(11,8,i.points)," Pts"))},dependencies:[g.BC,g.vS,r.Nm,r.Ip,r.Je,p.QX,v.D9],styles:[".points-selector[_ngcontent-%COMP%]{margin:12px 0;display:flex;flex-direction:column;align-items:flex-start}.points-selector[_ngcontent-%COMP%]   .points-dropdown[_ngcontent-%COMP%]{--color: var(--ion-color-tertiary-contrast);--placeholder-color: var(--ion-color-tertiary-contrast);--background: transparent;--border-color: transparent;--padding-start: 0;--padding-end: 0;font-size:14px;font-family:var(--mont-regular);width:100%;margin-bottom:8px}.points-selector[_ngcontent-%COMP%]   .points-dropdown[_ngcontent-%COMP%]   ion-select-option[_ngcontent-%COMP%]{--color: var(--ion-color-dark)}.points-selector[_ngcontent-%COMP%]   .selected-points-value[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{font-size:25px;font-family:var(--mont-semibold);color:var(--ion-color-tertiary-contrast);display:block}"]})}}return e})();var a=c(94440),_=c(51591);const C=function(e){return{"background-image":e}};function b(e,m){if(1&e){const t=n.RV6();n.j41(0,"div",17)(1,"div",18)(2,"h2"),n.EFF(3),n.nI1(4,"translate"),n.k0s(),n.j41(5,"div",19),n.nI1(6,"loyaltyProgramClassColor"),n.EFF(7),n.nI1(8,"number"),n.nI1(9,"translate"),n.k0s(),n.j41(10,"app-points-selector",20),n.bIt("viewChange",function(i){n.eBV(t);const d=n.XpG(2);return n.Njj(d.onPointsViewChange(i))}),n.k0s(),n.j41(11,"div",21)(12,"div",22),n.EFF(13),n.nI1(14,"loyaltyProgramLevelLabel"),n.k0s(),n.j41(15,"div",23),n.EFF(16),n.k0s()()()()}if(2&e){const t=n.XpG(2);let o,i,d,P;n.Y8G("ngStyle",n.eq3(19,C,"url("+t.imagesUrl[(null!==(o=null==t.points?null:t.points.status)&&void 0!==o?o:1)-1]+")")),n.R7$(3),n.JRh(n.bMT(4,9,"fidelity-page.points")),n.R7$(2),n.Y8G("ngClass",n.bMT(6,11,null!==(i=null==t.points?null:t.points.status)&&void 0!==i?i:1)),n.R7$(2),n.Lme(" ",n.bMT(8,13,(null==t.points?null:t.points.validate)||"0")," ",n.bMT(9,15,"fidelity-page.pts")," "),n.R7$(3),n.Y8G("view",t.selectedPointsView)("points",t.getDisplayedPoints()),n.R7$(3),n.JRh(n.bMT(14,17,null!==(d=null==t.points?null:t.points.status)&&void 0!==d?d:1)),n.R7$(3),n.JRh((null!==(P=null==t.currentAdvantages||null==t.currentAdvantages.pointsRange?null:t.currentAdvantages.pointsRange.min)&&void 0!==P?P:0)+" - "+(null!==(P=null==t.currentAdvantages||null==t.currentAdvantages.pointsRange?null:t.currentAdvantages.pointsRange.max)&&void 0!==P?P:2e3))}}function E(e,m){1&e&&n.nrm(0,"div",24)}function F(e,m){if(1&e&&(n.j41(0,"ion-item"),n.nrm(1,"img",26),n.j41(2,"ion-label"),n.EFF(3),n.k0s()()),2&e){const t=m.$implicit,o=n.XpG(3);let i;n.R7$(1),n.Y8G("src",o.imagesCheckBox[(null!==(i=null==o.points?null:o.points.status)&&void 0!==i?i:1)-1],n.B4B),n.R7$(2),n.JRh(t)}}function T(e,m){if(1&e&&(n.j41(0,"ion-list"),n.DNE(1,F,4,2,"ion-item",25),n.k0s()),2&e){const t=n.XpG(2);n.R7$(1),n.Y8G("ngForOf",t.getCurrentAdvantages(t.currentAdvantages))}}function S(e,m){1&e&&n.nrm(0,"div",28)}const D=function(){return[1,2,3,4]};function $(e,m){1&e&&n.DNE(0,S,1,0,"div",27),2&e&&n.Y8G("ngForOf",n.lJ4(1,D))}function j(e,m){1&e&&n.nrm(0,"div",29)}function A(e,m){if(1&e&&(n.j41(0,"div",38),n.nrm(1,"img",26),n.j41(2,"span"),n.EFF(3),n.k0s()()),2&e){const t=m.$implicit,o=n.XpG().$implicit,i=n.XpG(2);n.R7$(1),n.Y8G("src",i.imagesCheckBox[(null==o?null:o.statusValue)-1],n.B4B),n.R7$(2),n.JRh(t)}}const z=function(e){return{"background-color":e}};function B(e,m){if(1&e&&(n.j41(0,"div",30)(1,"div",31)(2,"div",32)(3,"div",33),n.EFF(4,"Profil"),n.k0s(),n.j41(5,"h2"),n.EFF(6),n.k0s(),n.j41(7,"div",34),n.EFF(8),n.k0s()()(),n.j41(9,"div",35)(10,"div",36),n.DNE(11,A,4,2,"div",37),n.k0s()()()),2&e){const t=m.$implicit,o=n.XpG(2);n.Y8G("ngStyle",n.eq3(5,z,o.bgColors[(null==t?null:t.statusValue)-1])),n.R7$(1),n.Y8G("ngStyle",n.eq3(7,C,"url("+o.imagesUrl[(null==t?null:t.statusValue)-1]+")")),n.R7$(5),n.JRh(t.label),n.R7$(2),n.SpI(" ",t.pointsRange.max?(null==t||null==t.pointsRange?null:t.pointsRange.min)+" - "+(null==t||null==t.pointsRange?null:t.pointsRange.max)+" Points":(null==t||null==t.pointsRange?null:t.pointsRange.min)+"+ Points"," "),n.R7$(3),n.Y8G("ngForOf",o.getCurrentAdvantages(t))}}function G(e,m){if(1&e&&(n.j41(0,"div"),n.DNE(1,b,17,21,"div",9),n.DNE(2,E,1,0,"ng-template",null,10,n.C5r),n.j41(4,"div",11),n.EFF(5),n.nI1(6,"translate"),n.k0s(),n.DNE(7,T,2,1,"ion-list",12),n.DNE(8,$,1,2,"ng-template",null,13,n.C5r),n.j41(10,"div",11),n.EFF(11),n.nI1(12,"translate"),n.k0s(),n.j41(13,"div",14),n.DNE(14,j,1,0,"div",15),n.DNE(15,B,12,9,"div",16),n.k0s()()),2&e){const t=n.sdS(3),o=n.sdS(9),i=n.XpG();n.R7$(1),n.Y8G("ngIf",!i.isLoading)("ngIfElse",t),n.R7$(4),n.JRh(n.bMT(6,8,"fidelity-page.description-title")),n.R7$(2),n.Y8G("ngIf",!i.isLoading)("ngIfElse",o),n.R7$(4),n.JRh(n.bMT(12,10,"fidelity-page.category")),n.R7$(3),n.Y8G("ngIf",i.isLoading),n.R7$(1),n.Y8G("ngForOf",i.advantages)}}function L(e,m){1&e&&(n.j41(0,"div")(1,"div",39),n.nrm(2,"ion-img",40),n.j41(3,"ion-label",41),n.EFF(4),n.nI1(5,"translate"),n.k0s()()()),2&e&&(n.R7$(4),n.JRh(n.bMT(5,1,"fidelity-page.no-gifts")))}function N(e,m){1&e&&n.nrm(0,"div",42)}const U=[{path:"",component:(()=>{class e{constructor(t,o,i){this.fidelityService=t,this.modalCtrl=o,this.storagesSrv=i,this.isLoading=!1,this.currentTab="points",this.selectedPointsView="total",this.advantagesByCategory={Privil\u00e8ge:["Ce niveau offre des avantages exclusifs","Des r\xe9compenser pour votre fid\xe9lit\xe9.","Profitez de points convertibles en cadeaux."]},this.imagesUrl=["/assets/images/amigo_banner.png","/assets/images/Colombe.png","/assets/images/PELICAN.png"],this.imagesCheckBox=["/assets/icons/checkbox_blue.png","/assets/icons/checkbox_blue.png","/assets/icons/checkbox_yellow.png"],this.bgColors=["var(--clr-secondary-100)","var(--clr-secondary-100)","var(--clr-premium-50)"],this.isReferralSheetOpen=!1,this.commonService=(0,n.WQX)(y.h)}ngOnInit(){var t=this;return(0,u.A)(function*(){t.commonService.showNav=!0,yield t.getPoints()})()}ionViewWillEnter(){var t=this;return(0,u.A)(function*(){t.isLoading=!0,yield t.getPoints(),yield t.loadAvantages(),t.isLoading=!1})()}getPoints(){var t=this;return(0,u.A)(function*(){const o=t.storagesSrv.getUserConnected();let i={};"company"in o&&o.category===s.s.CompanyUser&&(i.companyId=o.company._id);const d=yield t.fidelityService.getPoints(i);t.points=d.points,t.currentAdvantages=d.advantage})()}getCurrentAdvantages(t){let o=[...t?.oneTimeBenefit||[],...t?.monthlyBenefit||[],...t?.annualBenefit||[]];return o?.length?o:this.advantagesByCategory.Privil\u00e8ge}back(){this.commonService.navigateTo("navigation/home-alt")}loadContent(){setTimeout(()=>{},1e3)}closeReferralBottomSheet(){this.isReferralSheetOpen=!1}openReferralBottomSheet(){var t=this;return(0,u.A)(function*(){yield(yield t.modalCtrl.create({component:I,initialBreakpoint:.5,cssClass:"modal",breakpoints:[0,.5,.5,.5,1],mode:"ios",handle:!0})).present()})()}loadAvantages(){var t=this;return(0,u.A)(function*(){const o=yield t.fidelityService.getBenefitRewards(t.points?.status?{statusValue:{$ne:t.points?.status}}:{});t.advantages=o?.data})()}onPointsViewChange(t){this.selectedPointsView=t}getDisplayedPoints(){return this.fidelityService.getDisplayedPoints(this.points,this.selectedPointsView)}getDisplayedPointsLabel(){return this.fidelityService.getDisplayedPointsLabel(this.selectedPointsView)}static{this.\u0275fac=function(o){return new(o||e)(n.rXU(O._),n.rXU(r.W3),n.rXU(x.n))}}static{this.\u0275cmp=n.VBU({type:e,selectors:[["app-fidelity-program"]],decls:17,vars:12,consts:[[1,"header"],["slot","start","src","/assets/icons/arrow-blue.svg",3,"click"],[1,"title"],["slot","end",1,"addrefferal-btn",3,"click"],[3,"fullscreen"],["id","container"],[1,"container"],[4,"ngIf"],["cardGhostLoading",""],["class","points-card",3,"ngStyle",4,"ngIf","ngIfElse"],["pointsGhostLoading",""],[1,"section-title"],[4,"ngIf","ngIfElse"],["advantagesGhostLoading",""],[1,"categories-grid"],["class","loyalty-card ghost-loading",4,"ngIf"],["class","loyalty-card",3,"ngStyle",4,"ngFor","ngForOf"],[1,"points-card",3,"ngStyle"],[1,"points-info"],[1,"points-value",3,"ngClass"],[3,"view","points","viewChange"],[1,"category-info"],[1,"category-name"],[1,"category-points"],[1,"ghost-loading","points-card"],[4,"ngFor","ngForOf"],[1,"colombe-checkbox",3,"src"],["class","ghost-loading advantage-item",4,"ngFor","ngForOf"],[1,"ghost-loading","advantage-item"],[1,"loyalty-card","ghost-loading"],[1,"loyalty-card",3,"ngStyle"],[1,"card-header",3,"ngStyle"],[1,"elt-card"],[1,"profile-label"],[1,"points-label"],[1,"card-content"],[1,"benefits-list"],["class","benefit-item",4,"ngFor","ngForOf"],[1,"benefit-item"],[1,"gifts-empty-state"],["src","/assets/icons/Research paper-amico.svg",1,"full-page-image"],[1,"no-gifts-label"],[1,"ghost-loading","loyalty-card"]],template:function(o,i){1&o&&(n.j41(0,"ion-header")(1,"ion-toolbar",0)(2,"ion-img",1),n.bIt("click",function(){return i.back()}),n.k0s(),n.j41(3,"ion-title",2),n.EFF(4),n.nI1(5,"truncateString"),n.nI1(6,"translate"),n.k0s(),n.j41(7,"ion-button",3),n.bIt("click",function(){return i.openReferralBottomSheet()}),n.EFF(8),n.nI1(9,"translate"),n.k0s()()(),n.j41(10,"ion-content",4)(11,"div",5)(12,"div",6),n.DNE(13,G,16,12,"div",7),n.DNE(14,L,6,3,"div",7),n.k0s()()(),n.DNE(15,N,1,0,"ng-template",null,8,n.C5r)),2&o&&(n.R7$(4),n.JRh(n.i5U(5,5,n.bMT(6,8,"fidelity-page.title"),15)),n.R7$(4),n.JRh(n.bMT(9,10,"fidelity-page.add-referral")),n.R7$(2),n.Y8G("fullscreen",!0),n.R7$(3),n.Y8G("ngIf","points"===i.currentTab),n.R7$(1),n.Y8G("ngIf","gifts"===i.currentTab))},dependencies:[p.YU,p.Sq,p.bT,p.B3,r.Jm,r.W9,r.eU,r.KW,r.uz,r.he,r.nf,r.BC,r.ai,l,p.QX,a.c,_.j8,_.i8,v.D9],styles:["*[_ngcontent-%COMP%]{margin:0;padding:0}.header-md[_ngcontent-%COMP%]:after{background-image:none!important}.modal[_ngcontent-%COMP%]{--border-radius: calc(60 * var(--res))}.input-group[_ngcontent-%COMP%]{margin-top:calc(75 * var(--res))}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{--padding-start: 0;margin-bottom:calc(50 * var(--res));--placeholder-color: $color-six}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-input[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{font-family:Mont Regular;font-weight:400;font-size:calc(42 * var(--res));color:#1e1e1e}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{padding-top:calc(25 * var(--res));padding-bottom:calc(25 * var(--res))}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]{margin-bottom:calc(50 * var(--res));display:flex;align-items:center;justify-content:space-between;border-bottom-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-250, #c8c7cc)));border-bottom-width:.55px;border-bottom-style:solid}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{width:90%;margin-bottom:0;--border-color: transparent;--inner-border-width: 0px 0px 0px 0px;--border-style: none}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-checkbox[_ngcontent-%COMP%]{margin-top:5%}.bg-primary[_ngcontent-%COMP%]{color:#143c5d}.message[_ngcontent-%COMP%]{margin-bottom:calc(75 * var(--res))}.message[_ngcontent-%COMP%]   ion-title[_ngcontent-%COMP%]{font-weight:800;font-size:calc(70 * var(--res));padding-bottom:calc(25 * var(--res))}.message[_ngcontent-%COMP%]   ion-text[_ngcontent-%COMP%]{font-weight:400;color:#757474;display:block;line-height:1.5em!important}.btn--meduim[_ngcontent-%COMP%]{height:calc(130 * var(--res));font-size:calc(50 * var(--res));font-family:var(--mont-bold)}.btn--meduim[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:var(--mont-bold)}.btn--small[_ngcontent-%COMP%]{height:calc(100 * var(--res))}.btn--round[_ngcontent-%COMP%]{--border-radius: 50%;padding-inline-start:0;padding-inline-end:0;--padding-start: 0;--padding-end: 0}.btn--border-primary[_ngcontent-%COMP%]{--background: transparent;--background-activated: #c4e6d0;--border-color: var(--ion-color-primary);--border-style: solid;--border-width: 2px}.btn--border-secondary[_ngcontent-%COMP%]{--border-width: 2px;--background: transparent;--background-activated: #ddabb2;--border-color: var(--ion-color-secondary);--border-style: solid}.btn--upper[_ngcontent-%COMP%]{text-transform:uppercase}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]{overflow:hidden}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar{width:5px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-track{box-shadow:inset 0 0 5px #546e8d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background:#143c5d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover{background:#143c5d}.bottom-sheet-content[_ngcontent-%COMP%]{background:#fdfdfe;height:100%}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-thumbnail[_ngcontent-%COMP%]{margin-left:calc(31.25 * var(--res));background-color:#1e1e1e;--border-radius: 50%;--size: 23}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:Mont Bold;font-size:calc(45 * var(--res));text-align:center;color:#757474}.bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%], .bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]{padding:calc(75 * var(--res))}.font-size-5[_ngcontent-%COMP%]{font-size:calc(42 * var(--res))}ion-header[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]{--padding-start: var(--space-5);--padding-end: var(--space-5);--padding-top: var(--space-5);--border-color: transparent;--background-color: transparent;background-color:#f1f2f4;display:flex;align-items:center}ion-header[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   ion-img[_ngcontent-%COMP%]{width:calc(50 * var(--res));margin-right:calc(25 * var(--res))}ion-header[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{font-family:Mont Regular;text-align:start;color:var(--clr-primary-900)}ion-header[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .addrefferal-btn[_ngcontent-%COMP%]{--background: var(--ion-color-tertiary-shade);--border-radius: 35px;--color: var(--ion-color-tertiary-contrast);font-weight:700}ion-content[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]{padding:0;padding-top:.5rem;background-color:#f1f2f4}ion-content[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   ion-tab-button.hidden-tab[_ngcontent-%COMP%]{visibility:hidden;pointer-events:none}ion-content[_ngcontent-%COMP%]   ion-list[_ngcontent-%COMP%]{padding:0 calc(51 * var(--res)) 16px calc(51 * var(--res));background:transparent}ion-content[_ngcontent-%COMP%]   ion-list[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{--background: transparent;--padding-start: 0;--padding-end: 0}ion-content[_ngcontent-%COMP%]   .tab-container[_ngcontent-%COMP%]{padding:0 calc(51 * var(--res)) 16px calc(51 * var(--res));min-height:35px;justify-content:space-evenly;border:none}ion-content[_ngcontent-%COMP%]   .tab-container[_ngcontent-%COMP%]   ion-tab-button[_ngcontent-%COMP%]{min-width:-moz-fit-content;min-width:fit-content}ion-content[_ngcontent-%COMP%]   .tab-container[_ngcontent-%COMP%]   ion-title[_ngcontent-%COMP%]{font-size:var(--fs-14-px);color:#6d839d;font-family:Mont SemiBold}ion-content[_ngcontent-%COMP%]   .tab-container[_ngcontent-%COMP%]   .active[_ngcontent-%COMP%]{color:#6d839d;border-bottom:3px solid #419CFB}ion-content[_ngcontent-%COMP%]   .tab-container[_ngcontent-%COMP%]   .active[_ngcontent-%COMP%]   ion-title[_ngcontent-%COMP%]{color:#0b305c}ion-content[_ngcontent-%COMP%]   .points-card[_ngcontent-%COMP%]{margin:16px calc(41 * var(--res));margin-top:8px;background-repeat:no-repeat;background-position:right;background-size:cover;position:relative;overflow:hidden;border-radius:16px;min-height:180px;color:var(--ion-color-tertiary-contrast);filter:none}ion-content[_ngcontent-%COMP%]   .points-card[_ngcontent-%COMP%]   .points-info[_ngcontent-%COMP%]{padding:1rem;position:relative;z-index:2;background:none;border-radius:16px}ion-content[_ngcontent-%COMP%]   .points-card[_ngcontent-%COMP%]   .points-info[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{margin:0;margin-top:1rem;font-size:15px;font-weight:400;font-family:var(--mont-regular);color:var(--ion-color-tertiary-contrast);margin-bottom:.5rem}ion-content[_ngcontent-%COMP%]   .points-card[_ngcontent-%COMP%]   .points-info[_ngcontent-%COMP%]   .points-value[_ngcontent-%COMP%]{font-size:32px;font-family:var(--mont-bold);margin:0 0 14px}ion-content[_ngcontent-%COMP%]   .points-card[_ngcontent-%COMP%]   .points-info[_ngcontent-%COMP%]   .default-points[_ngcontent-%COMP%]{color:var(--ion-color-tertiary-contrast)}ion-content[_ngcontent-%COMP%]   .points-card[_ngcontent-%COMP%]   .points-info[_ngcontent-%COMP%]   .pelican-points[_ngcontent-%COMP%]{color:var(--ion-color-warning-contrast)}ion-content[_ngcontent-%COMP%]   .points-card[_ngcontent-%COMP%]   .points-info[_ngcontent-%COMP%]   .points-selector[_ngcontent-%COMP%]{margin:12px 0;display:flex;flex-direction:column;align-items:flex-start}ion-content[_ngcontent-%COMP%]   .points-card[_ngcontent-%COMP%]   .points-info[_ngcontent-%COMP%]   .points-selector[_ngcontent-%COMP%]   .points-dropdown[_ngcontent-%COMP%]{--color: var(--ion-color-tertiary-contrast);--placeholder-color: var(--ion-color-tertiary-contrast);--background: transparent;--border-color: transparent;--padding-start: 0;--padding-end: 0;font-size:14px;font-family:var(--mont-regular);width:100%;margin-bottom:8px}ion-content[_ngcontent-%COMP%]   .points-card[_ngcontent-%COMP%]   .points-info[_ngcontent-%COMP%]   .points-selector[_ngcontent-%COMP%]   .points-dropdown[_ngcontent-%COMP%]   ion-select-option[_ngcontent-%COMP%]{--color: var(--ion-color-dark)}ion-content[_ngcontent-%COMP%]   .points-card[_ngcontent-%COMP%]   .points-info[_ngcontent-%COMP%]   .points-selector[_ngcontent-%COMP%]   .selected-points-value[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{font-size:25px;font-family:var(--mont-semibold);color:var(--ion-color-tertiary-contrast);display:block}ion-content[_ngcontent-%COMP%]   .points-card[_ngcontent-%COMP%]   .points-info[_ngcontent-%COMP%]   .category-info[_ngcontent-%COMP%]{position:absolute;bottom:16px;right:16px;text-align:right}ion-content[_ngcontent-%COMP%]   .points-card[_ngcontent-%COMP%]   .points-info[_ngcontent-%COMP%]   .category-info[_ngcontent-%COMP%]   .category-name[_ngcontent-%COMP%]{font-size:16px;color:var(--ion-color-tertiary-contrast);margin-bottom:4px}ion-content[_ngcontent-%COMP%]   .points-card[_ngcontent-%COMP%]   .points-info[_ngcontent-%COMP%]   .category-info[_ngcontent-%COMP%]   .category-points[_ngcontent-%COMP%]{font-size:14px;color:#fffc}ion-content[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%]{color:var(--clr-primary-900);font-size:15px;line-height:0px;padding:16px calc(51 * var(--res));font-family:var(--mont-bold)}ion-content[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{color:var(--clr-primary-900);margin-left:.5em;font-family:var(--mont-regular);font-size:13px}ion-content[_ngcontent-%COMP%]   .categories-grid[_ngcontent-%COMP%]{padding:16px calc(41 * var(--res));display:flex;overflow-x:auto;padding:16px;gap:16px;-webkit-overflow-scrolling:touch;scrollbar-width:none}ion-content[_ngcontent-%COMP%]   .categories-grid[_ngcontent-%COMP%]::-webkit-scrollbar{display:none}ion-content[_ngcontent-%COMP%]   .categories-grid[_ngcontent-%COMP%]   .loyalty-card[_ngcontent-%COMP%]{flex:0 0 14em;width:300px;min-height:480px;border-radius:16px;overflow:hidden;box-shadow:0 4px 6px #0000001a;display:flex;flex-direction:column;background-color:var(--ion-color-tertiary-contrast)}ion-content[_ngcontent-%COMP%]   .categories-grid[_ngcontent-%COMP%]   .loyalty-card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]{background-repeat:no-repeat;background-position:center;background-size:cover;position:relative;overflow:hidden;border-radius:16px;min-height:180px;color:var(--ion-color-tertiary-contrast);filter:none}ion-content[_ngcontent-%COMP%]   .categories-grid[_ngcontent-%COMP%]   .loyalty-card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]   .elt-card[_ngcontent-%COMP%]{padding:2rem 2rem 0REM 2rem;margin-top:1rem}ion-content[_ngcontent-%COMP%]   .categories-grid[_ngcontent-%COMP%]   .loyalty-card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]   .elt-card[_ngcontent-%COMP%]   .profile-label[_ngcontent-%COMP%]{font-size:15px;color:var(--ion-color-tertiary-contrast);font-family:var(--mont-regular);margin-bottom:5px}ion-content[_ngcontent-%COMP%]   .categories-grid[_ngcontent-%COMP%]   .loyalty-card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]   .elt-card[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{font-size:30px;font-family:var(--mont-bold);display:block;margin-bottom:5px}ion-content[_ngcontent-%COMP%]   .categories-grid[_ngcontent-%COMP%]   .loyalty-card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]   .elt-card[_ngcontent-%COMP%]   .points-label[_ngcontent-%COMP%]{font-size:14px;font-family:var(--mont-semibold)}ion-content[_ngcontent-%COMP%]   .categories-grid[_ngcontent-%COMP%]   .loyalty-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]{padding:16px;flex:1;display:flex;flex-direction:column}ion-content[_ngcontent-%COMP%]   .categories-grid[_ngcontent-%COMP%]   .loyalty-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .benefits-list[_ngcontent-%COMP%]{flex:1}ion-content[_ngcontent-%COMP%]   .categories-grid[_ngcontent-%COMP%]   .loyalty-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .benefits-list[_ngcontent-%COMP%]   .benefit-item[_ngcontent-%COMP%]{display:flex;align-items:center;padding:14px 0;border-bottom:1px solid rgba(0,0,0,.1)}ion-content[_ngcontent-%COMP%]   .categories-grid[_ngcontent-%COMP%]   .loyalty-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .benefits-list[_ngcontent-%COMP%]   .benefit-item[_ngcontent-%COMP%]:last-child{border-bottom:none}ion-content[_ngcontent-%COMP%]   .categories-grid[_ngcontent-%COMP%]   .loyalty-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .benefits-list[_ngcontent-%COMP%]   .benefit-item[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{color:var(--clr-primary-900);margin-left:.5em;font-family:var(--mont-regular);font-size:13px}ion-content[_ngcontent-%COMP%]   .gifts-empty-state[_ngcontent-%COMP%]{display:flex;flex-direction:column;justify-content:center;align-items:center;height:80vh;text-align:center;padding:20px}ion-content[_ngcontent-%COMP%]   .gifts-empty-state[_ngcontent-%COMP%]   .full-page-image[_ngcontent-%COMP%]{width:80%;max-width:300px;height:auto;margin-bottom:20px}ion-content[_ngcontent-%COMP%]   .gifts-empty-state[_ngcontent-%COMP%]   .no-gifts-label[_ngcontent-%COMP%]{font-size:1.2rem;color:var(--ion-color-medium)}ion-content[_ngcontent-%COMP%]   .ghost-loading[_ngcontent-%COMP%]{border-radius:16px;background:linear-gradient(90deg,#f0f0f0 25%,#e0e0e0 50%,#f0f0f0 75%);background-size:200% 100%;animation:_ngcontent-%COMP%_shimmer 1.5s infinite}ion-content[_ngcontent-%COMP%]   .ghost-loading.points-card[_ngcontent-%COMP%]{height:180px;margin:0 16px}ion-content[_ngcontent-%COMP%]   .ghost-loading.loyalty-card[_ngcontent-%COMP%]{width:300px;height:480px;margin:16px 0}ion-content[_ngcontent-%COMP%]   .ghost-loading.advantage-item[_ngcontent-%COMP%]{height:48px;margin:8px 0;border-radius:8px}@keyframes _ngcontent-%COMP%_shimmer{0%{background-position:-200% 0}to{background-position:200% 0}}ion-content[_ngcontent-%COMP%]   .bottom-sheet[_ngcontent-%COMP%]{--border-radius: 16px 16px 0 0}.item-native[_ngcontent-%COMP%]{--padding: 0 !important}"]})}}return e})()}];let V=(()=>{class e{static{this.\u0275fac=function(o){return new(o||e)}}static{this.\u0275mod=n.$C({type:e})}static{this.\u0275inj=n.G2t({imports:[M.iI.forChild(U),M.iI]})}}return e})();var J=c(93887);let X=(()=>{class e{static{this.\u0275fac=function(o){return new(o||e)}}static{this.\u0275mod=n.$C({type:e})}static{this.\u0275inj=n.G2t({imports:[p.MD,g.YN,r.bv,V,J.G,v.h]})}}return e})()},80153:(k,f,c)=>{c.d(f,{s:()=>g});var g=function(r){return r[r.PENDING=200]="PENDING",r[r.VALIDATED=300]="VALIDATED",r[r.EXPIRED=400]="EXPIRED",r}(g||{})},94440:(k,f,c)=>{c.d(f,{c:()=>g});var p=c(2978);let g=(()=>{class r{transform(u,...n){return u?u.length>n[0]?`${u.substring(0,n[0]-3)}...`:u:""}static{this.\u0275fac=function(n){return new(n||r)}}static{this.\u0275pipe=p.EJ8({name:"truncateString",type:r,pure:!0})}}return r})()},13217:(k,f,c)=>{c.d(f,{_:()=>R});var p=c(73308),g=c(26409),r=c(45312),M=c(94934),u=c(79801),n=c(80153),w=c(99987),O=c(2978),y=c(33607),v=c(82571),x=c(62049);let R=(()=>{class h{constructor(s,l,a,_){this.baseUrl=s,this.http=l,this.commonSrv=a,this.translateSrv=_,this.ratioOfPointsByVolume=.2,this.POINTS_MATRIX={AMIGO:{5:1,25:5,50:10},COLOMBE:{5:2,25:8,50:15},PELICAN:{5:5,25:12,50:25}},this.base_url=`${this.baseUrl.getOrigin()}${r.c.basePath}`}calculateTotalPointsOrder(s,l){const a=l?.points?.status||u.Th.AMIGO;return s.reduce((_,C)=>{const b=C?.packaging?.unit?.value||0,E=C?.quantity||0;return _+(this.POINTS_MATRIX[a]?.[b]||0)*E},0)}getPoints(s){var l=this;return(0,p.A)(function*(){let a=new g.Nl;const{companyId:_}=s;return _&&(a=a.append("companyId",_)),yield(0,M.s)(l.http.get(`${l.base_url}loyalty-program/points`,{params:a}))})()}getBenefitRewards(s){var l=this;return(0,p.A)(function*(){let a=new g.Nl;return s?.monthly&&(a=a.append("monthly",JSON.stringify(s.monthly))),s?.statusValue&&(a=a.append("statusValue",JSON.stringify(s?.statusValue))),s?.annual&&(a=a.append("annual",JSON.stringify(s.annual))),s?.punctual&&(a=a.append("punctual",JSON.stringify(s.punctual))),yield(0,M.s)(l.http.get(`${l.base_url}advantages`,{params:a}))})()}acceptInvitation(s,l){var a=this;return(0,p.A)(function*(){return yield(0,M.s)(a.http.patch(`${a.base_url}invitations/${s}/accept`,{referredUserId:l}))})()}getInvitations(s){var l=this;return(0,p.A)(function*(){let a=new g.Nl;const{limit:_,status:C,prospectTel:b}=s;return _&&(a=a.append("limit",_)),b&&(a=a.append("prospectTel",b)),C&&(a=a.append("status",C)),yield(0,M.s)(l.http.get(`${l.base_url}invitations`,{params:a}))})()}sendReferralInvitation(s,l){var a=this;return(0,p.A)(function*(){try{const _={referrerId:s,prospectTel:l};return yield(0,M.s)(a.http.post(`${a.base_url}invitations/${s}/invite-referral`,_))}catch(_){const C=a.translateSrv.currentLang===w.T.French?"Impossible d'envoyer l'invitation de parrainage":"Failed to send referral invitation";return yield a.commonSrv.showToast({message:_?.error?.message??a.commonSrv.getError(C,_).message,color:"danger"}),_}})()}getPendingInvitationForUser(s){var l=this;return(0,p.A)(function*(){let a=new g.Nl;return a=a.set("prospectTel",+s),a=a.append("status",n.s.PENDING),yield(0,M.s)(l.http.get(`${l.base_url}invitations/pending-invitation`,{params:a}))})()}getDisplayedPoints(s,l){return"pending"===l?s?.unValidated||0:s?.totalPoints||0}getDisplayedPointsLabel(s){return"pending"===s?"fidelity-page.waiting":"fidelity-page.total-points"}static{this.\u0275fac=function(l){return new(l||h)(O.KVO(y.K),O.KVO(g.Qq),O.KVO(v.h),O.KVO(x.E))}}static{this.\u0275prov=O.jDH({token:h,factory:h.\u0275fac,providedIn:"root"})}}return h})()}}]);