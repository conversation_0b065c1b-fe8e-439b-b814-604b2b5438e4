"use strict";(self.webpackChunkapp=self.webpackChunkapp||[]).push([[4390],{81559:(R,O,s)=>{s.d(O,{Q:()=>T});var m=s(73308),n=s(35025),p=s.n(n),_=s(94934),d=s(56610),v=s(45312),y=s(26409),C=s(2978),x=s(82571),$=s(33607),S=s(14599);let T=(()=>{class h{constructor(i,a,e,c){this.http=i,this.commonSrv=a,this.baseUrlService=e,this.storageSrv=c,this.url=this.baseUrlService.getOrigin()+v.c.basePath}create(i){var a=this;return(0,m.A)(function*(){try{return yield(0,_.s)(a.http.post(`${a.url}orders`,i))}catch(e){const g={message:a.commonSrv.getError("",e).message,color:"danger"};return yield a.commonSrv.showToast(g),e}})()}createOrderByCommercialForClient(i,a){var e=this;return(0,m.A)(function*(){try{return yield(0,_.s)(e.http.post(`${e.url}orders/${a}`,i))}catch(c){const M={message:e.commonSrv.getError("",c).message,color:"danger"};return yield e.commonSrv.showToast(M),c}})()}getAllOrder(i){var a=this;return(0,m.A)(function*(){try{let e=new y.Nl;const{num:c,commercialId:g,status:M,offset:b,limit:f,startDate:I,endDate:E,customerReference:k,selectedCompanyId:F}=i;return I&&E&&(e=e.append("startDate",new d.vh("fr").transform(I,"YYYY-MM-dd"))),E&&I&&(e=e.append("endDate",new d.vh("fr").transform(E,"YYYY-MM-dd"))),k&&(e=e.append("appReference",k)),F&&(e=e.append("selectedCompanyId",F)),g&&(e=e.append("commercial",g)),void 0!==b&&(e=e.append("offset",b)),f&&(e=e.append("limit",f)),M&&(e=e.append("status",M)),c&&(e=e.append("appReference",c)),yield(0,_.s)(a.http.get(`${a.url}orders/history`,{params:e}))}catch(e){const g={message:a.commonSrv.getError("",e).message,color:"danger"};return yield a.commonSrv.showToast(g),e}})()}getOrders(i){var a=this;return(0,m.A)(function*(){try{let e=new y.Nl;const{status:c,appReference:g,offset:M,limit:b,userCategory:f,paymentMode:I,validation:E,customer:k,product:F,date:w,enable:A=!0}=i;return M&&(e=e.append("offset",M)),b&&(e=e.append("limit",b)),c&&(e=e.append("status",c)),g&&(e=e.append("appReference",`${g}`)),I&&(e=e.append("payment.mode.id",I)),f&&(e=e.append("user.category",f)),k&&(e=e.append("user.email",k)),F&&(e=e.append("cart.items.product.label",F)),E&&(e=e.append("validation",E)),w.start&&w.end&&(e=e.append("startDate",p()(w.start).format("YYYY-MM-DD")),e=e.append("endDate",p()(w.end).format("YYYY-MM-DD"))),e=e.append("enable",A),yield(0,_.s)(a.http.get(`${a.url}orders`,{params:e}))}catch(e){return e}})()}updateOrders(i,a){var e=this;return(0,m.A)(function*(){try{return yield(0,_.s)(e.http.patch(`${e.url}orders/${i}`,a))}catch(c){const M={message:e.commonSrv.getError("",c).message,color:"danger"};return yield e.commonSrv.showToast(M),c}})()}RhValidatedOrder(i,a){var e=this;return(0,m.A)(function*(){try{return yield(0,_.s)(e.http.patch(`${e.url}orders/${i._id}/validate`,a))}catch(c){const M={message:e.commonSrv.getError("",c).message,color:"danger"};return yield e.commonSrv.showToast(M),c}})()}RhRejectOrder(i){var a=this;return(0,m.A)(function*(){try{return yield(0,_.s)(a.http.patch(`${a.url}orders/${i._id}/reject`,{}))}catch(e){const g={message:a.commonSrv.getError("",e).message,color:"danger"};return yield a.commonSrv.showToast(g),e}})()}sendOtp(i){var a=this;return(0,m.A)(function*(){try{return yield(0,_.s)(a.http.post(`${a.url}callback/afriland`,i))}catch(e){const g={message:a.commonSrv.getError("",e).message,color:"danger"};return yield a.commonSrv.showToast(g),e}})()}sendWallet(i){var a=this;return(0,m.A)(function*(){try{return yield(0,_.s)(a.http.post(`${a.url}orders/verify-Wallet-Nber`,i))}catch(e){const g={message:a.commonSrv.getError("",e).message,color:"danger"};return yield a.commonSrv.showToast(g),e}})()}ubaPayment(i){var a=this;return(0,m.A)(function*(){try{return yield(0,_.s)(a.http.post(`${a.url}orders/m2u-paymentRequest`,i))}catch(e){const g={message:a.commonSrv.getError("",e).message,color:"danger"};return yield a.commonSrv.showToast(g),e}})()}find(i){var a=this;return(0,m.A)(function*(){try{return yield(0,_.s)(a.http.get(a.url+"orders/"+i))}catch(e){const g={message:a.commonSrv.getError("",e).message,color:"danger"};return yield a.commonSrv.showToast(g),e}})()}getCardToken(){var i=this;return(0,m.A)(function*(){try{return yield(0,_.s)(i.http.post(`${i.url}orders/order-generate-visa-key`,{}))}catch(a){const c={message:i.commonSrv.getError("",a).message,color:"danger"};return yield i.commonSrv.showToast(c),a}})()}setupPayerAuthentication(i,a){var e=this;return(0,m.A)(function*(){try{return yield(0,_.s)(e.http.post(`${e.url}orders/order-setup-payer-auth`,{transientTokenJwt:i,order:a}))}catch(c){const M={message:e.commonSrv.getError("",c).message,color:"danger"};return yield e.commonSrv.showToast(M),c}})()}authorizationWithPAEnroll(i,a){var e=this;return(0,m.A)(function*(){try{return yield(0,_.s)(e.http.post(`${e.url}orders/order-authorization-pay-enroll`,{order:i,options:a}))}catch(c){const M={message:e.commonSrv.getError("",c).message,color:"danger"};return yield e.commonSrv.showToast(M),c}})()}checkIfOrderExist(i){var a=this;return(0,m.A)(function*(){try{return yield(0,_.s)(a.http.get(`${a.url}orders/${i}/exist`))}catch(e){const g={message:a.commonSrv.getError("",e).message,color:"danger"};return yield a.commonSrv.showToast(g),e}})()}generatePurchaseOrder(i){var a=this;return(0,m.A)(function*(){try{return yield(0,_.s)(a.http.get(`${a.url}orders/${i}/generate-purchase`))}catch(e){const g={message:a.commonSrv.getError("",e).message,color:"danger"};return yield a.commonSrv.showToast(g),e}})()}cancellationOrder(i,a){var e=this;return(0,m.A)(function*(){try{return yield(0,_.s)(e.http.patch(`${e.url}orders/${i}/cancellation-order`,a))}catch(c){const M={message:e.commonSrv.getError("",c).message,color:"danger"};return yield e.commonSrv.showToast(M),c}})()}updateCarrier(i,a){var e=this;return(0,m.A)(function*(){try{return yield(0,_.s)(e.http.patch(`${e.url}orders/${i}/add-carrier`,{carrier:a}))}catch(c){const M={message:e.commonSrv.getError("",c).message,color:"danger"};return yield e.commonSrv.showToast(M),c}})()}static{this.\u0275fac=function(a){return new(a||h)(C.KVO(y.Qq),C.KVO(x.h),C.KVO($.K),C.KVO(S.n))}}static{this.\u0275prov=C.jDH({token:h,factory:h.\u0275fac,providedIn:"root"})}}return h})()},54648:(R,O,s)=>{s.d(O,{N:()=>dn});var m=s(73308),n=s(2978),p=s(82571),_=s(14599),d=s(77897),v=s(45312),y=s(94934),C=s(26409),x=s(33607);let $=(()=>{class r{constructor(t,l){this.http=t,this.baseUrlService=l,this.url=this.baseUrlService.getOrigin()+v.c.basePath}applyPromoCode(t,l){var o=this;return(0,m.A)(function*(){return yield(0,y.s)(o.http.post(`${o.url}promo-codes/apply/${t}`,l))})()}static{this.\u0275fac=function(l){return new(l||r)(n.KVO(C.Qq),n.KVO(x.K))}}static{this.\u0275prov=n.jDH({token:r,factory:r.\u0275fac,providedIn:"root"})}}return r})();var S=s(62049),T=s(37222);let h=(()=>{class r{constructor(t,l,o,P,D){this.commonSrv=t,this.storageSrv=l,this.modalCtrl=o,this.promoCodeSrv=P,this.translateService=D}ngOnInit(){}save(){var t=this;return(0,m.A)(function*(){t.isLoading=!0;const l=JSON.parse(t.storageSrv.load("cart"))??[],o=yield t.promoCodeSrv.applyPromoCode(t.codePromoValue,l);return o?.response?.statusCode>=400?(t.commonSrv.showToast({color:"danger",message:`${o.message}`}),t.modalCtrl.dismiss(),t.isLoading=!1):(t.modalCtrl.dismiss(o),t.isLoading=!1,o)})()}close(){this.modalCtrl.dismiss()}static{this.\u0275fac=function(l){return new(l||r)(n.rXU(p.h),n.rXU(_.n),n.rXU(d.W3),n.rXU($),n.rXU(S.E))}}static{this.\u0275cmp=n.VBU({type:r,selectors:[["app-promo-code-modal"]],decls:17,vars:1,consts:[[1,"bottom-sheet-content"],[1,"ion-text-center","ion-padding"],["slot","end",3,"click"],["src","assets/icons/close.svg"],[1,"input-group"],["position","floating"],["clearInput","","placeholder","Entrez le code promo","required","",3,"ngModel","ngModelChange"],[1,"btn-validate"],["color","primary","expand","block",1,"btn--meduim","btn--upper",3,"click"]],template:function(l,o){1&l&&(n.j41(0,"div",0)(1,"ion-header")(2,"ion-toolbar",1)(3,"ion-thumbnail",2),n.bIt("click",function(){return o.close()}),n.nrm(4,"ion-img",3),n.k0s(),n.j41(5,"ion-label"),n.EFF(6,"Saisissez le Code Promo"),n.k0s()()(),n.j41(7,"ion-content")(8,"div",4)(9,"ion-item")(10,"ion-label",5),n.EFF(11,"Saisissez le Code Promo"),n.k0s(),n.j41(12,"ion-input",6),n.bIt("ngModelChange",function(D){return o.codePromoValue=D}),n.k0s()()(),n.j41(13,"div",7)(14,"ion-button",8),n.bIt("click",function(){return o.save()}),n.j41(15,"ion-label"),n.EFF(16," VALIDER "),n.k0s()()()()()),2&l&&(n.R7$(12),n.Y8G("ngModel",o.codePromoValue))},dependencies:[T.BC,T.YS,T.vS,d.Jm,d.W9,d.eU,d.KW,d.$w,d.uz,d.he,d.Zx,d.ai,d.Gw],styles:["*[_ngcontent-%COMP%]{margin:0;padding:0}.header-md[_ngcontent-%COMP%]:after{background-image:none!important}.modal[_ngcontent-%COMP%]{--border-radius: calc(60 * var(--res))}.input-group[_ngcontent-%COMP%]{margin-top:calc(75 * var(--res))}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{--padding-start: 0;margin-bottom:calc(50 * var(--res));--placeholder-color: $color-six}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-input[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{font-family:Mont Regular;font-weight:400;font-size:calc(42 * var(--res));color:#1e1e1e}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{padding-top:calc(25 * var(--res));padding-bottom:calc(25 * var(--res))}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]{margin-bottom:calc(50 * var(--res));display:flex;align-items:center;justify-content:space-between;border-bottom-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-250, #c8c7cc)));border-bottom-width:.55px;border-bottom-style:solid}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{width:90%;margin-bottom:0;--border-color: transparent;--inner-border-width: 0px 0px 0px 0px;--border-style: none}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-checkbox[_ngcontent-%COMP%]{margin-top:5%}.bg-primary[_ngcontent-%COMP%]{color:#143c5d}.message[_ngcontent-%COMP%]{margin-bottom:calc(75 * var(--res))}.message[_ngcontent-%COMP%]   ion-title[_ngcontent-%COMP%]{font-weight:800;font-size:calc(70 * var(--res));padding-bottom:calc(25 * var(--res))}.message[_ngcontent-%COMP%]   ion-text[_ngcontent-%COMP%]{font-weight:400;color:#757474;display:block;line-height:1.5em!important}.btn--meduim[_ngcontent-%COMP%]{height:calc(130 * var(--res));font-size:calc(50 * var(--res));font-family:var(--mont-bold)}.btn--meduim[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:var(--mont-bold)}.btn--small[_ngcontent-%COMP%]{height:calc(100 * var(--res))}.btn--round[_ngcontent-%COMP%]{--border-radius: 50%;padding-inline-start:0;padding-inline-end:0;--padding-start: 0;--padding-end: 0}.btn--border-primary[_ngcontent-%COMP%]{--background: transparent;--background-activated: #c4e6d0;--border-color: var(--ion-color-primary);--border-style: solid;--border-width: 2px}.btn--border-secondary[_ngcontent-%COMP%]{--border-width: 2px;--background: transparent;--background-activated: #ddabb2;--border-color: var(--ion-color-secondary);--border-style: solid}.btn--upper[_ngcontent-%COMP%]{text-transform:uppercase}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]{overflow:hidden}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar{width:5px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-track{box-shadow:inset 0 0 5px #546e8d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background:#143c5d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover{background:#143c5d}.bottom-sheet-content[_ngcontent-%COMP%]{background:#fdfdfe;height:100%}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-thumbnail[_ngcontent-%COMP%]{margin-left:calc(31.25 * var(--res));background-color:#1e1e1e;--border-radius: 50%;--size: 23}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:Mont Bold;font-size:calc(45 * var(--res));text-align:center;color:#757474}.bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%], .bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]{padding:calc(75 * var(--res))}.font-size-5[_ngcontent-%COMP%]{font-size:calc(42 * var(--res))}.bottom-sheet-content[_ngcontent-%COMP%]   .btn-validate[_ngcontent-%COMP%], .bottom-sheet-content[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]{padding:0 calc(41 * var(--res))}"]})}}return r})();var z=s(99987),i=s(56610),a=s(11244),e=s(74657);let c=(()=>{class r{static{this.\u0275fac=function(l){return new(l||r)}}static{this.\u0275cmp=n.VBU({type:r,selectors:[["app-item-view"]],inputs:{item:"item"},decls:11,vars:9,consts:[[1,"card-content"],[1,"product-image"],[1,"mWidth",3,"src"],[1,"detail"]],template:function(l,o){1&l&&(n.j41(0,"ion-card")(1,"ion-card-content",0)(2,"div",1),n.nrm(3,"ion-img",2),n.k0s(),n.j41(4,"div",3)(5,"ion-label"),n.EFF(6),n.nI1(7,"capitalize"),n.k0s(),n.j41(8,"ion-label"),n.EFF(9),n.nI1(10,"translate"),n.k0s()()()()),2&l&&(n.R7$(3),n.Y8G("src",null==o.item||null==o.item.product?null:o.item.product.image),n.R7$(3),n.JRh(n.bMT(7,5,null==o.item||null==o.item.product?null:o.item.product.label)),n.R7$(3),n.E5c(" ",null==o.item?null:o.item.quantity," ",n.bMT(10,7,"order-new-page.second-step.bag-off")," ",null==o.item||null==o.item.packaging?null:o.item.packaging.label,""))},dependencies:[d.b_,d.I9,d.KW,d.he,a.F,e.D9],styles:["*[_ngcontent-%COMP%]{margin:0;padding:0}.header-md[_ngcontent-%COMP%]:after{background-image:none!important}.modal[_ngcontent-%COMP%]{--border-radius: calc(60 * var(--res))}.input-group[_ngcontent-%COMP%]{margin-top:calc(75 * var(--res))}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{--padding-start: 0;margin-bottom:calc(50 * var(--res));--placeholder-color: $color-six}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-input[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{font-family:Mont Regular;font-weight:400;font-size:calc(42 * var(--res));color:#1e1e1e}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{padding-top:calc(25 * var(--res));padding-bottom:calc(25 * var(--res))}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]{margin-bottom:calc(50 * var(--res));display:flex;align-items:center;justify-content:space-between;border-bottom-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-250, #c8c7cc)));border-bottom-width:.55px;border-bottom-style:solid}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{width:90%;margin-bottom:0;--border-color: transparent;--inner-border-width: 0px 0px 0px 0px;--border-style: none}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-checkbox[_ngcontent-%COMP%]{margin-top:5%}.bg-primary[_ngcontent-%COMP%]{color:#143c5d}.message[_ngcontent-%COMP%]{margin-bottom:calc(75 * var(--res))}.message[_ngcontent-%COMP%]   ion-title[_ngcontent-%COMP%]{font-weight:800;font-size:calc(70 * var(--res));padding-bottom:calc(25 * var(--res))}.message[_ngcontent-%COMP%]   ion-text[_ngcontent-%COMP%]{font-weight:400;color:#757474;display:block;line-height:1.5em!important}.btn--meduim[_ngcontent-%COMP%]{height:calc(130 * var(--res));font-size:calc(50 * var(--res));font-family:var(--mont-bold)}.btn--meduim[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:var(--mont-bold)}.btn--small[_ngcontent-%COMP%]{height:calc(100 * var(--res))}.btn--round[_ngcontent-%COMP%]{--border-radius: 50%;padding-inline-start:0;padding-inline-end:0;--padding-start: 0;--padding-end: 0}.btn--border-primary[_ngcontent-%COMP%]{--background: transparent;--background-activated: #c4e6d0;--border-color: var(--ion-color-primary);--border-style: solid;--border-width: 2px}.btn--border-secondary[_ngcontent-%COMP%]{--border-width: 2px;--background: transparent;--background-activated: #ddabb2;--border-color: var(--ion-color-secondary);--border-style: solid}.btn--upper[_ngcontent-%COMP%]{text-transform:uppercase}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]{overflow:hidden}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar{width:5px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-track{box-shadow:inset 0 0 5px #546e8d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background:#143c5d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover{background:#143c5d}.bottom-sheet-content[_ngcontent-%COMP%]{background:#fdfdfe;height:100%}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-thumbnail[_ngcontent-%COMP%]{margin-left:calc(31.25 * var(--res));background-color:#1e1e1e;--border-radius: 50%;--size: 23}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:Mont Bold;font-size:calc(45 * var(--res));text-align:center;color:#757474}.bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%], .bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]{padding:calc(75 * var(--res))}.font-size-5[_ngcontent-%COMP%]{font-size:calc(42 * var(--res))}ion-card[_ngcontent-%COMP%]{box-shadow:none;width:calc(2.1 * 112.5 * var(--res))}ion-card[_ngcontent-%COMP%]   .detail[_ngcontent-%COMP%]{margin-top:calc(.4 * 41 * var(--res));display:flex;flex-direction:column;justify-content:center}ion-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   div.product-image[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center}ion-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   div.product-image[_ngcontent-%COMP%]   ion-img[_ngcontent-%COMP%]{width:55%}ion-card[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{text-align:center;text-overflow:ellipsis;overflow:hidden!important;text-wrap:nowrap}ion-card[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]:first-child{color:#000;font-family:Mont SemiBold;font-size:calc(32 * var(--res))}ion-card[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]:last-child{font-size:calc(32 * var(--res))}"]})}}return r})();function g(r,u){1&r&&n.nrm(0,"app-item-view",7),2&r&&n.Y8G("item",u.$implicit)}let M=(()=>{class r{constructor(){this.items=[],this.modalCtrl=(0,n.WQX)(d.W3),this.popoverCtrl=(0,n.WQX)(d.IE),this.header=(0,n.WQX)(S.E).currentLang===z.T.French?"Liste des produits":"List of products",this.closeModal=()=>this.modalCtrl.dismiss()&&this.popoverCtrl.dismiss(),this.trackByFn=(t,l)=>t}static{this.\u0275fac=function(l){return new(l||r)}}static{this.\u0275cmp=n.VBU({type:r,selectors:[["app-list-of-items"]],inputs:{items:"items"},decls:10,vars:3,consts:[[1,"product-detail-container","bottom-sheet-content"],[1,"ion-text-center","ion-padding"],["slot","end",3,"click"],["src","assets/icons/close-modal-red.svg"],["id","container",1,"scroller-container"],[1,"product-container"],[3,"item",4,"ngFor","ngForOf","ngForTrackBy"],[3,"item"]],template:function(l,o){1&l&&(n.j41(0,"div",0)(1,"ion-header")(2,"ion-toolbar",1)(3,"ion-thumbnail",2),n.bIt("click",function(){return o.closeModal()}),n.nrm(4,"ion-img",3),n.k0s(),n.j41(5,"ion-label"),n.EFF(6),n.k0s()()()(),n.j41(7,"section",4)(8,"div",5),n.DNE(9,g,1,1,"app-item-view",6),n.k0s()()),2&l&&(n.R7$(6),n.SpI(" ",o.header," "),n.R7$(3),n.Y8G("ngForOf",o.items)("ngForTrackBy",o.trackByFn))},dependencies:[d.eU,d.KW,d.he,d.Zx,d.ai,i.Sq,c],styles:["*[_ngcontent-%COMP%]{margin:0;padding:0}.header-md[_ngcontent-%COMP%]:after{background-image:none!important}.modal[_ngcontent-%COMP%]{--border-radius: calc(60 * var(--res))}.input-group[_ngcontent-%COMP%]{margin-top:calc(75 * var(--res))}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{--padding-start: 0;margin-bottom:calc(50 * var(--res));--placeholder-color: $color-six}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-input[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{font-family:Mont Regular;font-weight:400;font-size:calc(42 * var(--res));color:#1e1e1e}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{padding-top:calc(25 * var(--res));padding-bottom:calc(25 * var(--res))}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]{margin-bottom:calc(50 * var(--res));display:flex;align-items:center;justify-content:space-between;border-bottom-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-250, #c8c7cc)));border-bottom-width:.55px;border-bottom-style:solid}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{width:90%;margin-bottom:0;--border-color: transparent;--inner-border-width: 0px 0px 0px 0px;--border-style: none}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-checkbox[_ngcontent-%COMP%]{margin-top:5%}.bg-primary[_ngcontent-%COMP%]{color:#143c5d}.message[_ngcontent-%COMP%]{margin-bottom:calc(75 * var(--res))}.message[_ngcontent-%COMP%]   ion-title[_ngcontent-%COMP%]{font-weight:800;font-size:calc(70 * var(--res));padding-bottom:calc(25 * var(--res))}.message[_ngcontent-%COMP%]   ion-text[_ngcontent-%COMP%]{font-weight:400;color:#757474;display:block;line-height:1.5em!important}.btn--meduim[_ngcontent-%COMP%]{height:calc(130 * var(--res));font-size:calc(50 * var(--res));font-family:var(--mont-bold)}.btn--meduim[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:var(--mont-bold)}.btn--small[_ngcontent-%COMP%]{height:calc(100 * var(--res))}.btn--round[_ngcontent-%COMP%]{--border-radius: 50%;padding-inline-start:0;padding-inline-end:0;--padding-start: 0;--padding-end: 0}.btn--border-primary[_ngcontent-%COMP%]{--background: transparent;--background-activated: #c4e6d0;--border-color: var(--ion-color-primary);--border-style: solid;--border-width: 2px}.btn--border-secondary[_ngcontent-%COMP%]{--border-width: 2px;--background: transparent;--background-activated: #ddabb2;--border-color: var(--ion-color-secondary);--border-style: solid}.btn--upper[_ngcontent-%COMP%]{text-transform:uppercase}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]{overflow:hidden}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar{width:5px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-track{box-shadow:inset 0 0 5px #546e8d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background:#143c5d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover{background:#143c5d}.bottom-sheet-content[_ngcontent-%COMP%]{background:#fdfdfe;height:100%}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-thumbnail[_ngcontent-%COMP%]{margin-left:calc(31.25 * var(--res));background-color:#1e1e1e;--border-radius: 50%;--size: 23}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:Mont Bold;font-size:calc(45 * var(--res));text-align:center;color:#757474}.bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%], .bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]{padding:calc(75 * var(--res))}.font-size-5[_ngcontent-%COMP%]{font-size:calc(42 * var(--res))}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]{text-align:start!important}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-thumbnail[_ngcontent-%COMP%]{--size: calc(45 * var(--res));margin-right:calc(15 * var(--res))}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{text-align:start;color:#0b305c;font-size:calc(40.8 * var(--res));margin-left:calc(25 * var(--res))}@media only screen and (max-width: 412px){.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{margin-left:0}}.product-container[_ngcontent-%COMP%]{gap:calc(41 * var(--res));display:flex;align-items:center;justify-content:space-around;padding:0 calc(75 * var(--res));margin:1em 0;flex-wrap:wrap;height:100%}app-item-view[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center}"]})}}return r})();var b=s(54357),f=s(68953),I=s(62897),E=s(58133),k=s(13217),F=s(79801),w=s(94440);let A=(()=>{class r{transform(t,...l){return t&&t instanceof Array?t.reduce((o,P)=>o+P.quantity,0):0}static{this.\u0275fac=function(l){return new(l||r)}}static{this.\u0275pipe=n.EJ8({name:"getTotalQuantityOfProductsInCart",type:r,pure:!0})}}return r})();function L(r,u){1&r&&(n.j41(0,"div",19)(1,"ion-label",20),n.EFF(2),n.nI1(3,"translate"),n.k0s()()),2&r&&(n.R7$(2),n.JRh(n.bMT(3,1,"order-new-page.third-step.title")))}function N(r,u){1&r&&(n.j41(0,"ion-label",4)(1,"strong"),n.EFF(2),n.nI1(3,"translate"),n.k0s()()),2&r&&(n.R7$(2),n.JRh(n.bMT(3,1,"order-new-page.third-step.unitPrice")))}function G(r,u){1&r&&(n.j41(0,"ion-label",4)(1,"strong"),n.EFF(2,"Points"),n.k0s()())}function U(r,u){if(1&r&&(n.j41(0,"ion-label",4),n.EFF(1),n.nI1(2,"number"),n.k0s()),2&r){const t=n.XpG().$implicit;n.R7$(1),n.SpI("",n.brH(2,1,null==t?null:t.unitPrice,"","fr")," XAF")}}function X(r,u){if(1&r&&(n.j41(0,"ion-label",4),n.EFF(1),n.nI1(2,"number"),n.k0s()),2&r){const t=n.XpG().$implicit,l=n.XpG();n.R7$(1),n.SpI("",n.brH(2,1,l.getPointsForItem(t),"","fr")," PTS ")}}const j=function(r,u,t){return[r,u,t]};function Y(r,u){if(1&r&&(n.j41(0,"div")(1,"li",21)(2,"div",22)(3,"ion-label"),n.EFF(4),n.nI1(5,"capitalize"),n.k0s(),n.j41(6,"ion-label"),n.EFF(7),n.k0s()(),n.j41(8,"ion-label",4),n.nrm(9,"ion-img",23),n.k0s(),n.j41(10,"ion-label",4),n.EFF(11),n.k0s(),n.DNE(12,U,3,5,"ion-label",5),n.DNE(13,X,3,5,"ion-label",5),n.k0s(),n.nrm(14,"li",24),n.k0s()),2&r){const t=u.$implicit,l=n.XpG();n.R7$(4),n.JRh(n.bMT(5,6,null==t||null==t.product?null:t.product.label)),n.R7$(3),n.JRh(null==t||null==t.packaging?null:t.packaging.label),n.R7$(2),n.FS9("src",null==t||null==t.product?null:t.product.image),n.R7$(2),n.JRh(null==t?null:t.quantity),n.R7$(1),n.Y8G("ngIf",!l.isCompanyCategoryBaker&&n.sMw(8,j,l.userCategory.Particular,l.userCategory.Commercial,l.userCategory.DonutAnimator).includes(null==l.commonSrv||null==l.commonSrv.user?null:l.commonSrv.user.category)),n.R7$(1),n.Y8G("ngIf",l.isPoint)}}function W(r,u){if(1&r&&(n.j41(0,"ion-label",8)(1,"strong"),n.EFF(2),n.nI1(3,"number"),n.k0s()()),2&r){const t=n.XpG();n.R7$(2),n.SpI("",n.brH(3,1,null==t.orderPrice?null:t.orderPrice.cartAmount,"","fr")," XAF")}}function J(r,u){if(1&r&&(n.j41(0,"ion-label",8)(1,"strong"),n.EFF(2),n.nI1(3,"number"),n.k0s()()),2&r){const t=n.XpG();n.R7$(2),n.SpI("",n.brH(3,1,t.points,"","fr")," PTS")}}function K(r,u){1&r&&(n.j41(0,"div",19)(1,"ion-label",20),n.EFF(2),n.nI1(3,"translate"),n.k0s()()),2&r&&(n.R7$(2),n.JRh(n.bMT(3,1,"order-new-page.third-step.title-shipping")))}function V(r,u){if(1&r&&(n.j41(0,"li",25)(1,"ion-text"),n.EFF(2),n.nI1(3,"translate"),n.k0s(),n.j41(4,"ion-text"),n.EFF(5),n.nI1(6,"date"),n.k0s()()),2&r){const t=n.XpG();n.R7$(2),n.JRh(n.bMT(3,2,"order.detail.delivery.date")),n.R7$(3),n.JRh(n.brH(6,4,null==t.shipping?null:t.shipping.deliveryDate,"dd/MM/yyyy","fr")||"N/A")}}function H(r,u){if(1&r&&(n.j41(0,"ul",2)(1,"li",3)(2,"ion-label",26)(3,"strong"),n.EFF(4),n.nI1(5,"titlecase"),n.nI1(6,"translate"),n.k0s()(),n.j41(7,"ion-label",26)(8,"strong"),n.EFF(9),n.nI1(10,"translate"),n.k0s()(),n.j41(11,"ion-label",4)(12,"strong"),n.EFF(13,"TOTAL"),n.k0s()()(),n.j41(14,"div")(15,"li",21)(16,"div",26),n.EFF(17),n.nI1(18,"number"),n.k0s(),n.j41(19,"ion-label",26),n.EFF(20),n.nI1(21,"number"),n.k0s(),n.j41(22,"ion-label",4),n.EFF(23),n.nI1(24,"number"),n.k0s()()()()),2&r){const t=n.XpG();n.R7$(4),n.SpI("",n.bMT(5,5,n.bMT(6,7,"order-new-page.second-step.bag-off")),"(50Kg)"),n.R7$(5),n.JRh(n.bMT(10,9,"order-new-page.third-step.unitCost")),n.R7$(8),n.SpI("",n.brH(18,11,null==t.shippingInfo?null:t.shippingInfo.totalNumberOfBags,"","fr")," "),n.R7$(3),n.SpI("",n.brH(21,15,null==t.shippingInfo?null:t.shippingInfo.amount,"","fr")," "),n.R7$(3),n.SpI("",n.brH(24,19,null==t.shippingInfo?null:t.shippingInfo.totalShippingPrice,"","fr")," XAF")}}function Q(r,u){1&r&&n.nrm(0,"app-item-view",27),2&r&&n.Y8G("item",u.$implicit)}function Z(r,u){if(1&r){const t=n.RV6();n.j41(0,"ion-text",28),n.bIt("click",function(){n.eBV(t);const o=n.XpG();return n.Njj(o.openListOfItems())}),n.EFF(1),n.nI1(2,"translate"),n.nrm(3,"ion-icon",29),n.k0s()}2&r&&(n.R7$(1),n.SpI(" ",n.bMT(2,1,"order-new-page.third-step.seeMore")," "))}function q(r,u){if(1&r&&(n.j41(0,"ion-text",30),n.EFF(1),n.nI1(2,"translate"),n.k0s()),2&r){const t=n.XpG();n.R7$(1),n.Lme(" ",n.bMT(2,2,"order-new-page.third-step.total-tons")," : ",t.getTotalQuantityOrder(t.itemsLimited)/20," T ")}}function nn(r,u){if(1&r&&(n.j41(0,"ion-text",30),n.EFF(1),n.k0s()),2&r){const t=n.XpG();n.R7$(1),n.SpI(" TOTAL POINT(S) : ",t.points," Pt(s) ")}}function tn(r,u){if(1&r&&(n.j41(0,"li")(1,"ion-text"),n.EFF(2),n.k0s(),n.j41(3,"ion-text",38),n.EFF(4),n.nI1(5,"number"),n.k0s()()),2&r){const t=u.$implicit;n.R7$(2),n.JRh(null==t?null:t.key),n.R7$(2),n.SpI("- ",n.brH(5,2,null==t?null:t.value,"","fr")," XAF")}}function en(r,u){if(1&r&&(n.j41(0,"li")(1,"ion-text"),n.EFF(2),n.nI1(3,"translate"),n.k0s(),n.j41(4,"ion-text"),n.EFF(5),n.nI1(6,"number"),n.k0s()()),2&r){const t=n.XpG(2);n.R7$(2),n.JRh(n.bMT(3,2,"order-new-page.third-step.shipping-costs")),n.R7$(3),n.SpI("",n.brH(6,4,null==t.orderPrice?null:t.orderPrice.shipping,"","fr")," XAF")}}function on(r,u){if(1&r&&(n.j41(0,"ul",31)(1,"div",32)(2,"li")(3,"ion-text",33),n.EFF(4),n.nI1(5,"translate"),n.k0s(),n.j41(6,"ion-text",33),n.EFF(7),n.nI1(8,"number"),n.k0s()(),n.j41(9,"li")(10,"ion-text"),n.EFF(11),n.nI1(12,"translate"),n.k0s(),n.j41(13,"ion-text"),n.EFF(14),n.nI1(15,"number"),n.k0s()(),n.j41(16,"li")(17,"ion-text",33),n.EFF(18),n.nI1(19,"translate"),n.k0s(),n.j41(20,"ion-text",33),n.EFF(21),n.nI1(22,"number"),n.k0s()(),n.DNE(23,tn,6,6,"li",34),n.nI1(24,"keyvalue"),n.DNE(25,en,7,8,"li",35),n.j41(26,"li")(27,"ion-text",36),n.EFF(28),n.nI1(29,"translate"),n.k0s(),n.j41(30,"ion-text",37),n.EFF(31),n.nI1(32,"number"),n.k0s()()()()),2&r){const t=n.XpG();n.R7$(4),n.JRh(n.bMT(5,11,"order-new-page.third-step.amount-ht")),n.R7$(3),n.SpI("",n.brH(8,13,null==t.orderPrice?null:t.orderPrice.HT,"","fr")," XAF"),n.R7$(4),n.JRh(n.bMT(12,17,"order-new-page.third-step.taxes")),n.R7$(3),n.SpI("",n.brH(15,19,null==t.orderPrice?null:t.orderPrice.VAT,"","fr")," XAF"),n.R7$(4),n.JRh(n.bMT(19,23,"order-new-page.third-step.cart-amount")),n.R7$(3),n.SpI("",n.brH(22,25,null==t.orderPrice?null:t.orderPrice.cartAmount,"","fr")," XAF"),n.R7$(2),n.Y8G("ngForOf",n.bMT(24,29,null==t.orderPrice?null:t.orderPrice.discount))("ngForTrackBy",null==t.commonSrv?null:t.commonSrv.trackByFn),n.R7$(2),n.Y8G("ngIf",(null==t.orderPrice?null:t.orderPrice.shipping)&&0!==(null==t.orderPrice?null:t.orderPrice.shipping)),n.R7$(3),n.JRh(n.bMT(29,31,"order-new-page.third-step.total-ttc")),n.R7$(3),n.SpI(" ",n.brH(32,33,null==t.orderPrice?null:t.orderPrice.TTC,"","fr")," XAF ")}}function rn(r,u){if(1&r&&(n.j41(0,"ion-text",36),n.EFF(1),n.nI1(2,"number"),n.k0s()),2&r){const t=n.XpG(2);n.R7$(1),n.SpI(" ",n.brH(2,1,null==t.orderPrice?null:t.orderPrice.TTC,"","fr")," XAF")}}function an(r,u){if(1&r&&(n.j41(0,"ion-text",36),n.EFF(1),n.nI1(2,"number"),n.k0s()),2&r){const t=n.XpG(2);n.R7$(1),n.SpI(" ",n.brH(2,1,null==t.orderPrice||null==t.orderPrice.discount?null:t.orderPrice.discount.promoCode,"","fr")," XAF")}}function cn(r,u){if(1&r){const t=n.RV6();n.j41(0,"ul",39)(1,"li")(2,"ion-text",33),n.EFF(3,"Nouveau TTC"),n.k0s(),n.DNE(4,rn,3,5,"ion-text",40),n.k0s(),n.j41(5,"li")(6,"ion-text",33),n.EFF(7,"Reduction"),n.k0s(),n.DNE(8,an,3,5,"ion-text",40),n.k0s(),n.j41(9,"button",41),n.bIt("click",function(){n.eBV(t);const o=n.XpG();return n.Njj(o.cancelDiscount())}),n.EFF(10,"Annuler le code promo"),n.k0s()()}if(2&r){const t=n.XpG();n.R7$(4),n.Y8G("ngIf",t.orderPrice),n.R7$(4),n.Y8G("ngIf",t.orderPrice)}}function sn(r,u){1&r&&(n.j41(0,"div",19)(1,"ion-label",20),n.EFF(2),n.nI1(3,"translate"),n.k0s()()),2&r&&(n.R7$(2),n.JRh(n.bMT(3,1,"order-new-page.third-step.carrier-title")))}function ln(r,u){if(1&r&&(n.j41(0,"ul",31)(1,"div",32)(2,"li")(3,"ion-text",42),n.EFF(4),n.nI1(5,"truncateString"),n.nI1(6,"capitalize"),n.nI1(7,"translate"),n.k0s(),n.j41(8,"ion-text",43),n.EFF(9),n.k0s()(),n.j41(10,"li")(11,"ion-text",42),n.EFF(12),n.nI1(13,"capitalize"),n.nI1(14,"translate"),n.k0s(),n.j41(15,"ion-text",43),n.EFF(16),n.k0s()(),n.j41(17,"li")(18,"ion-text",42),n.EFF(19),n.nI1(20,"truncateString"),n.nI1(21,"capitalize"),n.nI1(22,"translate"),n.k0s(),n.j41(23,"ion-text",43),n.EFF(24),n.k0s()(),n.j41(25,"li")(26,"ion-text",42),n.EFF(27),n.nI1(28,"truncateString"),n.nI1(29,"capitalize"),n.nI1(30,"translate"),n.k0s(),n.j41(31,"ion-text",43),n.EFF(32),n.nI1(33,"truncateString"),n.k0s()(),n.j41(34,"li")(35,"ion-text",42),n.EFF(36),n.nI1(37,"truncateString"),n.nI1(38,"capitalize"),n.nI1(39,"translate"),n.k0s(),n.j41(40,"ion-text",43),n.EFF(41),n.k0s()(),n.j41(42,"li")(43,"ion-text",42),n.EFF(44),n.nI1(45,"truncateString"),n.nI1(46,"capitalize"),n.nI1(47,"translate"),n.k0s(),n.j41(48,"ion-text",43),n.EFF(49),n.k0s()(),n.j41(50,"li")(51,"ion-text",42),n.EFF(52),n.nI1(53,"truncateString"),n.nI1(54,"capitalize"),n.nI1(55,"translate"),n.k0s(),n.j41(56,"ion-text",43),n.EFF(57),n.k0s()()()()),2&r){const t=n.XpG();n.R7$(4),n.SpI("",n.i5U(5,14,n.bMT(6,17,n.bMT(7,19,"order-new-page.first-step.driver-name")),12)," :"),n.R7$(5),n.JRh((null==t.carrierOrderInformation?null:t.carrierOrderInformation.name)||(null==t.carrierInformation?null:t.carrierInformation.name)||"N/A"),n.R7$(3),n.SpI("",n.bMT(13,21,n.bMT(14,23,"bottom-sheet-validation.tel"))," :"),n.R7$(4),n.JRh((null==t.carrierOrderInformation?null:t.carrierOrderInformation.phone)||(null==t.carrierInformation?null:t.carrierInformation.phone)||"N/A"),n.R7$(3),n.SpI("",n.i5U(20,25,n.bMT(21,28,n.bMT(22,30,"order-new-page.first-step.driver-id")),18)," :"),n.R7$(5),n.JRh((null==t.carrierOrderInformation?null:t.carrierOrderInformation.idCard)||(null==t.carrierInformation?null:t.carrierInformation.idCard)||"N/A"),n.R7$(3),n.SpI("",n.i5U(28,32,n.bMT(29,35,n.bMT(30,37,"order-new-page.first-step.driver-category")),16)," :"),n.R7$(5),n.JRh(n.i5U(33,39,(null==t.carrierOrderInformation?null:t.carrierOrderInformation.vehicleCategory)||(null==t.carrierInformation?null:t.carrierInformation.vehicleCategory)||"N/A",12)),n.R7$(4),n.SpI("",n.i5U(37,42,n.bMT(38,45,n.bMT(39,47,"order-new-page.first-step.driver-vehicle")),15)," :"),n.R7$(5),n.JRh((null==t.carrierOrderInformation?null:t.carrierOrderInformation.vehiclePlate)||(null==t.carrierInformation?null:t.carrierInformation.vehiclePlate)||"N/A"),n.R7$(3),n.SpI("",n.i5U(45,49,n.bMT(46,52,n.bMT(47,54,"order-new-page.first-step.driver-license")),15)," :"),n.R7$(5),n.JRh((null==t.carrierOrderInformation?null:t.carrierOrderInformation.driverLicense)||(null==t.carrierInformation?null:t.carrierInformation.driverLicense)||"N/A"),n.R7$(3),n.SpI(" ",n.i5U(53,56,n.bMT(54,59,n.bMT(55,61,"order-new-page.first-step.delivery-location")),15)," :"),n.R7$(5),n.JRh(t.customerDeliveryDestination||"N/A")}}const gn=function(r){return{"box-shadow":r}},B=function(r){return[r]};let dn=(()=>{class r{constructor(){this.itemsLimited=[],this.CompanyCategory=f.kJ,this.userCategory=E.s,this.bagDefaultValue=50,this.promoCodeAction=b.F,this.renderType=I.n,this.storageService=(0,n.WQX)(_.n),this.commonSrv=(0,n.WQX)(p.h),this.loyaltyProgramSrv=(0,n.WQX)(k._),this.isFrench=(0,n.WQX)(S.E).currentLang===z.T.French,this.isCompanyCategoryBaker=!1,this.POINTS_MATRIX={AMIGO:{5:1,25:5,50:10},COLOMBE:{5:2,25:8,50:15},PELICAN:{5:5,25:12,50:25}}}ngOnInit(){this.isCompanyCategoryBaker=this.commonSrv?.user?.company?.category===f.kJ.Baker;const t=this.storageService.load("carrierInformation");this.carrierInformation=t?JSON.parse(t):null,this.carrierInformation&&"Autre Cat\xe9gorie"===this.carrierInformation?.vehicleCategory&&this.carrierInformation?.otherCategory&&(this.carrierInformation.vehicleCategory=this.carrierInformation?.otherCategory),this.choiceDelivery="true"===this.storageService.load("choiceDelivery"),this.choiceDeliveryLocation="true"===this.storageService.load("choiceDeliveryLocation"),this.customerDeliveryDestination=this.storageService.load("customerDeliveryDestination")}openListOfItems(){var t=this;return(0,m.A)(function*(){yield(yield t.commonSrv.modalCtrl.create({component:M,mode:"ios",cssClass:"modalClass",componentProps:{items:t.cart.items}})).present()})()}getTotalQuantityOrder(t){return t?.reduce((o,P)=>o+P?.quantity*P?.packaging?.unit?.value,0)/this.bagDefaultValue}getListOfItemsColumnAndRow(t=0){return t&&1!==t?2===t?["two-columns"]:3===t?["three-columns","one-row"]:4===t?["two-columns","two-rows"]:[5,6].includes(t)?["three-columns","two-rows"]:["three-columns","three-rows"]:[]}codePromoInfosHandle(t){this.isCodePromoActive=t.detail.checked,this.openCodePromoModal()}openCodePromoModal(){var t=this;return(0,m.A)(function*(){if(!t.isCodePromoActive)return;const l=yield t.commonSrv.modalCtrl.create({component:h,initialBreakpoint:.4,cssClass:"modal",breakpoints:[0,.6,.7],mode:"ios"});yield l.present();const{data:o}=yield l.onWillDismiss();if(!o)return void(t.isCodePromoActive=!1);t.isDiscount=!0;const P=JSON.parse(t.storageService.load("cart"));return o>P.amount.TTC?(t.commonSrv.showToast({color:"danger",message:t.isFrench?"le montant du code promo est superieur au montant de votre commande":"The promo code amount is greater than your order total amount"}),t.isCodePromoActive=!1):(P.amount.discount=o,P.amount.TTC-=P.amount.discount,t.orderPrice=P.amount,t.storageService.store("cart",JSON.stringify(P)),t.commonSrv.showToast({color:"success",message:t.isFrench?"Code promo appliqu\xe9 avec succ\xe8s":"The promo code was successfully applied"}),t.isCodePromoActive=!1)})()}cancelDiscount(){const t=JSON.parse(this.storageService.load("cart"));t.amount.TTC+=t.amount.discount,delete t.amount.discount,this.orderPrice=t.amount,this.storageService.store("cart",JSON.stringify(t)),this.isDiscount=!1,this.commonSrv.showToast({color:"success",message:this.isFrench?"Le code promo a \xe9t\xe9 annuler avec succ\xe8s":"The promo code was successfully canceled"})}getPointsForItem(t){const l=this.commonSrv?.user?.points?.status||F.Th.AMIGO,o=t?.packaging?.unit?.value||0,P=t?.quantity||0;return(this.POINTS_MATRIX?.[l]?.[o]||0)*P}static{this.\u0275fac=function(l){return new(l||r)}}static{this.\u0275cmp=n.VBU({type:r,selectors:[["app-purchase-summary"]],inputs:{cart:"cart",isLoading:"isLoading",orderPrice:"orderPrice",itemsLimited:"itemsLimited",itemsMarket:"itemsMarket",shippingInfo:"shippingInfo",isPoint:"isPoint",shipping:"shipping",points:"points",carrierInformation:"carrierInformation",carrierOrderInformation:"carrierOrderInformation"},decls:41,vars:51,consts:[[1,"recap-container",3,"ngStyle"],["class","padding-horizontal",4,"ngIf"],[1,"product-list"],[1,"list-elt","head"],[1,"col"],["class","col",4,"ngIf"],[4,"ngFor","ngForOf"],[1,"list-elt","footer"],[1,"col","fextrabold"],["class","col fextrabold",4,"ngIf"],[1,"divider"],["class","padding-horizontal sub-title",4,"ngIf"],["class","product-list",4,"ngIf"],[1,"product-container"],[3,"item",4,"ngFor","ngForOf","ngForTrackBy"],["class","see-more","color","secondary",3,"click",4,"ngIf"],["class","see-more","color","secondary",4,"ngIf"],["class","detail-order padding-horizontal",4,"ngIf"],["class","detail-order padding-horizontal discount-details",4,"ngIf"],[1,"padding-horizontal"],[1,"title","recap-title"],[1,"list-elt"],[1,"col","product"],[3,"src"],[1,"list-elt","line"],[1,"padding-horizontal","sub-title"],[1,"col","equal"],[3,"item"],["color","secondary",1,"see-more",3,"click"],["name","arrow-forward-outline"],["color","secondary",1,"see-more"],[1,"detail-order","padding-horizontal"],[1,"border"],[1,"fbold"],[4,"ngFor","ngForOf","ngForTrackBy"],[4,"ngIf"],[1,"fextrabold"],["color","secondary",1,"fextrabold"],[1,"positive"],[1,"detail-order","padding-horizontal","discount-details"],["class","fextrabold",4,"ngIf"],[1,"cancel-discount",3,"click"],[1,"title"],[1,"value"]],template:function(l,o){1&l&&(n.j41(0,"div",0),n.DNE(1,L,4,3,"div",1),n.j41(2,"ul",2)(3,"li",3)(4,"ion-label",4)(5,"strong"),n.EFF(6),n.nI1(7,"translate"),n.k0s()(),n.j41(8,"ion-label",4)(9,"strong"),n.EFF(10),n.nI1(11,"translate"),n.nI1(12,"translate"),n.k0s()(),n.DNE(13,N,4,3,"ion-label",5),n.DNE(14,G,3,0,"ion-label",5),n.k0s(),n.DNE(15,Y,15,12,"div",6),n.j41(16,"li",7)(17,"ion-label",8)(18,"strong"),n.EFF(19),n.nI1(20,"translate"),n.k0s()(),n.j41(21,"ion-label",8)(22,"strong"),n.EFF(23),n.nI1(24,"getTotalQuantityOfProductsInCart"),n.nI1(25,"translate"),n.k0s()(),n.DNE(26,W,4,5,"ion-label",9),n.DNE(27,J,4,5,"ion-label",9),n.k0s()(),n.nrm(28,"div",10),n.DNE(29,K,4,3,"div",1),n.DNE(30,V,7,8,"li",11),n.DNE(31,H,25,23,"ul",12),n.j41(32,"div",13),n.DNE(33,Q,1,1,"app-item-view",14),n.k0s(),n.DNE(34,Z,4,3,"ion-text",15),n.DNE(35,q,3,4,"ion-text",16),n.DNE(36,nn,2,1,"ion-text",16),n.DNE(37,on,33,37,"ul",17),n.DNE(38,cn,11,2,"ul",18),n.DNE(39,sn,4,3,"div",1),n.DNE(40,ln,58,63,"ul",17),n.k0s()),2&l&&(n.Y8G("ngStyle",n.eq3(37,gn,o.isPoint&&"none")),n.R7$(1),n.Y8G("ngIf",!o.isPoint),n.R7$(5),n.JRh(n.bMT(7,25,"order-new-page.third-step.product")),n.R7$(4),n.Lme("",n.bMT(11,27,"order-new-page.third-step.qte")," (",n.bMT(12,29,"reseller-new-page.detail.bags"),")"),n.R7$(3),n.Y8G("ngIf",!o.isCompanyCategoryBaker&&(null==o.commonSrv||null==o.commonSrv.user?null:o.commonSrv.user.category)!==o.userCategory.Particular&&(null==o.commonSrv||null==o.commonSrv.user?null:o.commonSrv.user.category)!==o.userCategory.Commercial),n.R7$(1),n.Y8G("ngIf",o.isPoint),n.R7$(1),n.Y8G("ngForOf",null==o.cart?null:o.cart.items),n.R7$(4),n.SpI("TOTAL ",n.bMT(20,31,"order-new-page.third-step.flour"),""),n.R7$(4),n.Lme("",n.bMT(24,33,null==o.cart?null:o.cart.items)," ",n.bMT(25,35,"order-new-page.third-step.parcel"),""),n.R7$(3),n.Y8G("ngIf",!o.isCompanyCategoryBaker&&(null==o.commonSrv||null==o.commonSrv.user?null:o.commonSrv.user.category)!==o.userCategory.Particular&&(null==o.commonSrv||null==o.commonSrv.user?null:o.commonSrv.user.category)!==o.userCategory.Commercial),n.R7$(1),n.Y8G("ngIf",o.isPoint),n.R7$(2),n.Y8G("ngIf",(null==o.shippingInfo?null:o.shippingInfo.totalShippingPrice)&&!o.isCompanyCategoryBaker&&(null==o.commonSrv||null==o.commonSrv.user?null:o.commonSrv.user.category)!==o.userCategory.Particular),n.R7$(1),n.Y8G("ngIf",(null==o.cart?null:o.cart.renderType)===o.renderType.RENDU),n.R7$(1),n.Y8G("ngIf",(null==o.shippingInfo?null:o.shippingInfo.totalShippingPrice)&&!o.isCompanyCategoryBaker&&((null==o.commonSrv||null==o.commonSrv.user?null:o.commonSrv.user.category)!==o.userCategory.Particular||(null==o.commonSrv||null==o.commonSrv.user?null:o.commonSrv.user.category)!==o.userCategory.Commercial)),n.R7$(2),n.Y8G("ngForOf",o.itemsLimited)("ngForTrackBy",null==o.commonSrv?null:o.commonSrv.trackByFn),n.R7$(1),n.Y8G("ngIf",(null==o.cart||null==o.cart.items?null:o.cart.items.length)>3&&!o.isPoint),n.R7$(1),n.Y8G("ngIf",!o.isPoint),n.R7$(1),n.Y8G("ngIf",o.isPoint),n.R7$(1),n.Y8G("ngIf",!o.isCompanyCategoryBaker&&!n.sMw(39,j,o.userCategory.Particular,o.userCategory.Commercial,o.userCategory.DonutAnimator).includes(null==o.commonSrv||null==o.commonSrv.user?null:o.commonSrv.user.category)),n.R7$(1),n.Y8G("ngIf",o.isDiscount&&o.orderPrice.discount&&!n.sMw(43,j,o.userCategory.Particular,o.userCategory.Commercial,o.userCategory.DonutAnimator).includes(null==o.commonSrv||null==o.commonSrv.user?null:o.commonSrv.user.category)),n.R7$(1),n.Y8G("ngIf",!o.choiceDelivery&&n.eq3(47,B,null==o.userCategory?null:o.userCategory.CompanyUser).includes(null==o.commonSrv||null==o.commonSrv.user?null:o.commonSrv.user.category)),n.R7$(1),n.Y8G("ngIf",!o.choiceDelivery&&n.eq3(49,B,null==o.userCategory?null:o.userCategory.CompanyUser).includes(null==o.commonSrv||null==o.commonSrv.user?null:o.commonSrv.user.category)))},dependencies:[d.iq,d.KW,d.he,d.IO,i.Sq,i.bT,i.B3,i.QX,i.PV,i.vh,i.lG,e.D9,w.c,A,a.F],styles:["*[_ngcontent-%COMP%]{margin:0;padding:0}.header-md[_ngcontent-%COMP%]:after{background-image:none!important}.modal[_ngcontent-%COMP%]{--border-radius: calc(60 * var(--res))}.input-group[_ngcontent-%COMP%]{margin-top:calc(75 * var(--res))}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{--padding-start: 0;margin-bottom:calc(50 * var(--res));--placeholder-color: $color-six}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-input[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{font-family:Mont Regular;font-weight:400;font-size:calc(42 * var(--res));color:#1e1e1e}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{padding-top:calc(25 * var(--res));padding-bottom:calc(25 * var(--res))}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]{margin-bottom:calc(50 * var(--res));display:flex;align-items:center;justify-content:space-between;border-bottom-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-250, #c8c7cc)));border-bottom-width:.55px;border-bottom-style:solid}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{width:90%;margin-bottom:0;--border-color: transparent;--inner-border-width: 0px 0px 0px 0px;--border-style: none}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-checkbox[_ngcontent-%COMP%]{margin-top:5%}.bg-primary[_ngcontent-%COMP%]{color:#143c5d}.message[_ngcontent-%COMP%]{margin-bottom:calc(75 * var(--res))}.message[_ngcontent-%COMP%]   ion-title[_ngcontent-%COMP%]{font-weight:800;font-size:calc(70 * var(--res));padding-bottom:calc(25 * var(--res))}.message[_ngcontent-%COMP%]   ion-text[_ngcontent-%COMP%]{font-weight:400;color:#757474;display:block;line-height:1.5em!important}.btn--meduim[_ngcontent-%COMP%]{height:calc(130 * var(--res));font-size:calc(50 * var(--res));font-family:var(--mont-bold)}.btn--meduim[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:var(--mont-bold)}.btn--small[_ngcontent-%COMP%]{height:calc(100 * var(--res))}.btn--round[_ngcontent-%COMP%]{--border-radius: 50%;padding-inline-start:0;padding-inline-end:0;--padding-start: 0;--padding-end: 0}.btn--border-primary[_ngcontent-%COMP%]{--background: transparent;--background-activated: #c4e6d0;--border-color: var(--ion-color-primary);--border-style: solid;--border-width: 2px}.btn--border-secondary[_ngcontent-%COMP%]{--border-width: 2px;--background: transparent;--background-activated: #ddabb2;--border-color: var(--ion-color-secondary);--border-style: solid}.btn--upper[_ngcontent-%COMP%]{text-transform:uppercase}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]{overflow:hidden}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar{width:5px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-track{box-shadow:inset 0 0 5px #546e8d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background:#143c5d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover{background:#143c5d}.bottom-sheet-content[_ngcontent-%COMP%]{background:#fdfdfe;height:100%}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-thumbnail[_ngcontent-%COMP%]{margin-left:calc(31.25 * var(--res));background-color:#1e1e1e;--border-radius: 50%;--size: 23}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:Mont Bold;font-size:calc(45 * var(--res));text-align:center;color:#757474}.bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%], .bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]{padding:calc(75 * var(--res))}.font-size-5[_ngcontent-%COMP%]{font-size:calc(42 * var(--res))}.recap-container[_ngcontent-%COMP%]{padding:calc(41 * var(--res)) 0;max-height:calc(100% - 1.5rem);margin:0 calc(41 * var(--res));background-color:#fff;border-radius:calc(30 * var(--res));overflow-x:hidden;overflow-y:auto;box-shadow:-6px 14px 20px 9px #35363633}.recap-container[_ngcontent-%COMP%]   .fbold[_ngcontent-%COMP%]{font-family:Mont Bold}.recap-container[_ngcontent-%COMP%]   .fMeduim[_ngcontent-%COMP%]{font-family:Mont Light}.recap-container[_ngcontent-%COMP%]   .primary[_ngcontent-%COMP%]{color:#143c5d!important}.recap-container[_ngcontent-%COMP%]   .recap-title[_ngcontent-%COMP%]{margin-bottom:calc(50 * var(--res))}.recap-container[_ngcontent-%COMP%]   .sub-title[_ngcontent-%COMP%]{display:flex;justify-content:space-between}.recap-container[_ngcontent-%COMP%]   .sub-title[_ngcontent-%COMP%]   ion-text[_ngcontent-%COMP%]{font-size:calc(37 * var(--res))!important;margin-bottom:.5rem}.recap-container[_ngcontent-%COMP%]   .totalTtc[_ngcontent-%COMP%]{margin-bottom:calc(37.5 * var(--res))}.recap-container[_ngcontent-%COMP%]   .totalTtc[_ngcontent-%COMP%]   div[_ngcontent-%COMP%]{padding:0 calc(37.5 * var(--res));height:calc(120 * var(--res));display:flex;background-color:#c62f45e0;color:#fff;align-items:center;justify-content:space-between;border-radius:calc(20 * var(--res))}.recap-container[_ngcontent-%COMP%]   .type-truck[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;margin:1em 0 .5em}.recap-container[_ngcontent-%COMP%]   .type-truck[_ngcontent-%COMP%]   ion-toggle[_ngcontent-%COMP%]{color:#419cfb;--background-checked: #419cfb}.recap-container[_ngcontent-%COMP%]   .padding-horizontal[_ngcontent-%COMP%]{padding:0 calc(41 * var(--res))}.recap-container[_ngcontent-%COMP%]   .padding-horizontal[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{display:block}.recap-container[_ngcontent-%COMP%]   .padding-horizontal[_ngcontent-%COMP%]   ion-text[_ngcontent-%COMP%]{font-size:calc(40.7 * var(--res))}.recap-container[_ngcontent-%COMP%]   .discount-details[_ngcontent-%COMP%]{padding-top:.5em;padding-bottom:1em;border-top:1px solid rgba(128,128,128,.448)}.recap-container[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%]{font-family:Mont Bold}.recap-container[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]{overflow-y:auto;margin-bottom:.5em}.recap-container[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   .list-elt[_ngcontent-%COMP%]{gap:.2em;display:flex;align-items:center;align-items:flex-start;margin:0 calc(41 * var(--res));padding:calc(20 * var(--res)) calc(41 * var(--res))}.recap-container[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   .list-elt[_ngcontent-%COMP%]   .col[_ngcontent-%COMP%]{color:#3c597d;text-overflow:ellipsis;overflow:hidden!important;text-wrap:nowrap;font-size:calc(35 * var(--res));font-family:Mont SemiBold}.recap-container[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   .list-elt[_ngcontent-%COMP%]   .col[_ngcontent-%COMP%]:first-child{width:41%}.recap-container[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   .list-elt[_ngcontent-%COMP%]   .col[_ngcontent-%COMP%]:first-child   ion-label[_ngcontent-%COMP%]:first-child{text-overflow:ellipsis;overflow:hidden!important;text-wrap:nowrap;font-family:Mont SemiBold;font-size:calc(35 * var(--res))}.recap-container[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   .list-elt[_ngcontent-%COMP%]   .col[_ngcontent-%COMP%]:nth-child(2){width:23%;min-width:27px}.recap-container[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   .list-elt[_ngcontent-%COMP%]   .col[_ngcontent-%COMP%]:last-child{flex-grow:1;text-align:end}.recap-container[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   .list-elt[_ngcontent-%COMP%]   .col.product[_ngcontent-%COMP%]{gap:5px;flex-wrap:wrap;display:flex;align-items:center;justify-content:flex-start}.recap-container[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   .list-elt[_ngcontent-%COMP%]   .col.product[_ngcontent-%COMP%]   [_ngcontent-%COMP%]:last-child{display:flex;align-items:center;font-size:calc(29.1 * var(--res))}.recap-container[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   .list-elt[_ngcontent-%COMP%]   .col.product[_ngcontent-%COMP%]   [_ngcontent-%COMP%]:first-child{width:100%;text-align:start}@media only screen and (max-width: 530px){.recap-container[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   .list-elt[_ngcontent-%COMP%]   .col.product[_ngcontent-%COMP%]{gap:0}}.recap-container[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   .list-elt[_ngcontent-%COMP%]   .equal[_ngcontent-%COMP%]{width:32%!important}.recap-container[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   .list-elt.line[_ngcontent-%COMP%]{padding:0;margin:0 calc(2 * 41 * var(--res));border-bottom:#ccdef1 solid 1px}.recap-container[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   .list-elt.head[_ngcontent-%COMP%]{background-color:#f1f8ff;border-radius:calc(12 * var(--res))}.recap-container[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   .list-elt.head[_ngcontent-%COMP%]   .col[_ngcontent-%COMP%]{color:#000}.recap-container[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   .list-elt.footer[_ngcontent-%COMP%]{background-color:#f5f6f6;border-radius:calc(12 * var(--res))}.recap-container[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   .list-elt.footer[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{color:#0b305c;font-family:Mont Bold}.recap-container[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   .border[_ngcontent-%COMP%]{margin-top:calc(41 * var(--res))}.recap-container[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   .border[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:space-between;padding-bottom:calc(25 * var(--res))}.recap-container[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   .border[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]:first-child   ion-text[_ngcontent-%COMP%]:first-child{color:#303950}.recap-container[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   .border[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]:first-child   ion-text[_ngcontent-%COMP%]:last-child{color:#103a5a}.recap-container[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   .border[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]:last-child   ion-text[_ngcontent-%COMP%]{color:#419cfb}.recap-container[_ngcontent-%COMP%]   .divider[_ngcontent-%COMP%]{height:1px;border-bottom:#4b4a4a dashed 1px;margin:calc(.7 * 41 * var(--res)) calc(41 * var(--res))}.recap-container[_ngcontent-%COMP%]   .product-container[_ngcontent-%COMP%]{gap:calc(41 * var(--res));display:flex;align-items:center;justify-content:space-around;padding:0 calc(75 * var(--res));margin:calc(.8 * 41 * var(--res)) 0}.recap-container[_ngcontent-%COMP%]   .see-more[_ngcontent-%COMP%]{gap:calc(.3 * 41 * var(--res));color:#419cfb;display:flex;align-items:center;justify-content:center;font-family:Mont Bold}.recap-container[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{font-family:Mont Bold;font-size:calc(45 * var(--res));display:block;color:#303950}.recap-container[_ngcontent-%COMP%]   .payment-container[_ngcontent-%COMP%]   ion-slide[_ngcontent-%COMP%]{height:calc(12.25 * var(--resH));box-shadow:0 3.08499px 10.7975px #00000016;border:.771248px solid rgba(218,218,218,.47);border-radius:3.85624px;--background: #ffffff}.recap-container[_ngcontent-%COMP%]   .payment-container[_ngcontent-%COMP%]   ion-slide[_ngcontent-%COMP%]   .active[_ngcontent-%COMP%]{--background: var(--ion-color-primary)}.recap-container[_ngcontent-%COMP%]   .payment-container[_ngcontent-%COMP%]   ion-slide[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center}.recap-container[_ngcontent-%COMP%]   .cancel-discount[_ngcontent-%COMP%]{display:inline-block;padding:.6em;color:#fff;background-color:var(--ion-color-secondary);border-radius:5px;margin:0 0 1em}.recap-container[_ngcontent-%COMP%]   ion-img[_ngcontent-%COMP%]{width:45%!important}.recap-container[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{--margin: auto}.recap-container[_ngcontent-%COMP%]   .right-block[_ngcontent-%COMP%], .recap-container[_ngcontent-%COMP%]   .left-block[_ngcontent-%COMP%]{width:50%;letter-spacing:1px;text-align:initial}.recap-container[_ngcontent-%COMP%]   .left-block[_ngcontent-%COMP%]{display:flex;align-items:flex-end;flex-direction:column}.recap-container[_ngcontent-%COMP%]   .right-block[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{margin-bottom:.5em;color:#000000de;font-size:59%}.recap-container[_ngcontent-%COMP%]   .left-block[_ngcontent-%COMP%]   .value[_ngcontent-%COMP%]{white-space:nowrap;display:flex;overflow:hidden;color:#000000de;margin-bottom:.5em}.recap-container[_ngcontent-%COMP%]   .fextrabold[_ngcontent-%COMP%]{font-family:Mont Bold;font-weight:700;font-size:calc(40.7 * var(--res))}.recap-container[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{font-family:Mont Bold;display:block;font-size:calc(45 * var(--res));color:#303950}.recap-container--full[_ngcontent-%COMP%]{flex-direction:column;width:100%}.recap-container--full[_ngcontent-%COMP%]   .full-width-input[_ngcontent-%COMP%]{width:100%;padding:calc(41 * var(--res)) 0;--background: #ffffff;--border-color: var(--ion-color-primary);--padding-start: 10px;--padding-end: 10px;margin:0}.recap-container--full[_ngcontent-%COMP%]   .full-width-input[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{color:#000000de;font-size:calc(35 * var(--res));font-weight:var(--mont-semibold)}.recap-container--full[_ngcontent-%COMP%]   .full-width-input[_ngcontent-%COMP%]   ion-input[_ngcontent-%COMP%]{color:#143c5d;font-family:var(--mont-semibold);font-weight:600;font-size:calc(38 * var(--res))}"]})}}return r})()},80153:(R,O,s)=>{s.d(O,{s:()=>n});var n=function(p){return p[p.PENDING=200]="PENDING",p[p.VALIDATED=300]="VALIDATED",p[p.EXPIRED=400]="EXPIRED",p}(n||{})},54357:(R,O,s)=>{s.d(O,{F:()=>n});var n=function(p){return p.CREATE="create_promo_code",p.UPDATE="update_promo_code",p.DELETE="delete_promo_code",p.VIEW="view_promo_code",p}(n||{})},11244:(R,O,s)=>{s.d(O,{F:()=>n});var m=s(2978);let n=(()=>{class p{transform(d){return console.log(),`${d?.slice(0,1)?.toLocaleUpperCase()+d?.slice(1)?.toLocaleLowerCase()}`}static{this.\u0275fac=function(v){return new(v||p)}}static{this.\u0275pipe=m.EJ8({name:"capitalize",type:p,pure:!0})}}return p})()},13217:(R,O,s)=>{s.d(O,{_:()=>T});var m=s(73308),n=s(26409),p=s(45312),_=s(94934),d=s(79801),v=s(80153),y=s(99987),C=s(2978),x=s(33607),$=s(82571),S=s(62049);let T=(()=>{class h{constructor(i,a,e,c){this.baseUrl=i,this.http=a,this.commonSrv=e,this.translateSrv=c,this.ratioOfPointsByVolume=.2,this.POINTS_MATRIX={AMIGO:{5:1,25:5,50:10},COLOMBE:{5:2,25:8,50:15},PELICAN:{5:5,25:12,50:25}},this.base_url=`${this.baseUrl.getOrigin()}${p.c.basePath}`}calculateTotalPointsOrder(i,a){const e=a?.points?.status||d.Th.AMIGO;return i.reduce((c,g)=>{const M=g?.packaging?.unit?.value||0,b=g?.quantity||0;return c+(this.POINTS_MATRIX[e]?.[M]||0)*b},0)}getPoints(i){var a=this;return(0,m.A)(function*(){let e=new n.Nl;const{companyId:c}=i;return c&&(e=e.append("companyId",c)),yield(0,_.s)(a.http.get(`${a.base_url}loyalty-program/points`,{params:e}))})()}getBenefitRewards(i){var a=this;return(0,m.A)(function*(){let e=new n.Nl;return i?.monthly&&(e=e.append("monthly",JSON.stringify(i.monthly))),i?.statusValue&&(e=e.append("statusValue",JSON.stringify(i?.statusValue))),i?.annual&&(e=e.append("annual",JSON.stringify(i.annual))),i?.punctual&&(e=e.append("punctual",JSON.stringify(i.punctual))),yield(0,_.s)(a.http.get(`${a.base_url}advantages`,{params:e}))})()}acceptInvitation(i,a){var e=this;return(0,m.A)(function*(){return yield(0,_.s)(e.http.patch(`${e.base_url}invitations/${i}/accept`,{referredUserId:a}))})()}getInvitations(i){var a=this;return(0,m.A)(function*(){let e=new n.Nl;const{limit:c,status:g,prospectTel:M}=i;return c&&(e=e.append("limit",c)),M&&(e=e.append("prospectTel",M)),g&&(e=e.append("status",g)),yield(0,_.s)(a.http.get(`${a.base_url}invitations`,{params:e}))})()}sendReferralInvitation(i,a){var e=this;return(0,m.A)(function*(){try{const c={referrerId:i,prospectTel:a};return yield(0,_.s)(e.http.post(`${e.base_url}invitations/${i}/invite-referral`,c))}catch(c){const g=e.translateSrv.currentLang===y.T.French?"Impossible d'envoyer l'invitation de parrainage":"Failed to send referral invitation";return yield e.commonSrv.showToast({message:c?.error?.message??e.commonSrv.getError(g,c).message,color:"danger"}),c}})()}getPendingInvitationForUser(i){var a=this;return(0,m.A)(function*(){let e=new n.Nl;return e=e.set("prospectTel",+i),e=e.append("status",v.s.PENDING),yield(0,_.s)(a.http.get(`${a.base_url}invitations/pending-invitation`,{params:e}))})()}getDisplayedPoints(i,a){return"pending"===a?i?.unValidated||0:i?.totalPoints||0}getDisplayedPointsLabel(i){return"pending"===i?"fidelity-page.waiting":"fidelity-page.total-points"}static{this.\u0275fac=function(a){return new(a||h)(C.KVO(x.K),C.KVO(n.Qq),C.KVO($.h),C.KVO(S.E))}}static{this.\u0275prov=C.jDH({token:h,factory:h.\u0275fac,providedIn:"root"})}}return h})()}}]);