<div class="select-account-container bottom-sheet-content">
  <ion-header>
    <ion-toolbar class="ion-text-center ion-padding">
      <ion-thumbnail slot="end" (click)="closeModal()">
        <ion-img src="assets/icons/close.svg"></ion-img>
      </ion-thumbnail>

      <ion-label> {{"signin-page.modal-otp-description.title" | translate}} </ion-label>
    </ion-toolbar>
  </ion-header>

  <ion-content class="container">
    <ion-label> {{"signin-page.modal-otp-description.instruction" | translate}} </ion-label>

    <form [formGroup]="loginForm" class="input-group">
      <ion-item>
        <ion-label position="floating"> {{"signin-page.modal-otp-description.label" | translate}}</ion-label>
        <ion-input type="number" placeholder="Exemple: 6596589" formControlName="code">
        </ion-input>
      </ion-item>
    </form>

    <div class="ion-text-center ion-padding">
      <ion-text>
        <ion-label> {{"signin-page.modal-otp-description.do-not-recieve-the-code" | translate}} </ion-label> <br>
        <span *ngIf="!isLoading && credentialOtp" (click)="resendCode()" class="link"> {{"signin-page.modal-otp-description.resend" | translate}}</span>
        <ion-spinner *ngIf="isLoading" name="bubbles"></ion-spinner>
      </ion-text>
    </div>

    <div class="button-confirm">
      <ion-button type="submit" class="btn btn--meduim" [disabled]="loginForm.invalid || isLoading" color="primary"
        (click)="login()" expand="block">
        <ion-label> {{"signin-page.modal-otp-description.button-confirm" | translate}} </ion-label>
        <ion-spinner *ngIf="isLoading" name="bubbles"></ion-spinner>
      </ion-button>
    </div>

  </ion-content>



