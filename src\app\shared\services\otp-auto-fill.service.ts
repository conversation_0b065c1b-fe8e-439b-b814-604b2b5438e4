import { Injectable } from '@angular/core';
import { Platform } from '@ionic/angular';

declare var window: any;

@Injectable({
  providedIn: 'root'
})
export class OtpAutoFillService {
  private isListening = false;
  private otpCallback: ((otp: string) => void) | null = null;

  constructor(private platform: Platform) {}

  /**
   * Démarre l'écoute automatique des SMS OTP
   * @param callback Fonction appelée quand un OTP est détecté
   */
  async startListening(callback: (otp: string) => void): Promise<void> {
    if (!this.platform.is('android')) {
      console.warn('[OTP] Auto-fill disponible uniquement sur Android');
      return;
    }

    this.otpCallback = callback;
    this.isListening = true;

    try {
      // Méthode 1: Utiliser l'API SMS User Consent si disponible
      if (this.isSmsUserConsentAvailable()) {
        await this.startSmsUserConsent();
      } 
      // Méthode 2: Utiliser l'API WebOTP si disponible
      else if (this.isWebOtpAvailable()) {
        await this.startWebOtp();
      }
      // Méthode 3: Fallback - surveillance du presse-papiers
      else {
        this.startClipboardWatching();
      }
    } catch (error) {
      console.error('[OTP] Erreur lors du démarrage de l\'auto-fill:', error);
    }
  }

  /**
   * Arrête l'écoute automatique
   */
  stopListening(): void {
    this.isListening = false;
    this.otpCallback = null;
  }

  /**
   * Vérifie si l'API SMS User Consent est disponible
   */
  private isSmsUserConsentAvailable(): boolean {
    return 'navigator' in window && 'credentials' in navigator;
  }

  /**
   * Vérifie si l'API WebOTP est disponible
   */
  private isWebOtpAvailable(): boolean {
    return 'OTPCredential' in window;
  }

  /**
   * Utilise l'API SMS User Consent (Android)
   */
  private async startSmsUserConsent(): Promise<void> {
    try {
      console.log('[OTP] Démarrage SMS User Consent...');
      
      if (navigator.credentials) {
        const credential = await navigator.credentials.get({
          otp: { transport: ['sms'] }
        } as any);

        if (credential && this.otpCallback && this.isListening) {
          const otp = this.extractOtp((credential as any).code);
          if (otp) {
            console.log('[OTP] Code détecté via SMS User Consent:', otp);
            this.otpCallback(otp);
          }
        }
      }
    } catch (error) {
      console.error('[OTP] Erreur SMS User Consent:', error);
    }
  }

  /**
   * Utilise l'API WebOTP
   */
  private async startWebOtp(): Promise<void> {
    try {
      console.log('[OTP] Démarrage WebOTP...');
      
      const abortController = new AbortController();
      
      // Arrêter après 5 minutes
      setTimeout(() => abortController.abort(), 5 * 60 * 1000);

      const credential = await navigator.credentials.get({
        otp: { transport: ['sms'] },
        signal: abortController.signal
      } as any);

      if (credential && this.otpCallback && this.isListening) {
        const otp = this.extractOtp((credential as any).code);
        if (otp) {
          console.log('[OTP] Code détecté via WebOTP:', otp);
          this.otpCallback(otp);
        }
      }
    } catch (error) {
      if (error.name !== 'AbortError') {
        console.error('[OTP] Erreur WebOTP:', error);
      }
    }
  }

  /**
   * Surveillance du presse-papiers (fallback)
   */
  private startClipboardWatching(): void {
    console.log('[OTP] Démarrage surveillance presse-papiers...');
    
    let lastClipboard = '';
    
    const checkClipboard = async () => {
      if (!this.isListening) return;
      
      try {
        if (navigator.clipboard && navigator.clipboard.readText) {
          const text = await navigator.clipboard.readText();
          
          if (text !== lastClipboard) {
            lastClipboard = text;
            const otp = this.extractOtp(text);
            
            if (otp && this.otpCallback) {
              console.log('[OTP] Code détecté dans le presse-papiers:', otp);
              this.otpCallback(otp);
            }
          }
        }
      } catch (error) {
        // Ignorer les erreurs de permission du presse-papiers
      }
      
      // Vérifier toutes les 2 secondes
      if (this.isListening) {
        setTimeout(checkClipboard, 2000);
      }
    };
    
    checkClipboard();
  }

  /**
   * Extrait le code OTP d'un texte
   */
  private extractOtp(text: string): string | null {
    if (!text) return null;

    console.log('[OTP] Analyse du texte:', text);

    // Patterns pour détecter les codes OTP
    const patterns = [
      /'(\d{4,8})'/g,                      // '1234'
    ];

    for (const pattern of patterns) {
      const matches = text.match(pattern);
      if (matches) {
        for (const match of matches) {
          const digits = match.replace(/\D/g, '');
          if (digits.length >= 4 && digits.length <= 8) {
            console.log('[OTP] Code OTP extrait:', digits);
            return digits;
          }
        }
      }
    }

    return null;
  }

  /**
   * Vérifie si l'auto-fill est supporté sur la plateforme
   */
  isSupported(): boolean {
    return this.platform.is('android');
  }
}
