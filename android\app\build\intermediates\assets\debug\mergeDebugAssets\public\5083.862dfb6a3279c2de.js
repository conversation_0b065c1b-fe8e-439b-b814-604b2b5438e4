"use strict";(self.webpackChunkapp=self.webpackChunkapp||[]).push([[5083],{74916:(E,y,i)=>{i.d(y,{B:()=>p});var l=i(44912),M=i(9867),h=i(96111);function p(f,c=l.E){return(0,M.N)((_,O)=>{let v=null,S=null,m=null;const a=()=>{if(v){v.unsubscribe(),v=null;const n=S;S=null,O.next(n)}};function g(){const n=m+f,P=c.now();if(P<n)return v=this.schedule(void 0,n-P),void O.add(v);a()}_.subscribe((0,h._)(O,n=>{S=n,m=c.now(),v||(v=c.schedule(g,f),O.add(v))},()=>{a(),O.complete()},void 0,()=>{S=v=null}))})}},26843:(E,y,i)=>{i.d(y,{v:()=>O});var l=i(73308),M=i(35025),h=i.n(M),p=i(2978),f=i(77897),c=i(14599),_=i(74657);let O=(()=>{class v{constructor(m,a,g){this.platform=m,this.storageSrv=a,this.popOver=g,this.isContentShown=!1}ngOnInit(){this.storeLink=this.platform.is("android")?"https://play.google.com/store/apps/details?id=com.clickcadyst.mobile":"https://apps.apple.com/us/app/clic cadyst/id1467838902"}cancel(){var m=this;return(0,l.A)(function*(){yield m.storageSrv.store("lastDeniedUpdateApp",h()().toDate().getTime()),m.popOver.dismiss()})()}static{this.\u0275fac=function(a){return new(a||v)(p.rXU(f.OD),p.rXU(c.n),p.rXU(f.IE))}}static{this.\u0275cmp=p.VBU({type:v,selectors:[["app-update-app-modal"]],decls:18,vars:10,consts:[[1,"dialog-content"],[1,"phone-logo"],[1,"phone"],[1,"text"],[1,"mcm-btn-navigate-container"],[1,"mc-btn","full-width"],[1,"store-link",3,"href"],[1,"mcm-btn-navigate-container-close"],["color","light",1,"mc-btn","full-width","white-update",3,"click"],[1,"store-link"]],template:function(a,g){1&a&&(p.j41(0,"div",0)(1,"div",1),p.nrm(2,"div",2),p.k0s(),p.j41(3,"h1"),p.EFF(4,"CLICK CADYST"),p.k0s(),p.j41(5,"p",3),p.EFF(6),p.nI1(7,"translate"),p.k0s(),p.j41(8,"div",4)(9,"ion-button",5)(10,"a",6),p.EFF(11),p.nI1(12,"translate"),p.k0s()()(),p.j41(13,"div",7)(14,"ion-button",8),p.bIt("click",function(){return g.cancel()}),p.j41(15,"a",9),p.EFF(16),p.nI1(17,"translate"),p.k0s()()()()),2&a&&(p.R7$(6),p.SpI(" ",p.bMT(7,4,"home-page.update-text")," "),p.R7$(4),p.Y8G("href",g.storeLink,p.B4B),p.R7$(1),p.SpI(" ",p.bMT(12,6,"home-page.uptodate"),""),p.R7$(5),p.SpI(" ",p.bMT(17,8,"home-page.update-cancel")," "))},dependencies:[f.Jm,_.D9],styles:["*[_ngcontent-%COMP%]{margin:0;padding:0}.header-md[_ngcontent-%COMP%]:after{background-image:none!important}.modal[_ngcontent-%COMP%]{--border-radius: calc(60 * var(--res))}.input-group[_ngcontent-%COMP%]{margin-top:calc(75 * var(--res))}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{--padding-start: 0;margin-bottom:calc(50 * var(--res));--placeholder-color: $color-six}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-input[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{font-family:Mont Regular;font-weight:400;font-size:calc(42 * var(--res));color:#1e1e1e}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{padding-top:calc(25 * var(--res));padding-bottom:calc(25 * var(--res))}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]{margin-bottom:calc(50 * var(--res));display:flex;align-items:center;justify-content:space-between;border-bottom-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-250, #c8c7cc)));border-bottom-width:.55px;border-bottom-style:solid}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{width:90%;margin-bottom:0;--border-color: transparent;--inner-border-width: 0px 0px 0px 0px;--border-style: none}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-checkbox[_ngcontent-%COMP%]{margin-top:5%}.bg-primary[_ngcontent-%COMP%]{color:#143c5d}.message[_ngcontent-%COMP%]{margin-bottom:calc(75 * var(--res))}.message[_ngcontent-%COMP%]   ion-title[_ngcontent-%COMP%]{font-weight:800;font-size:calc(70 * var(--res));padding-bottom:calc(25 * var(--res))}.message[_ngcontent-%COMP%]   ion-text[_ngcontent-%COMP%]{font-weight:400;color:#757474;display:block;line-height:1.5em!important}.btn--meduim[_ngcontent-%COMP%]{height:calc(130 * var(--res));font-size:calc(50 * var(--res));font-family:var(--mont-bold)}.btn--meduim[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:var(--mont-bold)}.btn--small[_ngcontent-%COMP%]{height:calc(100 * var(--res))}.btn--round[_ngcontent-%COMP%]{--border-radius: 50%;padding-inline-start:0;padding-inline-end:0;--padding-start: 0;--padding-end: 0}.btn--border-primary[_ngcontent-%COMP%]{--background: transparent;--background-activated: #c4e6d0;--border-color: var(--ion-color-primary);--border-style: solid;--border-width: 2px}.btn--border-secondary[_ngcontent-%COMP%]{--border-width: 2px;--background: transparent;--background-activated: #ddabb2;--border-color: var(--ion-color-secondary);--border-style: solid}.btn--upper[_ngcontent-%COMP%]{text-transform:uppercase}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]{overflow:hidden}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar{width:5px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-track{box-shadow:inset 0 0 5px #546e8d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background:#143c5d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover{background:#143c5d}.bottom-sheet-content[_ngcontent-%COMP%]{background:#fdfdfe;height:100%}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-thumbnail[_ngcontent-%COMP%]{margin-left:calc(31.25 * var(--res));background-color:#1e1e1e;--border-radius: 50%;--size: 23}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:Mont Bold;font-size:calc(45 * var(--res));text-align:center;color:#757474}.bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%], .bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]{padding:calc(75 * var(--res))}.font-size-5[_ngcontent-%COMP%]{font-size:calc(42 * var(--res))}.dialog-content[_ngcontent-%COMP%]{padding:16px}.dialog-content[_ngcontent-%COMP%]   .phone-logo[_ngcontent-%COMP%]{height:200px;width:100%;height:22vh;display:flex;align-items:center;justify-content:center}.dialog-content[_ngcontent-%COMP%]   .phone-logo[_ngcontent-%COMP%]   .phone[_ngcontent-%COMP%]{height:100%;width:100%;background-image:url(updatepasta-icon.52e001d874e40d5e.png);background-repeat:no-repeat;background-position:center;background-size:contain}.dialog-content[_ngcontent-%COMP%]   .text[_ngcontent-%COMP%]{font-family:Mont Regular;font-size:.7em;text-align:center;margin:0}.dialog-content[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]{margin:10px 0 25px;font-family:Mont Bold;font-size:12px;text-align:center}.dialog-content[_ngcontent-%COMP%]   .mcm-btn-navigate-container[_ngcontent-%COMP%]{bottom:16px;position:unset;width:100%;margin:25px 0 0}.dialog-content[_ngcontent-%COMP%]   .mcm-btn-navigate-container[_ngcontent-%COMP%]   .mc-btn[_ngcontent-%COMP%]{width:100%;margin-top:4em}.dialog-content[_ngcontent-%COMP%]   .mcm-btn-navigate-container[_ngcontent-%COMP%]   .store-link[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;text-decoration:none;color:#fff;width:100%;height:100%;text-transform:uppercase;font-family:Mont Regular}.dialog-content[_ngcontent-%COMP%]   .mcm-btn-navigate-container-close[_ngcontent-%COMP%]{bottom:16px;position:unset;width:100%;margin:10px 0 0;color:#ffffffbd}.dialog-content[_ngcontent-%COMP%]   .mcm-btn-navigate-container-close[_ngcontent-%COMP%]   .mc-btn[_ngcontent-%COMP%]{width:100%}.dialog-content[_ngcontent-%COMP%]   .mcm-btn-navigate-container-close[_ngcontent-%COMP%]   .store-link[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;text-decoration:none;color:--ion-color-primary;width:100%;height:100%;text-transform:uppercase;font-family:Mont Regular}.dialog-content[_ngcontent-%COMP%]   .mcm-btn-navigate-container-close[_ngcontent-%COMP%]   .store-link.cancel[_ngcontent-%COMP%]{color:#143c5d}"]})}}return v})()},5083:(E,y,i)=>{i.r(y),i.d(y,{NewOrderPageModule:()=>Ce});var l=i(77897),M=i(24608),h=i(56610),p=i(74657),f=i(93887),c=i(37222),_=i(73308),O=i(74916),v=i(73793),S=i(38503),m=i(93527),a=i(99987),g=i(26843),n=i(2978),P=i(77575),b=i(82571),j=i(97130),T=i(62049);function q(r,d){1&r&&n.nrm(0,"ion-checkbox",14),2&r&&n.Y8G("checked",!0)("disabled",!0)}function H(r,d){if(1&r&&(n.j41(0,"div",15)(1,"ion-label"),n.EFF(2),n.k0s()()),2&r){const e=n.XpG().index;n.R7$(2),n.JRh(e+1)}}function Z(r,d){if(1&r&&(n.j41(0,"div",11),n.DNE(1,q,1,2,"ion-checkbox",12),n.DNE(2,H,3,1,"div",13),n.k0s()),2&r){const e=d.$implicit;n.R7$(1),n.Y8G("ngIf",1===e),n.R7$(1),n.Y8G("ngIf",0===e)}}function nn(r,d){1&r&&(n.j41(0,"ion-button",16)(1,"ion-label"),n.EFF(2),n.nI1(3,"translate"),n.k0s()()),2&r&&(n.R7$(2),n.SpI(" ",n.bMT(3,1,"order-new-page.information-label")," "))}function en(r,d){if(1&r){const e=n.RV6();n.j41(0,"ion-header")(1,"ion-toolbar",2)(2,"div",3)(3,"ion-img",4),n.bIt("click",function(){n.eBV(e);const o=n.XpG();return n.Njj(o.back())}),n.k0s(),n.j41(4,"ion-title",5),n.EFF(5),n.k0s()()(),n.j41(6,"ion-toolbar",6)(7,"div",7),n.DNE(8,Z,3,2,"div",8),n.k0s()(),n.j41(9,"div",9),n.DNE(10,nn,4,3,"ion-button",10),n.k0s()()}if(2&r){const e=n.XpG();n.R7$(5),n.JRh(e.stepTitle),n.R7$(3),n.Y8G("ngForOf",e.steps),n.R7$(2),n.Y8G("ngIf","Informations de votre commande"===e.action||"Information of your order"===e.action)}}function tn(r,d){if(1&r){const e=n.RV6();n.j41(0,"ion-header")(1,"ion-toolbar",2)(2,"div",3)(3,"ion-img",4),n.bIt("click",function(){n.eBV(e);const o=n.XpG();return n.Njj(o.back())}),n.k0s(),n.j41(4,"ion-title",5),n.EFF(5),n.nI1(6,"translate"),n.k0s()()()()}2&r&&(n.R7$(5),n.JRh(n.bMT(6,1,"order-new-page.new-order")))}function on(r,d){1&r&&(n.j41(0,"ion-content",17)(1,"div",18)(2,"div",19),n.nrm(3,"ion-img",20),n.k0s(),n.j41(4,"div",21),n.EFF(5),n.nI1(6,"translate"),n.nrm(7,"br"),n.EFF(8),n.nI1(9,"translate"),n.nrm(10,"br"),n.EFF(11),n.nI1(12,"translate"),n.k0s()()()),2&r&&(n.R7$(5),n.SpI(" ",n.bMT(6,3,"order-new-page.maintenance.first")," "),n.R7$(3),n.SpI("",n.bMT(9,5,"order-new-page.maintenance.second")," "),n.R7$(3),n.SpI(" ",n.bMT(12,7,"order-new-page.maintenance.third")," "))}function rn(r,d){if(1&r){const e=n.RV6();n.j41(0,"ion-content")(1,"ion-router-outlet",22),n.bIt("activate",function(o){n.eBV(e);const s=n.XpG();return n.Njj(s.onOutletLoaded(o))}),n.k0s()()}}let an=(()=>{class r{constructor(e,t,o,s,C,x,F){this.router=e,this.location=t,this.route=o,this.commonSrv=s,this.popOver=C,this.versionService=x,this.translateService=F,this.steps=[0,0,0],this.action="Nouvelle commande",this.stepTitle="",this.lastRoute="",this.astualStep="",this.isDisableOrderProcess=!1,this.formatter=k=>k.label+(k._id?" ( "+k.description+" )":""),this.search=k=>k.pipe((0,O.B)(200),(0,v.F)(),(0,S.p)(I=>I.length>=2),(0,m.T)(I=>this.products.filter(D=>new RegExp(I,"mi").test(D?.label+" "+D?.description)).slice(0,10)))}ngOnInit(){var e=this;return(0,_.A)(function*(){const{isDisableOrderProcess:t}=yield e.commonSrv.getOrderProcessState();e.isDisableOrderProcess=t})()}getProducts(){}onOutletLoaded(e){this.route.params.subscribe(t=>{this.action=this.translateService?.currentLang===a.T.French?"Nouvelle commande":"New order";let o={"/order/new/first-step":{step:0,url:"/navigation/home",title:this.translateService?.currentLang===a.T.French?"Nouvelle commande":"New Order",label:this.translateService?.currentLang===a.T.French?"Informations g\xe9n\xe9rales":"general information",description:"",steps:[0,0,0]},"/order/new/second-step":{step:1,title:this.translateService?.currentLang===a.T.French?"S\xe9lection des produits":"Product selection",label:this.translateService?.currentLang===a.T.French?"Rechercher":"search",url:"first-step",description:this.translateService?.currentLang===a.T.French?"S\xe9lectionner un produit pour l\u2019ajouter au panier ":"Select a product to add to cart",steps:[1,0,0]},"/order/new/third-step":{step:2,title:this.translateService?.currentLang===a.T.French?"Finaliser la commande":"Finalise order",url:"first-step",label:this.translateService?.currentLang===a.T.French?"Informations de votre commande":"Information of your order",description:"",steps:[1,1,0]},"/order/new/four-step":{step:3,title:this.translateService?.currentLang===a.T.French?"Op\xe9ration r\xe9ussie":"Successful Operation",url:"third-step",label:"",description:"",steps:[1,1,1]}};this.stepTitle=o[this.router.url].title,this.steps=o[this.router.url].steps,this.action=o[this.router.url].label,this.lastRoute=o[this.router.url].url})}back(){if("/order/new/four-step"===this.router.url)return this.router.navigate(["navigation/home"]);this.location.back(),this.commonSrv.tab="",this.commonSrv.tab="navigation/home"}verifyAppUpToDate(){var e=this;return(0,_.A)(function*(){if(!e.isAppMaintained&&!(yield e.versionService.isUpToDate())){e.isAppMaintained=!0;const o=yield e.popOver.create({component:g.v,cssClass:"updateModal"});o.present(),yield o.onWillDismiss(),e.isAppMaintained=!1}})()}static{this.\u0275fac=function(t){return new(t||r)(n.rXU(P.Ix),n.rXU(h.aZ),n.rXU(P.nX),n.rXU(b.h),n.rXU(l.IE),n.rXU(j.I),n.rXU(T.E))}}static{this.\u0275cmp=n.VBU({type:r,selectors:[["app-new-order"]],decls:4,vars:4,consts:[[4,"ngIf"],["class","ion-padding","fullscreen","true",4,"ngIf"],[1,"header"],["slot","start",1,"header-title"],["slot","start","src","/assets/icons/arrow-blue.svg",1,"ion-arrow",3,"click"],[1,"title"],[1,"step-container"],[1,"step-content"],["class","step-item",4,"ngFor","ngForOf"],[1,"space-h-v"],["class","btn btn--meduim","color","primary","expand","block",4,"ngIf"],[1,"step-item"],["mode","ios",3,"checked","disabled",4,"ngIf"],["class","checkbox",4,"ngIf"],["mode","ios",3,"checked","disabled"],[1,"checkbox"],["color","primary","expand","block",1,"btn","btn--meduim"],["fullscreen","true",1,"ion-padding"],[1,"no-order-process"],[1,"icon"],["src","assets/images/Phone-maintenance.png",1,"ion-text-center"],[1,"text"],[3,"activate"]],template:function(t,o){1&t&&(n.DNE(0,en,11,3,"ion-header",0),n.DNE(1,tn,7,3,"ion-header",0),n.DNE(2,on,13,9,"ion-content",1),n.DNE(3,rn,2,0,"ion-content",0)),2&t&&(n.Y8G("ngIf",!o.isDisableOrderProcess),n.R7$(1),n.Y8G("ngIf",o.isDisableOrderProcess),n.R7$(1),n.Y8G("ngIf",o.isDisableOrderProcess),n.R7$(1),n.Y8G("ngIf",!o.isDisableOrderProcess))},dependencies:[l.Jm,l.eY,l.W9,l.eU,l.KW,l.he,l.BC,l.ai,l.hB,l.Rg,h.Sq,h.bT,p.D9],styles:["*[_ngcontent-%COMP%]{margin:0;padding:0}.header-md[_ngcontent-%COMP%]:after{background-image:none!important}.modal[_ngcontent-%COMP%]{--border-radius: calc(60 * var(--res))}.input-group[_ngcontent-%COMP%]{margin-top:calc(75 * var(--res))}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{--padding-start: 0;margin-bottom:calc(50 * var(--res));--placeholder-color: $color-six}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-input[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{font-family:Mont Regular;font-weight:400;font-size:calc(42 * var(--res));color:#1e1e1e}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{padding-top:calc(25 * var(--res));padding-bottom:calc(25 * var(--res))}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]{margin-bottom:calc(50 * var(--res));display:flex;align-items:center;justify-content:space-between;border-bottom-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-250, #c8c7cc)));border-bottom-width:.55px;border-bottom-style:solid}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{width:90%;margin-bottom:0;--border-color: transparent;--inner-border-width: 0px 0px 0px 0px;--border-style: none}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-checkbox[_ngcontent-%COMP%]{margin-top:5%}.bg-primary[_ngcontent-%COMP%]{color:#143c5d}.message[_ngcontent-%COMP%]{margin-bottom:calc(75 * var(--res))}.message[_ngcontent-%COMP%]   ion-title[_ngcontent-%COMP%]{font-weight:800;font-size:calc(70 * var(--res));padding-bottom:calc(25 * var(--res))}.message[_ngcontent-%COMP%]   ion-text[_ngcontent-%COMP%]{font-weight:400;color:#757474;display:block;line-height:1.5em!important}.btn--meduim[_ngcontent-%COMP%]{height:calc(130 * var(--res));font-size:calc(50 * var(--res));font-family:var(--mont-bold)}.btn--meduim[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:var(--mont-bold)}.btn--small[_ngcontent-%COMP%]{height:calc(100 * var(--res))}.btn--round[_ngcontent-%COMP%]{--border-radius: 50%;padding-inline-start:0;padding-inline-end:0;--padding-start: 0;--padding-end: 0}.btn--border-primary[_ngcontent-%COMP%]{--background: transparent;--background-activated: #c4e6d0;--border-color: var(--ion-color-primary);--border-style: solid;--border-width: 2px}.btn--border-secondary[_ngcontent-%COMP%]{--border-width: 2px;--background: transparent;--background-activated: #ddabb2;--border-color: var(--ion-color-secondary);--border-style: solid}.btn--upper[_ngcontent-%COMP%]{text-transform:uppercase}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]{overflow:hidden}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar{width:5px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-track{box-shadow:inset 0 0 5px #546e8d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background:#143c5d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover{background:#143c5d}.bottom-sheet-content[_ngcontent-%COMP%]{background:#fdfdfe;height:100%}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-thumbnail[_ngcontent-%COMP%]{margin-left:calc(31.25 * var(--res));background-color:#1e1e1e;--border-radius: 50%;--size: 23}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:Mont Bold;font-size:calc(45 * var(--res));text-align:center;color:#757474}.bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%], .bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]{padding:calc(75 * var(--res))}.font-size-5[_ngcontent-%COMP%]{font-size:calc(42 * var(--res))}ion-header[_ngcontent-%COMP%]{background:#f4f4f4}ion-header[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%], ion-header[_ngcontent-%COMP%]   .subtitle[_ngcontent-%COMP%]{font-family:Mont Regular}ion-header[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{color:#0b305c}ion-header[_ngcontent-%COMP%]   .subtitle[_ngcontent-%COMP%]{color:#000;font-size:calc(45 * var(--res))}ion-header[_ngcontent-%COMP%]   .space-h-v[_ngcontent-%COMP%]{margin:calc(41 * var(--res)) 0;padding:0 calc(41 * var(--res))}ion-header[_ngcontent-%COMP%]   .space-h-v[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%]::part(native){color:var(--clr-primary-700);background:var(--clr-primary-0)}ion-header[_ngcontent-%COMP%]   .space-h-v[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:var(--mont-bold)}ion-header[_ngcontent-%COMP%]   .searchbar[_ngcontent-%COMP%]{--background: transparent;height:calc(5 * var(--resH));border:none;--box-shadow: none;border-bottom:1px solid #1e1e1e}ion-header[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]{text-transform:initial}ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]{--border-color: transparent;padding:calc(41 * var(--res)) calc(41 * var(--res)) 0 calc(41 * var(--res));--background: #f4f4f4}ion-header[_ngcontent-%COMP%]   .step-container[_ngcontent-%COMP%]{padding-top:0;padding-bottom:0;border-bottom-left-radius:calc(50 * var(--res));border-bottom-right-radius:calc(50 * var(--res))}ion-header[_ngcontent-%COMP%]   .step-container[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]{display:flex;justify-content:space-evenly;width:50%;margin-left:auto;margin-right:auto}ion-header[_ngcontent-%COMP%]   .step-container[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .step-item[_ngcontent-%COMP%]{display:flex;flex-direction:column;justify-content:space-between}ion-header[_ngcontent-%COMP%]   .step-container[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .step-item[_ngcontent-%COMP%]   ion-checkbox[_ngcontent-%COMP%]{opacity:1!important;font-family:Mont Bold;--background-checked: transparent;--checkmark-color: var(--ion-color-primary);--border-width: 3px;--checkmark-width: 3px}ion-header[_ngcontent-%COMP%]   .step-container[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .step-item[_ngcontent-%COMP%]   .checkbox[_ngcontent-%COMP%], ion-header[_ngcontent-%COMP%]   .step-container[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .step-item[_ngcontent-%COMP%]   ion-checkbox[_ngcontent-%COMP%]{width:30px;height:30px}ion-header[_ngcontent-%COMP%]   .step-container[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .step-item[_ngcontent-%COMP%]   .checkbox[_ngcontent-%COMP%]{background:transparent;border:3px solid #6d839d;border-radius:50%;display:flex;align-items:center;justify-content:center;color:#8597ad;line-height:normal}ion-header[_ngcontent-%COMP%]   .step-container[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .step-item[_ngcontent-%COMP%]   .checkbox[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:Mont SemiBold;font-size:16px}ion-header[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]{display:flex;align-items:center}ion-header[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .header-title[_ngcontent-%COMP%]{display:flex;align-items:center}ion-header[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .header-title[_ngcontent-%COMP%]   ion-img[_ngcontent-%COMP%]{width:calc(50 * var(--res));margin-right:calc(25 * var(--res))}ion-header[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .header-title[_ngcontent-%COMP%]   .ion-arrow[_ngcontent-%COMP%]{height:20px;width:25px}ion-header[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .header-title[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{font-size:calc(46 * var(--res));text-align:start;color:#0b305c}ion-header[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .icons_profil[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:flex-end;gap:calc(31 * var(--res));width:100px}ion-header[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .icons_profil[_ngcontent-%COMP%]   ion-img[_ngcontent-%COMP%]{width:calc(60 * var(--res))}ion-header[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .icons_profil[_ngcontent-%COMP%]   .notification[_ngcontent-%COMP%]{text-align:right;position:relative}ion-header[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .icons_profil[_ngcontent-%COMP%]   .notification[_ngcontent-%COMP%]   .badge[_ngcontent-%COMP%]{position:absolute;right:18%;width:calc(20 * var(--res));height:calc(20 * var(--res));border-radius:50%;background:rgb(173,5,5)}ion-content[_ngcontent-%COMP%]{--background: #f4f4f4}.no-order-process[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;color:#3c597d;text-align:center;margin-top:25%}.no-order-process[_ngcontent-%COMP%]   .icon[_ngcontent-%COMP%]{height:10em;width:10em}.no-order-process[_ngcontent-%COMP%]   .icon[_ngcontent-%COMP%]   ion-img[_ngcontent-%COMP%]{height:100%;width:100%}.no-order-process[_ngcontent-%COMP%]   .text[_ngcontent-%COMP%]{line-height:1.7em;font-family:Mont Bold;font-size:var(--fs-15-px)}"]})}}return r})();var G=i(81559),$=i(14599);let cn=(()=>{class r{constructor(e,t,o){this.router=e,this.orderService=t,this.storageService=o}ngOnInit(){}goToUrl(){this.storageService.remove("cart"),this.storageService.remove("removals"),this.storageService.getUserConnected(),this.router.navigateByUrl("navigation/home")}static{this.\u0275fac=function(t){return new(t||r)(n.rXU(P.Ix),n.rXU(G.Q),n.rXU($.n))}}static{this.\u0275cmp=n.VBU({type:r,selectors:[["app-four-step"]],decls:16,vars:10,consts:[["id","container"],[1,"illustration"],["src","assets/icons/Logo.svg"],[1,"msg-container"],[1,"text-response","detail"],[1,"btn-validate"],["color","primary","expand","block",1,"btn--meduim","btn--upper",3,"click"]],template:function(t,o){1&t&&(n.j41(0,"section",0)(1,"div",1),n.nrm(2,"ion-img",2),n.j41(3,"div",3),n.EFF(4),n.nI1(5,"translate"),n.nrm(6,"br"),n.EFF(7),n.nI1(8,"translate"),n.j41(9,"div",4),n.EFF(10),n.k0s()()(),n.j41(11,"div",5)(12,"ion-button",6),n.bIt("click",function(){return o.goToUrl()}),n.j41(13,"ion-label"),n.EFF(14),n.nI1(15,"translate"),n.k0s()()()()),2&t&&(n.R7$(4),n.SpI(" ",n.bMT(5,4,"order-new-page.last-step.thanks")," "),n.R7$(3),n.SpI(" ",n.bMT(8,6,"order-new-page.last-step.for-your-orders")," "),n.R7$(3),n.JRh(null==o.orderService||null==o.orderService.response?null:o.orderService.response.message),n.R7$(4),n.SpI(" ",n.bMT(15,8,"order-new-page.last-step.back-button-label")," "))},dependencies:[l.Jm,l.KW,l.he,p.D9],styles:["*[_ngcontent-%COMP%]{margin:0;padding:0}.header-md[_ngcontent-%COMP%]:after{background-image:none!important}.modal[_ngcontent-%COMP%]{--border-radius: calc(60 * var(--res))}.input-group[_ngcontent-%COMP%]{margin-top:calc(75 * var(--res))}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{--padding-start: 0;margin-bottom:calc(50 * var(--res));--placeholder-color: $color-six}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-input[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{font-family:Mont Regular;font-weight:400;font-size:calc(42 * var(--res));color:#1e1e1e}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{padding-top:calc(25 * var(--res));padding-bottom:calc(25 * var(--res))}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]{margin-bottom:calc(50 * var(--res));display:flex;align-items:center;justify-content:space-between;border-bottom-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-250, #c8c7cc)));border-bottom-width:.55px;border-bottom-style:solid}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{width:90%;margin-bottom:0;--border-color: transparent;--inner-border-width: 0px 0px 0px 0px;--border-style: none}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-checkbox[_ngcontent-%COMP%]{margin-top:5%}.bg-primary[_ngcontent-%COMP%]{color:#143c5d}.message[_ngcontent-%COMP%]{margin-bottom:calc(75 * var(--res))}.message[_ngcontent-%COMP%]   ion-title[_ngcontent-%COMP%]{font-weight:800;font-size:calc(70 * var(--res));padding-bottom:calc(25 * var(--res))}.message[_ngcontent-%COMP%]   ion-text[_ngcontent-%COMP%]{font-weight:400;color:#757474;display:block;line-height:1.5em!important}.btn--meduim[_ngcontent-%COMP%]{height:calc(130 * var(--res));font-size:calc(50 * var(--res));font-family:var(--mont-bold)}.btn--meduim[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:var(--mont-bold)}.btn--small[_ngcontent-%COMP%]{height:calc(100 * var(--res))}.btn--round[_ngcontent-%COMP%]{--border-radius: 50%;padding-inline-start:0;padding-inline-end:0;--padding-start: 0;--padding-end: 0}.btn--border-primary[_ngcontent-%COMP%]{--background: transparent;--background-activated: #c4e6d0;--border-color: var(--ion-color-primary);--border-style: solid;--border-width: 2px}.btn--border-secondary[_ngcontent-%COMP%]{--border-width: 2px;--background: transparent;--background-activated: #ddabb2;--border-color: var(--ion-color-secondary);--border-style: solid}.btn--upper[_ngcontent-%COMP%]{text-transform:uppercase}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]{overflow:hidden}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar{width:5px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-track{box-shadow:inset 0 0 5px #546e8d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background:#143c5d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover{background:#143c5d}.bottom-sheet-content[_ngcontent-%COMP%]{background:#fdfdfe;height:100%}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-thumbnail[_ngcontent-%COMP%]{margin-left:calc(31.25 * var(--res));background-color:#1e1e1e;--border-radius: 50%;--size: 23}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:Mont Bold;font-size:calc(45 * var(--res));text-align:center;color:#757474}.bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%], .bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]{padding:calc(75 * var(--res))}.font-size-5[_ngcontent-%COMP%]{font-size:calc(42 * var(--res))}#container[_ngcontent-%COMP%]{display:flex;flex-direction:column;padding:calc(75 * var(--res));height:100%}#container[_ngcontent-%COMP%]   ion-img[_ngcontent-%COMP%]{margin:calc(1 * var(--res)) 20%;width:calc(60 * var(--resW))}#container[_ngcontent-%COMP%]   .msg-container[_ngcontent-%COMP%]{text-align:center;font-family:Mont Bold;color:#3c597d;font-size:var(--fs-20-px)}#container[_ngcontent-%COMP%]   .msg-container[_ngcontent-%COMP%]   .detail[_ngcontent-%COMP%]{font-family:Mont Regular;padding-top:calc(.5 * 41 * var(--res));color:#0b305c}#container[_ngcontent-%COMP%]   .btn-validate[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:Mont Bold}#container[_ngcontent-%COMP%]   .illustration[_ngcontent-%COMP%]{flex:1;display:flex;align-items:center;justify-content:space-around;flex-direction:column}#container[_ngcontent-%COMP%]   .text-response[_ngcontent-%COMP%]{text-align:center;font-size:var(--fs-14-px);font-weight:700;margin-top:1em}"]})}}return r})();var A=i(62897),u=i(58133),sn=i(68953),ln=i(35025),B=i.n(ln),dn=i(93387),V=i(39316),z=i(43556),gn=i(94934),N=i(26409),pn=i(45312),mn=i(33607);let un=(()=>{class r{constructor(e,t,o){this.baseUrl=e,this.commonSrv=t,this.http=o,this.base_url=`${this.baseUrl.getOrigin()}${pn.c.basePath}locations`}getParticularLocation(e){var t=this;return(0,_.A)(function*(){try{const o=_n(e);return yield(0,gn.s)(t.http.get(t.base_url,{params:o}))}catch(o){const C={message:t.commonSrv.getError("",o).message,color:"danger"};return yield t.commonSrv.showToast(C),o}})()}static{this.\u0275fac=function(t){return new(t||r)(n.KVO(mn.K),n.KVO(b.h),n.KVO(N.Qq))}}static{this.\u0275prov=n.jDH({token:r,factory:r.\u0275fac,providedIn:"root"})}}return r})();const _n=r=>{let d=new N.Nl;const{userType:e,region:t}=r;return["null",null,void 0,"undefined"].includes(e)||(d=d.append("category",e)),t&&(d=d.append("region",t)),d};var hn=i(61955),vn=i(23985),L=i(71333),X=i(11244);function Cn(r,d){1&r&&n.nrm(0,"app-progress-spinner")}function Pn(r,d){if(1&r&&(n.j41(0,"div",7)(1,"div"),n.EFF(2),n.nI1(3,"translate"),n.j41(4,"span",8),n.EFF(5),n.k0s()(),n.j41(6,"div"),n.EFF(7),n.nI1(8,"translate"),n.j41(9,"span",8),n.EFF(10),n.k0s()()()),2&r){const e=n.XpG();n.R7$(2),n.SpI("",n.bMT(3,4,"order-new-page.first-step.capacityPerYear"),": "),n.R7$(3),n.SpI(" ",null==e.commonService||null==e.commonService.user||null==e.commonService.user.tonnage?null:e.commonService.user.tonnage.capacityPerYear," T"),n.R7$(2),n.SpI("",n.bMT(8,6,"order-new-page.first-step.capacityLeft"),": "),n.R7$(3),n.SpI(" ",null==e.commonService||null==e.commonService.user||null==e.commonService.user.tonnage?null:e.commonService.user.tonnage.capacity," T ")}}function Mn(r,d){if(1&r&&(n.j41(0,"ion-select-option",12),n.EFF(1),n.k0s()),2&r){const e=d.$implicit;n.Y8G("value",e),n.R7$(1),n.SpI(" ",e.name," ")}}function fn(r,d){if(1&r){const e=n.RV6();n.j41(0,"ion-item")(1,"ion-label",9),n.EFF(2),n.nI1(3,"translate"),n.k0s(),n.j41(4,"ion-select",10),n.bIt("ionChange",function(o){n.eBV(e);const s=n.XpG();return n.Njj(s.onCompanySelect(o))}),n.nI1(5,"translate"),n.DNE(6,Mn,2,2,"ion-select-option",11),n.k0s()()}if(2&r){const e=n.XpG();n.R7$(2),n.JRh(n.bMT(3,3,"order-new-page.first-step.select-company")),n.R7$(2),n.FS9("cancelText",n.bMT(5,5,"button.cancel")),n.R7$(2),n.Y8G("ngForOf",e.companies)}}function On(r,d){if(1&r&&(n.j41(0,"ion-select-option",12),n.EFF(1),n.nI1(2,"capitalize"),n.k0s()),2&r){const e=d.$implicit;n.Y8G("value",e),n.R7$(1),n.SpI(" ",n.bMT(2,2,null==e?null:e.label)," ")}}function bn(r,d){if(1&r){const e=n.RV6();n.j41(0,"ion-item")(1,"ion-label",9),n.EFF(2),n.nI1(3,"capitalize"),n.nI1(4,"translate"),n.k0s(),n.j41(5,"ion-select",13),n.bIt("ionChange",function(o){n.eBV(e);const s=n.XpG();return n.Njj(s.handlePackaging(o))}),n.nI1(6,"capitalize"),n.nI1(7,"translate"),n.DNE(8,On,3,4,"ion-select-option",14),n.k0s()()}if(2&r){const e=n.XpG();n.R7$(2),n.JRh(n.bMT(3,5,n.bMT(4,7,"order-new-page.first-step.title"))),n.R7$(3),n.FS9("cancelText",n.bMT(6,9,n.bMT(7,11,"button.cancel"))),n.Y8G("disabled",(null==e.commonService||null==e.commonService.user?null:e.commonService.user.category)===e.UserCategory.Particular),n.R7$(3),n.Y8G("ngForOf",e.initData)("ngForTrackBy",e.trackByFn)}}function yn(r,d){if(1&r&&(n.j41(0,"ion-select-option",12),n.EFF(1),n.k0s()),2&r){const e=d.$implicit;n.Y8G("value",e),n.R7$(1),n.SpI(" ",e," ")}}function Sn(r,d){if(1&r){const e=n.RV6();n.j41(0,"ion-item")(1,"ion-label",9),n.EFF(2),n.nI1(3,"translate"),n.k0s(),n.j41(4,"ion-input",26),n.bIt("ionInput",function(o){n.eBV(e);const s=n.XpG(3);return n.Njj(s.onCategoryChange(o))}),n.k0s()()}if(2&r){const e=n.XpG(3);n.R7$(2),n.JRh(n.bMT(3,2,"order-new-page.first-step.category")),n.R7$(2),n.Y8G("value",e.otherCategoryValue)}}function Fn(r,d){if(1&r){const e=n.RV6();n.j41(0,"div",19)(1,"ion-item")(2,"ion-label",9),n.EFF(3),n.nI1(4,"translate"),n.k0s(),n.nrm(5,"ion-input",20),n.k0s(),n.j41(6,"ion-item")(7,"ion-label",9),n.EFF(8),n.nI1(9,"translate"),n.k0s(),n.nrm(10,"ion-input",21),n.k0s(),n.j41(11,"ion-item")(12,"ion-label",9),n.EFF(13),n.nI1(14,"translate"),n.k0s(),n.nrm(15,"ion-input",22),n.k0s(),n.j41(16,"ion-item")(17,"ion-label",9),n.EFF(18),n.nI1(19,"translate"),n.k0s(),n.nrm(20,"ion-input",23),n.k0s(),n.j41(21,"ion-item")(22,"ion-label",9),n.EFF(23),n.nI1(24,"translate"),n.k0s(),n.j41(25,"ion-select",24),n.bIt("ionChange",function(o){n.eBV(e);const s=n.XpG(2);return n.Njj(s.onCategoryChange(o))}),n.DNE(26,yn,2,2,"ion-select-option",11),n.k0s()(),n.DNE(27,Sn,5,4,"ion-item",0),n.j41(28,"ion-item")(29,"ion-label",9),n.EFF(30),n.nI1(31,"translate"),n.k0s(),n.nrm(32,"ion-input",25),n.k0s()()}if(2&r){const e=n.XpG(2);n.R7$(3),n.JRh(n.bMT(4,8,"order-new-page.first-step.driver-vehicle")),n.R7$(5),n.JRh(n.bMT(9,10,"order-new-page.first-step.driver-name")),n.R7$(5),n.JRh(n.bMT(14,12,"order-new-page.first-step.input-phone-driver")),n.R7$(5),n.JRh(n.bMT(19,14,"order-new-page.first-step.driver-license")),n.R7$(5),n.JRh(n.bMT(24,16,"order-new-page.first-step.driver-category")),n.R7$(3),n.Y8G("ngForOf",e.truckCategories),n.R7$(1),n.Y8G("ngIf",e.isOtherSelected),n.R7$(3),n.JRh(n.bMT(31,18,"order-new-page.first-step.driver-id"))}}function kn(r,d){1&r&&(n.j41(0,"ion-item")(1,"ion-label",9),n.EFF(2),n.nI1(3,"translate"),n.k0s(),n.nrm(4,"ion-input",27),n.k0s()),2&r&&(n.R7$(2),n.JRh(n.bMT(3,1,"order-new-page.first-step.delivery-location")))}function Tn(r,d){if(1&r&&(n.j41(0,"ion-select-option",12),n.EFF(1),n.k0s()),2&r){const e=d.$implicit;n.FS9("value",e.region),n.R7$(1),n.SpI("",null==e?null:e.region," ")}}function xn(r,d){if(1&r&&(n.j41(0,"ion-select-option",12),n.EFF(1),n.k0s()),2&r){const e=d.$implicit;n.FS9("value",(null==e?null:e.name)||e),n.R7$(1),n.SpI("",(null==e?null:e.name)||e," ")}}function wn(r,d){if(1&r&&(n.j41(0,"ion-select-option",12),n.EFF(1),n.k0s()),2&r){const e=d.$implicit;n.Y8G("value",e),n.R7$(1),n.SpI("",e.name," ")}}function In(r,d){if(1&r){const e=n.RV6();n.j41(0,"ion-datetime",35,36),n.bIt("ionChange",function(){n.eBV(e);const o=n.sdS(1);return n.Njj(o.confirm(!0))}),n.k0s()}}function Rn(r,d){if(1&r){const e=n.RV6();n.j41(0,"div")(1,"div",29)(2,"ion-item")(3,"ion-label",9),n.EFF(4),n.nI1(5,"translate"),n.k0s(),n.j41(6,"ion-select",30),n.bIt("ionChange",function(){n.eBV(e);const o=n.XpG(3);return o.checkValidator(),n.Njj(o.getCities())}),n.nI1(7,"translate"),n.DNE(8,Tn,2,2,"ion-select-option",11),n.k0s()(),n.j41(9,"ion-item")(10,"ion-label",9),n.EFF(11),n.nI1(12,"translate"),n.k0s(),n.j41(13,"ion-select",31),n.bIt("ionChange",function(){n.eBV(e);const o=n.XpG(3);let s;return o.checkValidator(),n.Njj(o.getCompanies(null==o.orderForm||null==(s=o.orderForm.get("address.city"))?null:s.value))}),n.nI1(14,"translate"),n.DNE(15,xn,2,2,"ion-select-option",11),n.k0s()()(),n.j41(16,"ion-item")(17,"ion-label",9),n.EFF(18),n.nI1(19,"translate"),n.k0s(),n.j41(20,"ion-select",32),n.nI1(21,"translate"),n.DNE(22,wn,2,2,"ion-select-option",14),n.k0s()(),n.j41(23,"ion-item")(24,"ion-label",9),n.EFF(25),n.nI1(26,"translate"),n.k0s(),n.nrm(27,"ion-input",33),n.nI1(28,"date"),n.j41(29,"ion-popover",34),n.DNE(30,In,2,0,"ng-template"),n.k0s()()()}if(2&r){const e=n.XpG(3);let t;n.R7$(4),n.SpI(" ",n.bMT(5,12,"order-new-page.first-step.select-region")," "),n.R7$(2),n.FS9("cancelText",n.bMT(7,14,"button.cancel")),n.R7$(2),n.Y8G("ngForOf",e.employeeLocations),n.R7$(3),n.SpI(" ",n.bMT(12,16,"order-new-page.first-step.select-city")," "),n.R7$(2),n.FS9("cancelText",n.bMT(14,18,"button.cancel")),n.R7$(2),n.Y8G("ngForOf",e.cities),n.R7$(3),n.SpI(" ",n.bMT(19,20,"order-new-page.first-step.select-distributor-label")," "),n.R7$(2),n.FS9("cancelText",n.bMT(21,22,"button.cancel")),n.R7$(2),n.Y8G("ngForOf",e.companies)("ngForTrackBy",e.trackByFn),n.R7$(3),n.JRh(n.bMT(26,24,"order-new-page.first-step.delivery-date")),n.R7$(2),n.FS9("value",n.i5U(28,26,null==e.orderForm||null==(t=e.orderForm.get("deliveryDate"))?null:t.value,"dd/MM/yyyy"))}}function Dn(r,d){if(1&r&&(n.j41(0,"ion-select-option",12),n.EFF(1),n.k0s()),2&r){const e=d.$implicit;n.Y8G("value",e),n.R7$(1),n.SpI("",e.label," ")}}function En(r,d){if(1&r&&(n.j41(0,"div",29)(1,"ion-item")(2,"ion-label",9),n.EFF(3),n.nI1(4,"translate"),n.k0s(),n.j41(5,"ion-select",37),n.nI1(6,"translate"),n.DNE(7,Dn,2,2,"ion-select-option",14),n.k0s()()()),2&r){const e=n.XpG(3);n.R7$(3),n.JRh(n.bMT(4,4,"order-new-page.first-step.select-city-label")),n.R7$(2),n.FS9("cancelText",n.bMT(6,6,"button.cancel")),n.R7$(2),n.Y8G("ngForOf",e.address)("ngForTrackBy",e.trackByFn)}}function jn(r,d){1&r&&(n.j41(0,"ion-item")(1,"ion-label",9),n.EFF(2),n.nI1(3,"translate"),n.k0s(),n.nrm(4,"ion-input",38),n.k0s()),2&r&&(n.R7$(2),n.JRh(n.bMT(3,1,"order-new-page.first-step.input-location-label")))}function $n(r,d){1&r&&(n.j41(0,"ion-item")(1,"ion-label",9),n.EFF(2),n.nI1(3,"translate"),n.k0s(),n.nrm(4,"ion-input",39),n.k0s()),2&r&&(n.R7$(2),n.JRh(n.bMT(3,1,"order-new-page.first-step.input-phone-label")))}function An(r,d){if(1&r&&(n.j41(0,"div"),n.DNE(1,Rn,31,29,"div",0),n.DNE(2,En,8,8,"div",28),n.DNE(3,jn,5,3,"ion-item",0),n.DNE(4,$n,5,3,"ion-item",0),n.k0s()),2&r){const e=n.XpG(2);n.R7$(1),n.Y8G("ngIf",!e.choiceDelivery),n.R7$(1),n.Y8G("ngIf",e.choiceDelivery),n.R7$(1),n.Y8G("ngIf",e.choiceDelivery),n.R7$(1),n.Y8G("ngIf",e.choiceDelivery)}}function Vn(r,d){if(1&r){const e=n.RV6();n.j41(0,"ion-item-group")(1,"ion-label",15),n.EFF(2),n.nI1(3,"translate"),n.k0s(),n.j41(4,"div",40)(5,"div",16)(6,"ion-toggle",17),n.bIt("ionChange",function(o){n.eBV(e);const s=n.XpG(3);return n.Njj(s.changeChoiceAddress(o))}),n.EFF(7),n.nI1(8,"translate"),n.k0s()()()()}if(2&r){const e=n.XpG(3);n.R7$(2),n.JRh(n.bMT(3,3,"order-new-page.first-step.input-deliver-address-label")),n.R7$(4),n.Y8G("checked",e.choiceAddress),n.R7$(1),n.SpI(" ",n.bMT(8,5,"order-new-page.first-step.question-about-delivery.no-button"),"")}}function Un(r,d){if(1&r&&(n.j41(0,"ion-select-option",12),n.EFF(1),n.k0s()),2&r){const e=d.$implicit;n.Y8G("value",e),n.R7$(1),n.SpI("",null==e?null:e.label," ")}}function Nn(r,d){if(1&r){const e=n.RV6();n.j41(0,"ion-datetime",35,36),n.bIt("ionChange",function(){n.eBV(e);const o=n.sdS(1);return n.Njj(o.confirm(!0))}),n.k0s()}}function Ln(r,d){if(1&r&&(n.j41(0,"div")(1,"ion-item")(2,"ion-label",9),n.EFF(3),n.nI1(4,"translate"),n.k0s(),n.j41(5,"ion-select",41),n.nI1(6,"translate"),n.DNE(7,Un,2,2,"ion-select-option",14),n.k0s()(),n.j41(8,"ion-item",42)(9,"ion-label",9),n.EFF(10),n.nI1(11,"translate"),n.k0s(),n.nrm(12,"ion-input",38),n.k0s(),n.j41(13,"div")(14,"ion-item",43)(15,"ion-label",9),n.EFF(16),n.nI1(17,"translate"),n.k0s(),n.nrm(18,"ion-input",44),n.nI1(19,"date"),n.j41(20,"ion-popover",45),n.DNE(21,Nn,2,0,"ng-template"),n.k0s()()(),n.j41(22,"ion-item",42)(23,"ion-label",9),n.EFF(24),n.nI1(25,"translate"),n.k0s(),n.nrm(26,"ion-input",39),n.k0s()()),2&r){const e=n.XpG(3);n.R7$(3),n.JRh(n.bMT(4,8,"order-new-page.first-step.select-address-label")),n.R7$(2),n.FS9("cancelText",n.bMT(6,10,"button.cancel")),n.R7$(2),n.Y8G("ngForOf",e.address)("ngForTrackBy",e.trackByFn),n.R7$(3),n.JRh(n.bMT(11,12,"order-new-page.first-step.input-location-label")),n.R7$(6),n.JRh(n.bMT(17,14,"order-new-page.first-step.delivery-date")),n.R7$(2),n.FS9("value",n.i5U(19,16,null==e.orderForm?null:e.orderForm.get("deliveryDate").value,"dd/MM/yyyy")),n.R7$(6),n.JRh(n.bMT(25,19,"order-new-page.first-step.input-phone-label"))}}function Gn(r,d){if(1&r&&(n.j41(0,"div"),n.DNE(1,Vn,9,7,"ion-item-group",0),n.DNE(2,Ln,27,21,"div",0),n.k0s()),2&r){const e=n.XpG(2);n.R7$(1),n.Y8G("ngIf",e.choiceDelivery&&(null==e.particularAddress?null:e.particularAddress.length)),n.R7$(1),n.Y8G("ngIf",e.choiceDelivery)}}const Y=function(r,d,e){return[r,d,e]};function Bn(r,d){if(1&r){const e=n.RV6();n.j41(0,"div")(1,"ion-item-group")(2,"ion-label",15),n.EFF(3),n.nI1(4,"translate"),n.k0s(),n.j41(5,"div",16)(6,"ion-toggle",17),n.bIt("ionChange",function(o){n.eBV(e);const s=n.XpG();return n.Njj(s.changeDeliverChoiceHandle(o))}),n.EFF(7),n.nI1(8,"translate"),n.k0s()()(),n.DNE(9,Fn,33,20,"div",18),n.DNE(10,kn,5,3,"ion-item",0),n.DNE(11,An,5,4,"div",0),n.DNE(12,Gn,3,2,"div",0),n.k0s()}if(2&r){const e=n.XpG();n.R7$(3),n.SpI(" ",n.bMT(4,7,"order-new-page.first-step.question-about-delivery.label")," "),n.R7$(3),n.Y8G("checked",e.choiceDelivery),n.R7$(1),n.SpI(" ",n.bMT(8,9,"order-new-page.first-step.question-about-delivery.no-button")," "),n.R7$(2),n.Y8G("ngIf",!e.choiceDelivery),n.R7$(1),n.Y8G("ngIf",!e.choiceDelivery),n.R7$(1),n.Y8G("ngIf",!n.sMw(11,Y,null==e.UserCategory?null:e.UserCategory.CompanyUser,e.UserCategory.Particular,e.UserCategory.Commercial).includes(null==e.commonService||null==e.commonService.user?null:e.commonService.user.category)),n.R7$(1),n.Y8G("ngIf",n.sMw(15,Y,null==e.UserCategory?null:e.UserCategory.CompanyUser,e.UserCategory.Particular,e.UserCategory.Commercial).includes(null==e.commonService||null==e.commonService.user?null:e.commonService.user.category))}}let zn=(()=>{class r{constructor(e,t,o,s,C,x,F,k,I,D){this.router=e,this.storageSrv=t,this.commonService=o,this.pricesService=s,this.productService=C,this.companyService=x,this.locationService=F,this.shipmentService=k,this.translateService=I,this.userSrv=D,this.globalAddress=[],this.particularAddress=[],this.isLoading=!1,this.choiceCustomerDeliveryDestination=!1,this.choiceAddress=!1,this.acceptDelivery=!0,this.carrierInformation={},this.truckCategories=[this.translateService?.currentLang===a.T.French?"Camion Fourgon":"Box Truck",this.translateService?.currentLang===a.T.French?"Camion Citerne \xe0 Produits Alimentaires":"Food-Grade Tanker Truck",this.translateService?.currentLang===a.T.French?"Camion Plateau avec B\xe2che":"Flatbed Truck with Tarp",this.translateService?.currentLang===a.T.French?"Camion Frigorifique":"Refrigerated Truck",this.translateService?.currentLang===a.T.French?"Camion Benne avec B\xe2che":"Dump Truck with Tarp",this.translateService?.currentLang===a.T.French?"Camion Porte-Conteneur":"Container Truck",this.translateService?.currentLang===a.T.French?"Camion de Livraison L\xe9ger":"Light Delivery Truck",this.translateService?.currentLang===a.T.French?"Autre Cat\xe9gorie":"Other Category"],this.isOtherSelected=!1,this.otherCategoryValue="",this.label=this.translateService?.currentLang===a.T.French?"Informations g\xe9n\xe9rales":"General informations",this.UserCategory=u.s,this.cities=[],this.stores=[],this.orderFormPasserBy=new c.gE({selectedCompany:new c.MJ(["",c.k0.required]),shipPoint:new c.MJ("",[c.k0.required]),packaging:new c.MJ("",[]),location:new c.MJ("",[c.k0.required]),retreivePoint:new c.MJ("",[c.k0.required]),tel:new c.MJ("",[c.k0.required]),deliveryDate:new c.MJ("",[c.k0.required]),address:new c.gE({city:new c.MJ("",[c.k0.required]),region:new c.MJ("")})}),this.orderFormCompany=new c.gE({shipPoint:new c.MJ("",[c.k0.required]),packaging:new c.MJ("",[]),address:new c.MJ("",[c.k0.required]),deliveryDate:new c.MJ("",[c.k0.required]),deliveryPoint:new c.gE({tel:new c.MJ("",[c.k0.required]),location:new c.MJ("",[c.k0.required])}),carrier:new c.gE({name:new c.MJ(""),phone:new c.MJ(""),idCard:new c.MJ(""),vehiclePlate:new c.MJ(""),vehicleCategory:new c.MJ(""),driverLicense:new c.MJ(""),otherCategory:new c.MJ("")}),customerDeliveryDestination:new c.MJ("")}),this.currentDate=B()().valueOf(),this.storageSrv.getUserConnected()}ngOnInit(){var e=this;return(0,_.A)(function*(){if(e.commonService.showNav=!1,e.getFormType(),e.commonService.user.category===u.s.Commercial)return yield e.loadCompanies();yield e.initDataStoresWithPrice()})()}initDataStoresWithPrice(){var e=this;return(0,_.A)(function*(){e.isLoading=!0,e.getFormType(),yield e.getStore(),e.populateForm(),e.isLoading=!1})()}onCompanySelect(e){var t=this;return(0,_.A)(function*(){t.companyService.selectedCompanyForSalesOrderProcess=e.detail.value,yield t.initDataStoresWithPrice()})()}onCategoryChange(e){const t=e.detail.value;this.isOtherSelected=t===(this.translateService?.currentLang===a.T.French?"Autre Cat\xe9gorie":"Other Category"),this.orderFormCompany.patchValue({vehicleCategory:t,otherCategory:this.isOtherSelected?this.orderFormCompany.value.otherCategory:""})}populateForm(){const e=this.storageSrv.load("orderForm");if(e){const t=JSON.parse(e);this.orderForm.patchValue(t)}}ionViewWillEnter(){}trackByFn(e){return e}getFormType(){this.orderForm=[u.s.CompanyUser,u.s.Particular,u.s.Commercial].includes(this.commonService?.user?.category)?this.orderFormCompany:this.orderFormPasserBy,this.orderForm.reset(),[u.s?.CompanyUser,u.s.Particular,u.s.Commercial].includes(this.commonService?.user?.category)?this.checkValidatorCompany():this.checkValidator()}loadCompanies(){var e=this;return(0,_.A)(function*(){const t=yield e.userSrv.find(e.commonService?.user?._id);if(t?._id){const{accessToken:o}=e.storageSrv.getUserConnected();e.storageSrv.store("USER_INFO",JSON.stringify({...t,accessToken:o}))}t?.associatedCompanies.length&&(e.companies=t?.associatedCompanies,e.companyService.selectedCompanyForSalesOrderProcess=null)})()}getStore(){var e=this;return(0,_.A)(function*(){e.isLoading=!0;const t={companyId:e.companyService.selectedCompanyForSalesOrderProcess?._id},o=yield e.pricesService.getStores(t);o&&(e.initData=o,e.storageSrv.store("stores",JSON.stringify(o))),e.isLoading=!1})()}getPackagings(e){return e!==this.valueEvent&&(this.orderForm.patchValue({packaging:""}),this.orderForm?.get("packaging")?.updateValueAndValidity(),this.valueEvent=e),e?.packaging}handlePackaging(e){var t=this;return(0,_.A)(function*(){let o;o="detail"in e&&null!==e.detail?e.detail.value:e.target.value,o?(t.orderForm.get("shipPoint").setValue(o),t.orderForm.get("shipPoint").updateValueAndValidity(),t.orderForm.get("shipPoint").markAsTouched(),yield t.getPrices(o?.storeRef),[u.s.CompanyUser,u.s.Particular,u.s.Commercial].includes(t.commonService?.user?.category)||(yield t.getLocation())):console.warn("Aucune usine s\xe9lectionn\xe9e !")})()}getPrices(e){var t=this;return(0,_.A)(function*(){t.isLoading=!0;const o={store:t.orderForm?.get("shipPoint")?.value?._id,companyId:t.companyService.selectedCompanyForSalesOrderProcess?._id};t.prices=yield t.pricesService.getPricesForOrder(o),yield t.getAddress(e),t.prices&&(t.productService.prices=t.prices),t.isLoading=!1})()}getCompanies(e){var t=this;return(0,_.A)(function*(){t.isLoading=!0;const o={category:sn.kJ.WholeSaler,city:e??null},s=yield t.companyService.getCompanies(o);s instanceof Error||(t.companies=e&&0==+s?.data?.length?[{name:t.orderForm.get("shipPoint").value.label}]:s.data),t.isLoading=!1})()}getLocation(){var e=this;return(0,_.A)(function*(){const t={region:e.orderForm.get("shipPoint").value?.address?.region,userType:e.commonService?.user?.category},o=yield e.locationService.getParticularLocation(t);o instanceof Error||(e.employeeLocations=o?.data)})()}getCities(){this.cities=this.employeeLocations.find(e=>this.orderForm.get("address.region").value.includes(e.region))?.cities}getAddress(e){var t=this;return(0,_.A)(function*(){t.isLoading=!0,t.globalAddress=t.address=yield t.getDefaultAddress(e),t.prices?.forEach(o=>{"shippingPrices"in o&&o?.shippingPrices.forEach(s=>{t.particularAddress?.find(C=>s._id===C._id)||t.particularAddress.push(s)})}),t.isLoading=!1})()}getDefaultAddress(e){var t=this;return(0,_.A)(function*(){const o={startRef:e,category:t.commonService?.user?.company?.category,commercialRegion:t.commonService?.user?.company?.address?.commercialRegion},s=(yield t.shipmentService.getCategoriesShippingAddress(o))?.data;return delete o.category,s?.length?s:(o.region=t.commonService?.user?.company?.address?.commercialRegion?.toUpperCase()||t.commonService?.user?.company?.address?.region?.toUpperCase(),(yield t.shipmentService.getDefaultShippingAddress(o)).data)})()}changeDeliverChoiceHandle(e){var t=this;return(0,_.A)(function*(){t.choiceDelivery=e.detail.checked,t.acceptDelivery=!t.choiceDelivery,[u.s?.CompanyUser,u.s.Particular,u.s.Commercial].includes(t.commonService?.user?.category)?t.checkValidatorCompany():t.checkValidator(),[!u.s?.CompanyUser,u.s.Particular,u.s.Commercial].includes(t.commonService?.user?.category)&&(yield t.getCompanies())})()}changeChoiceAddress(e){var t=this;return(0,_.A)(function*(){try{t.isLoading=!0,t.choiceAddress=e.detail.checked,yield t.getAddress(t.orderForm.get("shipPoint")?.value?.storeRef),t.address=t.choiceAddress?t.particularAddress:t.globalAddress,t.orderForm.patchValue({address:""}),t.orderForm?.get("address")?.updateValueAndValidity()}catch(o){return o}finally{t.isLoading=!1}})()}checkValidator(){this.orderForm?.get("retreivePoint")?.setValidators(this.choiceDelivery||[u.s?.CompanyUser,u.s.Particular,u.s.Commercial].includes(this.commonService?.user?.category)?[]:[c.k0.required]),this.orderForm?.get("retreivePoint")?.updateValueAndValidity(),this.orderForm?.get("address")?.setValidators(this.choiceDelivery&&![u.s?.CompanyUser,u.s.Particular,u.s.Commercial].includes(this.commonService?.user?.category)?[c.k0.required]:[]),this.orderForm?.get("address")?.updateValueAndValidity(),this.orderForm?.get("address")?.get("city")?.setValidators(this.choiceDelivery&&![u.s?.CompanyUser,u.s.Particular,u.s.Commercial].includes(this.commonService?.user?.category)?[c.k0.required]:[]),this.orderForm?.get("address")?.get("city")?.updateValueAndValidity(),this.orderForm?.get("address")?.get("region")?.setValidators(this.choiceDelivery||[u.s?.CompanyUser,u.s.Particular,u.s.Commercial].includes(this.commonService?.user?.category)?[]:[c.k0.required]),this.orderForm?.get("address")?.get("region")?.updateValueAndValidity(),this.orderForm?.get("deliveryDate")?.setValidators(this.choiceDelivery||[u.s?.CompanyUser,u.s.Particular,u.s.Commercial].includes(this.commonService?.user?.category)?[]:[c.k0.required]),this.orderForm?.get("deliveryDate")?.updateValueAndValidity(),this.orderForm?.get("location")?.setValidators(this.choiceDelivery&&![u.s?.CompanyUser,u.s.Particular,u.s.Commercial].includes(this.commonService?.user?.category)?[c.k0.required]:[]),this.orderForm?.get("location")?.updateValueAndValidity(),this.orderForm?.get("tel")?.setValidators(this.choiceDelivery&&![u.s?.CompanyUser,u.s.Particular,u.s.Commercial].includes(this.commonService?.user?.category)?[c.k0.required,c.k0.minLength(9),c.k0.maxLength(9)]:[]),this.orderForm?.get("tel")?.updateValueAndValidity()}checkValidatorCompany(){this.orderForm.get("address")?.setValidators(this.choiceDelivery?[c.k0.required]:[]),this.orderForm?.get("address")?.updateValueAndValidity(),this.orderForm?.get("deliveryPoint")?.get("location")?.setValidators(this.choiceDelivery?[c.k0.required]:[]),this.orderForm?.get("deliveryPoint")?.get("location")?.updateValueAndValidity(),this.orderForm?.get("deliveryDate")?.setValidators(this.choiceDelivery?[c.k0.required]:[]),this.orderForm?.get("deliveryDate")?.updateValueAndValidity(),this.orderForm?.get("deliveryPoint")?.get("tel")?.setValidators(this.choiceDelivery?[c.k0.required,c.k0.minLength(9),c.k0.maxLength(9)]:[]),this.orderForm?.get("deliveryPoint")?.get("tel")?.updateValueAndValidity()}onShipPointChange(e){const t=e.detail.value;console.log("Nouvelle usine s\xe9lectionn\xe9e :",t),t&&(this.handlePackaging(t),this.orderFormCompany.patchValue({shipPoint:t}))}nextStep(){let e={...this.orderForm.value,isDeliver:this.choiceDelivery};this.carrierInformation=this.orderForm?.value?.carrier||{},this.customerDeliveryDestination=e?.customerDeliveryDestination||"";const t=this.translateService?.currentLang===a.T.French?"Autre Cat\xe9gorie":"Other Category";this.carrierInformation?.vehicleCategory!==t&&(this.carrierInformation.otherCategory=""),this.cart={store:{_id:e?.shipPoint?._id,label:e?.shipPoint?.label,storeRef:e?.shipPoint?.storeRef,address:{...e?.shipPoint?.address}},items:[],amount:null,shipping:this.choiceDelivery?{...e?.address,retrievePoint:{...e?.retreivePoint},...e?.deliveryPoint,deliveryDate:e?.deliveryDate}:{},renderType:e.isDeliver?A.n.RENDU:A.n.PICKUP,company:e.selectedCompany},this.cart.renderType===A.n.RENDU&&![u.s?.CompanyUser,u.s.Particular,u.s.Commercial].includes(this.commonService?.user?.category)&&(this.cart.shipping={...e?.address?.city,location:e?.location,tel:e?.tel}),this.storageSrv.store("orderForm",JSON.stringify(this.orderForm.value)),this.storageSrv.store("cart",JSON.stringify(this.cart)),this.storageSrv.store("orderDelivery",this.orderDelivery),this.storageSrv.store("choiceDelivery",this.choiceDelivery),this.storageSrv.store("carrierInformation",JSON.stringify(this.carrierInformation)),this.storageSrv.store("customerDeliveryDestination",this.customerDeliveryDestination),this.router.navigate(["order/new/second-step"])}static{this.\u0275fac=function(t){return new(t||r)(n.rXU(P.Ix),n.rXU($.n),n.rXU(b.h),n.rXU(dn.A),n.rXU(V.b),n.rXU(z.B),n.rXU(un),n.rXU(hn.o),n.rXU(T.E),n.rXU(vn.D))}}static{this.\u0275cmp=n.VBU({type:r,selectors:[["app-first-step"]],decls:15,vars:11,consts:[[4,"ngIf"],["id","container",1,"scroller-container"],[1,"input-group",3,"formGroup"],[1,"subtitle-form"],["class","tonnage-employee",4,"ngIf"],[1,"btn-validate"],["color","primary","expand","block",1,"btn--meduim","btn--upper",3,"disabled","click"],[1,"tonnage-employee"],[1,"primary-text"],["position","floating"],["mode","ios","interface","action-sheet",3,"cancelText","ionChange"],[3,"value",4,"ngFor","ngForOf"],[3,"value"],["mode","ios","interface","action-sheet","formControlName","shipPoint",3,"cancelText","disabled","ionChange"],[3,"value",4,"ngFor","ngForOf","ngForTrackBy"],[1,"label-toggle"],[1,"toggle"],["mode","ios","slot","start","name","apple","color","primary",3,"checked","ionChange"],["formGroupName","carrier",4,"ngIf"],["formGroupName","carrier"],["formControlName","vehiclePlate","clearInput",""],["formControlName","name","clearInput",""],["formControlName","phone","clearInput",""],["formControlName","driverLicense","clearInput",""],["mode","ios","interface","action-sheet","formControlName","vehicleCategory",3,"ionChange"],["formControlName","idCard","clearInput",""],["formControlName","otherCategory",3,"value","ionInput"],["formControlName","customerDeliveryDestination","clearInput",""],["formGroupName","address",4,"ngIf"],["formGroupName","address"],["mode","ios","formControlName","region","interface","action-sheet",3,"cancelText","ionChange"],["mode","ios","formControlName","city","interface","action-sheet",3,"cancelText","ionChange"],["mode","ios","formControlName","retreivePoint","interface","action-sheet",3,"cancelText"],["id","date","placeholder","JJ/MM/AAAA",1,"ion-text-start",3,"value"],["trigger","date","size","cover"],["formControlName","deliveryDate","presentation","date","locale","fr-FR",3,"ionChange"],["popoverDatetime",""],["mode","ios","formControlName","city","interface","action-sheet",3,"cancelText"],["formControlName","location","clearInput",""],["formControlName","tel","clearInput",""],[1,"btn-groups"],["mode","ios","formControlName","address","interface","action-sheet",3,"cancelText"],["formGroupName","deliveryPoint"],[1,"date-time"],["id","popoverDatetime","placeholder","JJ/MM/AAAA",1,"ion-text-start",3,"value"],["trigger","popoverDatetime","side","top","alignment","center",1,"account-pop"]],template:function(t,o){if(1&t&&(n.DNE(0,Cn,1,0,"app-progress-spinner",0),n.j41(1,"section",1)(2,"form",2)(3,"div",3)(4,"ion-text"),n.EFF(5),n.k0s()(),n.DNE(6,Pn,11,8,"div",4),n.DNE(7,fn,7,7,"ion-item",0),n.DNE(8,bn,9,13,"ion-item",0),n.DNE(9,Bn,13,19,"div",0),n.k0s(),n.j41(10,"div",5)(11,"ion-button",6),n.bIt("click",function(){return o.nextStep()}),n.j41(12,"ion-label"),n.EFF(13),n.nI1(14,"translate"),n.k0s()()()()),2&t){let s;n.Y8G("ngIf",o.isLoading),n.R7$(2),n.Y8G("formGroup",o.orderForm),n.R7$(3),n.SpI("",o.label," "),n.R7$(1),n.Y8G("ngIf",o.commonService.user.category===o.commonService.userCategory.EmployeeLapasta),n.R7$(1),n.Y8G("ngIf",(null==o.commonService||null==o.commonService.user?null:o.commonService.user.category)===o.UserCategory.Commercial),n.R7$(1),n.Y8G("ngIf",(null==o.commonService||null==o.commonService.user?null:o.commonService.user.category)!==o.UserCategory.Commercial||(null==o.companyService?null:o.companyService.selectedCompanyForSalesOrderProcess)),n.R7$(1),n.Y8G("ngIf",null==o.orderForm||null==(s=o.orderForm.get("shipPoint"))?null:s.value),n.R7$(2),n.Y8G("disabled",null==o.orderForm?null:o.orderForm.invalid),n.R7$(2),n.SpI(" ",n.bMT(14,9,"order-new-page.first-step.next-button-label")," ")}},dependencies:[c.qT,c.BC,c.cb,l.Jm,l.A9,l.$w,l.uz,l.jh,l.he,l.Nm,l.Ip,l.IO,l.BY,l.CF,l.hB,l.Je,l.Gw,h.Sq,h.bT,L._,c.j4,c.JD,c.$R,h.vh,X.F,p.D9],styles:["*[_ngcontent-%COMP%]{margin:0;padding:0}.header-md[_ngcontent-%COMP%]:after{background-image:none!important}.modal[_ngcontent-%COMP%]{--border-radius: calc(60 * var(--res))}.input-group[_ngcontent-%COMP%]{margin-top:calc(75 * var(--res))}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{--padding-start: 0;margin-bottom:calc(50 * var(--res));--placeholder-color: $color-six}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-input[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{font-family:Mont Regular;font-weight:400;font-size:calc(42 * var(--res));color:#1e1e1e}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{padding-top:calc(25 * var(--res));padding-bottom:calc(25 * var(--res))}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]{margin-bottom:calc(50 * var(--res));display:flex;align-items:center;justify-content:space-between;border-bottom-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-250, #c8c7cc)));border-bottom-width:.55px;border-bottom-style:solid}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{width:90%;margin-bottom:0;--border-color: transparent;--inner-border-width: 0px 0px 0px 0px;--border-style: none}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-checkbox[_ngcontent-%COMP%]{margin-top:5%}.bg-primary[_ngcontent-%COMP%]{color:#143c5d}.message[_ngcontent-%COMP%]{margin-bottom:calc(75 * var(--res))}.message[_ngcontent-%COMP%]   ion-title[_ngcontent-%COMP%]{font-weight:800;font-size:calc(70 * var(--res));padding-bottom:calc(25 * var(--res))}.message[_ngcontent-%COMP%]   ion-text[_ngcontent-%COMP%]{font-weight:400;color:#757474;display:block;line-height:1.5em!important}.btn--meduim[_ngcontent-%COMP%]{height:calc(130 * var(--res));font-size:calc(50 * var(--res));font-family:var(--mont-bold)}.btn--meduim[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:var(--mont-bold)}.btn--small[_ngcontent-%COMP%]{height:calc(100 * var(--res))}.btn--round[_ngcontent-%COMP%]{--border-radius: 50%;padding-inline-start:0;padding-inline-end:0;--padding-start: 0;--padding-end: 0}.btn--border-primary[_ngcontent-%COMP%]{--background: transparent;--background-activated: #c4e6d0;--border-color: var(--ion-color-primary);--border-style: solid;--border-width: 2px}.btn--border-secondary[_ngcontent-%COMP%]{--border-width: 2px;--background: transparent;--background-activated: #ddabb2;--border-color: var(--ion-color-secondary);--border-style: solid}.btn--upper[_ngcontent-%COMP%]{text-transform:uppercase}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]{overflow:hidden}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar{width:5px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-track{box-shadow:inset 0 0 5px #546e8d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background:#143c5d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover{background:#143c5d}.bottom-sheet-content[_ngcontent-%COMP%]{background:#fdfdfe;height:100%}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-thumbnail[_ngcontent-%COMP%]{margin-left:calc(31.25 * var(--res));background-color:#1e1e1e;--border-radius: 50%;--size: 23}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:Mont Bold;font-size:calc(45 * var(--res));text-align:center;color:#757474}.bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%], .bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]{padding:calc(75 * var(--res))}.font-size-5[_ngcontent-%COMP%]{font-size:calc(42 * var(--res))}#container[_ngcontent-%COMP%]{height:100%;background-color:transparent;display:flex;flex-direction:column;justify-content:space-between;padding:0 calc(41 * var(--res))}#container[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]{border-radius:calc(26 * var(--res));margin-block:calc(37 * var(--res));background-color:#e7eaef;min-height:calc(75 * var(--resH));overflow-x:hidden;overflow-y:auto;padding:calc(41 * var(--res));margin-top:0}#container[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]::part(icon){content:url(/assets/icons/chevron-down-outline.svg)}#container[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .tonnage-employee[_ngcontent-%COMP%]{font-family:Mont Bold;font-size:calc(40 * var(--res));margin-bottom:calc(26 * var(--res))}#container[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .tonnage-employee[_ngcontent-%COMP%]   .primary-text[_ngcontent-%COMP%]{color:#143c5d}#container[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   ion-item-group[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:space-between;height:calc(125.5 * var(--res));--placeholder-color: $color-six;margin-bottom:calc(40 * var(--res));border-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-250, #c8c7cc)));border-width:0px 0px .55px 0px;border-style:solid}#container[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   ion-item-group[_ngcontent-%COMP%]   .btn-groups[_ngcontent-%COMP%]{display:flex}#container[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   ion-item-group[_ngcontent-%COMP%]   .btn-groups[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{margin-bottom:0;--border-color: transparent}#container[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   ion-item-group[_ngcontent-%COMP%]   .btn-groups[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-checkbox[_ngcontent-%COMP%]{margin-left:5px;width:16px;height:16px}#container[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   ion-item-group[_ngcontent-%COMP%]   .toggle[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;gap:.5rem}#container[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   ion-item-group[_ngcontent-%COMP%]   .toggle[_ngcontent-%COMP%]   ion-text[_ngcontent-%COMP%]{font-family:Mont SemiBold}#container[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .mbottom[_ngcontent-%COMP%]{margin-bottom:calc(37.5 * var(--res));width:100%}#container[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{--padding-start: 0px;margin-bottom:calc(41 * var(--res));--background: $color-nineteen;--ripple-color: transparent;--background-activated: transparent;--background-activated-opacity: transparent;--background-focused: transparent;--background-focused-opacity: transparent;--background-hover: transparent;--background-hover-opacity: transparent}#container[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   ion-input[_ngcontent-%COMP%], #container[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%], #container[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .label-toggle[_ngcontent-%COMP%]{color:#143c5d;font-family:Mont SemiBold;font-weight:600;font-size:calc(38 * var(--res))}#container[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .subtitle-form[_ngcontent-%COMP%]{margin-bottom:calc(41 * var(--res))}#container[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .subtitle-form[_ngcontent-%COMP%]   ion-text[_ngcontent-%COMP%]{color:#143c5d;font-family:Mont Bold;font-weight:700;font-size:calc(45 * var(--res))}#container[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{color:#546e8d;font-family:Mont Regular;font-weight:400;font-size:calc(35 * var(--res))}#container[_ngcontent-%COMP%]   .btn-validate[_ngcontent-%COMP%]{padding:calc(41 * var(--res)) 0}#container[_ngcontent-%COMP%]   .btn-validate[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%]{font-family:var(--mont-semibold)}#container[_ngcontent-%COMP%]   .btn-validate[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:var(--mont-bold)}"]})}}return r})();var Xn=i(765),Yn=i(21635),Jn=i(38112),Qn=i(54357),w=i(88233),R=i(5141),Wn=i(54648),Kn=i(36384);function qn(r,d){1&r&&n.nrm(0,"app-progress-spinner")}function Hn(r,d){if(1&r&&(n.j41(0,"div",7)(1,"ion-title",8),n.EFF(2),n.nI1(3,"translate"),n.k0s(),n.j41(4,"div",9)(5,"div",10)(6,"ion-label",11),n.EFF(7),n.nI1(8,"capitalize"),n.nI1(9,"translate"),n.k0s(),n.j41(10,"ion-label",11),n.EFF(11),n.nI1(12,"capitalize"),n.nI1(13,"translate"),n.k0s(),n.j41(14,"ion-label",11),n.EFF(15),n.nI1(16,"capitalize"),n.nI1(17,"translate"),n.k0s(),n.j41(18,"ion-label",11),n.EFF(19),n.nI1(20,"capitalize"),n.nI1(21,"translate"),n.k0s()(),n.j41(22,"div",12)(23,"ion-label",13),n.EFF(24),n.nI1(25,"transformEnumToString"),n.k0s(),n.j41(26,"ion-label",13),n.EFF(27),n.k0s(),n.j41(28,"ion-label",13),n.EFF(29),n.nI1(30,"date"),n.k0s(),n.j41(31,"ion-label",13),n.EFF(32),n.nI1(33,"number"),n.k0s()()()()),2&r){const e=n.XpG();n.R7$(2),n.JRh(n.bMT(3,9,"order.detail.delivery.title")),n.R7$(5),n.SpI("",n.bMT(8,11,n.bMT(9,13,"order.detail.delivery.mode"))," : "),n.R7$(4),n.SpI("",n.bMT(12,15,n.bMT(13,17,"order.detail.delivery.location"))," : "),n.R7$(4),n.SpI("",n.bMT(16,19,n.bMT(17,21,"order.detail.delivery.date"))," : "),n.R7$(4),n.SpI("",n.bMT(20,23,n.bMT(21,25,"order.detail.delivery.amount"))," : "),n.R7$(5),n.SpI("",n.i5U(25,27,null==e.cart?null:e.cart.renderType,"renderType")," "),n.R7$(3),n.JRh(1==(null==e.cart?null:e.cart.renderType)?null==e.cart||null==e.cart.store?null:e.cart.store.label:(null==e.cart||null==e.cart.shipping?null:e.cart.shipping.label)||"N/A"),n.R7$(2),n.SpI(" ",n.brH(30,30,null==e.cart||null==e.cart.shipping?null:e.cart.shipping.deliveryDate,"dd/MM/yyyy","fr")||"N/A"," "),n.R7$(3),n.SpI("",n.i5U(33,34,null==e.cart||null==e.cart.amount?null:e.cart.amount.shipping,"")," XAF")}}let Zn=(()=>{class r{selectClient(e){this.clientId=e}constructor(e,t){this.companySrv=e,this.fb=t,this.itemsLimited=[],this.isValidate=!1,this.modePayment="",this.isLoading=!0,this.promoCodeAction=Qn.F,this.clientId=null,this.slideOpts={initialSlide:0,speed:400,slidesPerView:4,spaceBetween:16},this.slideProductOpts={initialSlide:0,speed:400,slidesPerView:4,spaceBetween:10},this.products=[1,2,3,4],this.meansOfPayments=[],this.orderSrv=(0,n.WQX)(G.Q),this.storageService=(0,n.WQX)($.n),this.productSrv=(0,n.WQX)(V.b),this.commonSrv=(0,n.WQX)(b.h),this.translateService=(0,n.WQX)(T.E),this.computerPriceSrv=(0,n.WQX)(Jn.A),this.isFrench=this.translateService.currentLang===a.T.French,this.storageService.getUserConnected()}ngOnInit(){this.isLoading=!0}ionViewWillEnter(){var e=this;return(0,_.A)(function*(){e.isLoading=!0,e.customerDeliveryDestination=e.storageService.load("customerDeliveryDestination"),console.log(e.customerDeliveryDestination),e.cart=JSON.parse(e.storageService.load("cart")||"{}"),e.choiceDelivery="true"===e.storageService.load("choiceDelivery"),e.choiceDeliveryLocation="true"===e.storageService.load("choiceDeliveryLocation"),e.checkPaymentMethod(),delete e.cart.amount,e.commonSrv?.user?.associatedCompanies&&e.commonSrv?.user?.associatedCompanies?.length>0&&(e.cart.user=e.commonSrv?.user),e.cart=yield e.computerPriceSrv.getDetailOfCart(e.cart),e.itemsLimited=e.cart?.items?.slice(0,3),e.orderPrice=e.cart.amount,e.shippingInfo=e.cart?.amount?.shippingInfo,e.shipping=e.cart?.shipping,e.storageService.store("cart",JSON.stringify(e.cart)),e.isLoading=!1,e.PaymentMode=e.commonSrv?.user?.category==u.s.Particular?"credit_pay":"account_pay";const t=e.storageService.load("carrierInformation");e.carrierInformation=t?JSON.parse(t):null})()}checkPaymentMethod(){this.isFactory=(this.cart.shipping.retrievePoint&&this.cart?.shipping.retrievePoint?.name)?.toUpperCase()==this.cart?.store?.label?.toUpperCase(),this.meansOfPayments=b.H.filter(e=>!(e.label===w.ru.CREDIT&&1!==this.cart?.renderType||e.label===w.ru.CREDIT&&1===this.cart?.renderType&&!this.isFactory)&&this.commonSrv.user.authorizations.includes(e.label))}selectPaymentMode(e){var t=this;return(0,_.A)(function*(){t.isValidate=!1,t.modePayment=e,t.meansOfPayments[0].label===w.ru.MY_ACCOUNT&&(t.meansOfPayments[0].image=t.meansOfPayments[0].label===w.ru.MY_ACCOUNT?"/assets/logos/cimencam.svg":"/assets/images/cimencam.png");const o=yield t.commonSrv.modalCtrl.create({component:e===w.ru.CREDIT?Yn.X:Xn.c,initialBreakpoint:e===w.ru.CREDIT?.75:e===w.ru.VISA?1:.7,cssClass:"modal",breakpoints:[0,.8,.85,1],mode:"ios",componentProps:{title:t.isFrench?R.b7[e]?.titleForFrench:R.b7[e]?.titleForEnglish,type:e,cart:t.cart,amount:t.orderPrice?.TTC}});yield o.present(),yield o.onWillDismiss()})()}doPaiement(){var e=this;return(0,_.A)(function*(){e.isLoading=!0,e.cart=JSON.parse(e.storageService.load("cart")),e.cart.optionsDiscount&&delete e.cart.optionsDiscount,"Autre Cat\xe9gorie"===e.carrierInformation?.vehicleCategory&&(e.carrierInformation.vehicleCategory=e.carrierInformation.otherCategory,e.carrierInformation.otherCategory="");let t={payment:{mode:R.b7[e.PaymentMode]?.mode,clientOption:R.b7[e.PaymentMode]?.clientOption},customerDeliveryDestination:e.customerDeliveryDestination||"",carrier:e.carrierInformation,cart:{...e.cart,shipping:{...e.cart.shipping,deliveryDate:B()(e.cart.shipping.deliveryDate).valueOf()}}};if(e.commonSrv.user.category===u.s.Commercial){const o=e.companySrv.selectedCompanyForSalesOrderProcess?._id;console.log(e.companySrv.selectedCompanyForSalesOrderProcess?._id),e.orderSrv.response=yield e.orderSrv.createOrderByCommercialForClient(t,o)}else e.commonSrv.user.category===u.s.CompanyUser&&e.commonSrv?.user?.associatedCompanies?.length>0&&(t.user=e.commonSrv?.user),e.orderSrv.response=yield e.orderSrv.create(t);e.isLoading=!1,e.orderSrv.response instanceof N.yz?e.commonSrv.showToast({color:"danger",message:e.orderSrv.response.message}):e.commonSrv.router.navigate(["order/new/four-step"])})()}static{this.\u0275fac=function(t){return new(t||r)(n.rXU(z.B),n.rXU(c.ok))}}static{this.\u0275cmp=n.VBU({type:r,selectors:[["app-third-step"]],decls:10,vars:10,consts:[[1,"scroll-conatainer"],[4,"ngIf"],[1,"scroll-container"],[3,"cart","orderPrice","itemsLimited","shippingInfo","shipping"],["class","info-deliver",4,"ngIf"],[1,"btn-validate"],["color","primary","expand","block",1,"btn--meduim","btn--upper",3,"click"],[1,"info-deliver"],[1,"h3-title"],[1,"bill-info"],[1,"right-block"],[1,"title"],[1,"left-block"],[1,"value"]],template:function(t,o){1&t&&(n.j41(0,"div",0),n.DNE(1,qn,1,0,"app-progress-spinner",1),n.j41(2,"section",2),n.nrm(3,"app-purchase-summary",3),n.DNE(4,Hn,34,37,"div",4),n.k0s(),n.j41(5,"div",5)(6,"ion-button",6),n.bIt("click",function(){return o.doPaiement()}),n.j41(7,"ion-label"),n.EFF(8),n.nI1(9,"translate"),n.k0s()()()()),2&t&&(n.R7$(1),n.Y8G("ngIf",o.isLoading),n.R7$(2),n.Y8G("cart",o.cart)("orderPrice",o.orderPrice)("itemsLimited",o.itemsLimited)("shippingInfo",o.shippingInfo)("shipping",o.shipping),n.R7$(1),n.Y8G("ngIf",o.choiceDelivery),n.R7$(4),n.SpI(" ",n.bMT(9,8,"order-new-page.third-step.next-button-label")," "))},dependencies:[l.Jm,l.he,l.BC,h.bT,Wn.N,L._,h.QX,h.vh,X.F,Kn.E,p.D9],styles:["*[_ngcontent-%COMP%]{margin:0;padding:0}.header-md[_ngcontent-%COMP%]:after{background-image:none!important}.modal[_ngcontent-%COMP%]{--border-radius: calc(60 * var(--res))}.input-group[_ngcontent-%COMP%]{margin-top:calc(75 * var(--res))}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{--padding-start: 0;margin-bottom:calc(50 * var(--res));--placeholder-color: $color-six}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-input[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{font-family:Mont Regular;font-weight:400;font-size:calc(42 * var(--res));color:#1e1e1e}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{padding-top:calc(25 * var(--res));padding-bottom:calc(25 * var(--res))}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]{margin-bottom:calc(50 * var(--res));display:flex;align-items:center;justify-content:space-between;border-bottom-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-250, #c8c7cc)));border-bottom-width:.55px;border-bottom-style:solid}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{width:90%;margin-bottom:0;--border-color: transparent;--inner-border-width: 0px 0px 0px 0px;--border-style: none}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-checkbox[_ngcontent-%COMP%]{margin-top:5%}.bg-primary[_ngcontent-%COMP%]{color:#143c5d}.message[_ngcontent-%COMP%]{margin-bottom:calc(75 * var(--res))}.message[_ngcontent-%COMP%]   ion-title[_ngcontent-%COMP%]{font-weight:800;font-size:calc(70 * var(--res));padding-bottom:calc(25 * var(--res))}.message[_ngcontent-%COMP%]   ion-text[_ngcontent-%COMP%]{font-weight:400;color:#757474;display:block;line-height:1.5em!important}.btn--meduim[_ngcontent-%COMP%]{height:calc(130 * var(--res));font-size:calc(50 * var(--res));font-family:var(--mont-bold)}.btn--meduim[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:var(--mont-bold)}.btn--small[_ngcontent-%COMP%]{height:calc(100 * var(--res))}.btn--round[_ngcontent-%COMP%]{--border-radius: 50%;padding-inline-start:0;padding-inline-end:0;--padding-start: 0;--padding-end: 0}.btn--border-primary[_ngcontent-%COMP%]{--background: transparent;--background-activated: #c4e6d0;--border-color: var(--ion-color-primary);--border-style: solid;--border-width: 2px}.btn--border-secondary[_ngcontent-%COMP%]{--border-width: 2px;--background: transparent;--background-activated: #ddabb2;--border-color: var(--ion-color-secondary);--border-style: solid}.btn--upper[_ngcontent-%COMP%]{text-transform:uppercase}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]{overflow:hidden}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar{width:5px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-track{box-shadow:inset 0 0 5px #546e8d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background:#143c5d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover{background:#143c5d}.bottom-sheet-content[_ngcontent-%COMP%]{background:#fdfdfe;height:100%}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-thumbnail[_ngcontent-%COMP%]{margin-left:calc(31.25 * var(--res));background-color:#1e1e1e;--border-radius: 50%;--size: 23}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:Mont Bold;font-size:calc(45 * var(--res));text-align:center;color:#757474}.bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%], .bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]{padding:calc(75 * var(--res))}.font-size-5[_ngcontent-%COMP%]{font-size:calc(42 * var(--res))}.scroll-container[_ngcontent-%COMP%]{height:calc(100vh - 180px);overflow-y:auto;padding-bottom:80px}.scroll-container[_ngcontent-%COMP%]   .recap-container[_ngcontent-%COMP%]{padding:calc(41 * var(--res)) 0;height:80%;margin:0 calc(41 * var(--res));background-color:#fff;border-radius:calc(30 * var(--res));overflow-x:hidden;overflow-y:auto;box-shadow:-6px 14px 20px 9px #35363633}.scroll-container[_ngcontent-%COMP%]   .recap-container[_ngcontent-%COMP%]   .fbold[_ngcontent-%COMP%]{font-family:Mont Bold}.scroll-container[_ngcontent-%COMP%]   .recap-container[_ngcontent-%COMP%]   .fextrabold[_ngcontent-%COMP%]{font-family:Mont Bold;font-weight:700;font-size:calc(40.7 * var(--res))}.scroll-container[_ngcontent-%COMP%]   .recap-container[_ngcontent-%COMP%]   .fMeduim[_ngcontent-%COMP%]{font-family:Mont Light}.scroll-container[_ngcontent-%COMP%]   .recap-container[_ngcontent-%COMP%]   .primary[_ngcontent-%COMP%]{color:#143c5d!important}.scroll-container[_ngcontent-%COMP%]   .recap-container[_ngcontent-%COMP%]   .recap-title[_ngcontent-%COMP%]{margin-bottom:calc(50 * var(--res))}.scroll-container[_ngcontent-%COMP%]   .recap-container[_ngcontent-%COMP%]   .totalTtc[_ngcontent-%COMP%]{margin-bottom:calc(37.5 * var(--res))}.scroll-container[_ngcontent-%COMP%]   .recap-container[_ngcontent-%COMP%]   .totalTtc[_ngcontent-%COMP%]   div[_ngcontent-%COMP%]{padding:0 calc(37.5 * var(--res));height:calc(120 * var(--res));display:flex;background-color:#c62f45e0;color:#fff;align-items:center;justify-content:space-between;border-radius:calc(20 * var(--res))}.scroll-container[_ngcontent-%COMP%]   .recap-container[_ngcontent-%COMP%]   .type-truck[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;margin:1em 0;padding:0 calc(41 * var(--res))}.scroll-container[_ngcontent-%COMP%]   .recap-container[_ngcontent-%COMP%]   .type-truck[_ngcontent-%COMP%]   ion-toggle[_ngcontent-%COMP%]{color:#0d7d3d;--background-checked: #0d7d3d}.scroll-container[_ngcontent-%COMP%]   .recap-container[_ngcontent-%COMP%]   .padding-horizontal[_ngcontent-%COMP%]{padding:0 calc(41 * var(--res))}.scroll-container[_ngcontent-%COMP%]   .recap-container[_ngcontent-%COMP%]   .padding-horizontal[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{display:block}.scroll-container[_ngcontent-%COMP%]   .recap-container[_ngcontent-%COMP%]   .padding-horizontal[_ngcontent-%COMP%]   ion-text[_ngcontent-%COMP%]{font-size:calc(40 * var(--res))}.scroll-container[_ngcontent-%COMP%]   .recap-container[_ngcontent-%COMP%]   .discount-details[_ngcontent-%COMP%]{padding-top:.5em;padding-bottom:1em;border-top:1px solid rgba(128,128,128,.448)}.scroll-container[_ngcontent-%COMP%]   .recap-container[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%]{font-family:Mont Bold}.scroll-container[_ngcontent-%COMP%]   .recap-container[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{font-family:Mont Bold;display:block;font-size:calc(45 * var(--res))}.scroll-container[_ngcontent-%COMP%]   .recap-container[_ngcontent-%COMP%]   .payment-container[_ngcontent-%COMP%]   ion-slide[_ngcontent-%COMP%]{height:calc(12.25 * var(--resH));box-shadow:0 3.08499px 10.7975px #00000016;border:.771248px solid rgba(218,218,218,.47);border-radius:3.85624px;--background: #ffffff}.scroll-container[_ngcontent-%COMP%]   .recap-container[_ngcontent-%COMP%]   .payment-container[_ngcontent-%COMP%]   ion-slide[_ngcontent-%COMP%]   .active[_ngcontent-%COMP%]{--background: var(--ion-color-primary)}.scroll-container[_ngcontent-%COMP%]   .recap-container[_ngcontent-%COMP%]   .payment-container[_ngcontent-%COMP%]   ion-slide[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center}.scroll-container[_ngcontent-%COMP%]   .recap-container[_ngcontent-%COMP%]   .cancel-discount[_ngcontent-%COMP%]{display:inline-block;padding:.6em;color:#fff;background-color:var(--ion-color-secondary);border-radius:5px;margin:0 0 1em}.btn-validate[_ngcontent-%COMP%]{padding:0 calc(25 * var(--res));z-index:2;position:fixed;bottom:3%;width:calc(100% - 10 * var(--res))}.info-deliver[_ngcontent-%COMP%]{width:91%;padding:0 .5em;background:#ffffff;margin:1rem calc(41 * var(--res));border-radius:5px;border:1px solid #ebebeb;box-shadow:#d3d3d3 0 4px 16px}.info-deliver[_ngcontent-%COMP%]   .h3-title[_ngcontent-%COMP%]{margin:2vh 0;color:#143c5d;text-align:center}.info-deliver[_ngcontent-%COMP%]   .bill-info[_ngcontent-%COMP%]{display:flex;justify-content:center;align-items:center}.info-deliver[_ngcontent-%COMP%]   .bill-info[_ngcontent-%COMP%]   .right-block[_ngcontent-%COMP%], .info-deliver[_ngcontent-%COMP%]   .bill-info[_ngcontent-%COMP%]   .left-block[_ngcontent-%COMP%]{width:50%;letter-spacing:1px;text-align:initial}.info-deliver[_ngcontent-%COMP%]   .bill-info[_ngcontent-%COMP%]   .left-block[_ngcontent-%COMP%]{display:flex;align-items:flex-end;flex-direction:column}.info-deliver[_ngcontent-%COMP%]   .bill-info[_ngcontent-%COMP%]   .right-block[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{margin-bottom:.5em;color:#000000de;font-size:59%}.info-deliver[_ngcontent-%COMP%]   .bill-info[_ngcontent-%COMP%]   .left-block[_ngcontent-%COMP%]   .value[_ngcontent-%COMP%]{white-space:nowrap;display:flex;overflow:hidden;color:#000000de;margin-bottom:.5em}.info-deliver[_ngcontent-%COMP%]   .bill-info[_ngcontent-%COMP%]   .fextrabold[_ngcontent-%COMP%]{font-family:Mont Bold;font-weight:700;font-size:calc(40.7 * var(--res))}.info-deliver[_ngcontent-%COMP%]   .bill-info[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{font-family:Mont Bold;display:block;font-size:calc(45 * var(--res));color:#303950}.info-deliver[_ngcontent-%COMP%]   .bill-info--full[_ngcontent-%COMP%]{flex-direction:column;width:100%}.info-deliver[_ngcontent-%COMP%]   .bill-info--full[_ngcontent-%COMP%]   .full-width-input[_ngcontent-%COMP%]{width:100%;padding:calc(41 * var(--res)) 0;--background: #ffffff;--border-color: var(--ion-color-primary);--padding-start: 10px;--padding-end: 10px;margin:0}.info-deliver[_ngcontent-%COMP%]   .bill-info--full[_ngcontent-%COMP%]   .full-width-input[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{color:#000000de;font-size:calc(35 * var(--res));font-weight:var(--mont-semibold)}.info-deliver[_ngcontent-%COMP%]   .bill-info--full[_ngcontent-%COMP%]   .full-width-input[_ngcontent-%COMP%]   ion-input[_ngcontent-%COMP%]{color:#143c5d;font-family:var(--mont-semibold);font-weight:600;font-size:calc(38 * var(--res))}"]})}}return r})();var J=i(61095),Q=i(73014);const ne=["input-number"];function ee(r,d){if(1&r&&(n.j41(0,"div",18)(1,"ion-label",19),n.EFF(2),n.nI1(3,"translate"),n.j41(4,"span",20),n.EFF(5),n.k0s()()()),2&r){const e=n.XpG();n.R7$(2),n.SpI(" ",n.bMT(3,2,"order-new-page.planning-modal.remainning-quantity")," : "),n.R7$(3),n.SpI(" ",null==e.scheduleSrv?null:e.scheduleSrv.remainingQtyTonneInStock,"T")}}const te=function(r){return{active:r}};function oe(r,d){if(1&r){const e=n.RV6();n.j41(0,"div")(1,"ion-chip",21),n.bIt("click",function(){const s=n.eBV(e).$implicit,C=n.XpG();return n.Njj(C.selectQuickChoice(s))}),n.j41(2,"ion-text"),n.EFF(3),n.k0s()()()}if(2&r){const e=d.$implicit,t=n.XpG();n.R7$(1),n.Y8G("ngClass",n.eq3(2,te,t.currentChoice===e)),n.R7$(2),n.SpI(" ",e," ")}}function re(r,d){if(1&r&&(n.j41(0,"div",22)(1,"ion-button",23)(2,"ion-label"),n.EFF(3),n.nI1(4,"translate"),n.k0s()()()),2&r){const e=n.XpG();n.R7$(1),n.Y8G("disabled",!(null!=e.scheduleSrv&&null!=e.scheduleSrv.scheduleForm&&null!=e.scheduleSrv.scheduleForm.value&&e.scheduleSrv.scheduleForm.value.quantity)),n.R7$(2),n.SpI(" ",n.bMT(4,2,"order-new-page.planning-modal.save-button-label")," ")}}function ie(r,d){1&r&&(n.j41(0,"div",22)(1,"ion-button",24)(2,"ion-label"),n.EFF(3),n.k0s()()()),2&r&&(n.R7$(3),n.SpI(" ","Retirer du panier"," "))}let ae=(()=>{class r{constructor(){this.quickChoice=[100,200,300,400,500,600],this.currentChoice=0,this.activeDelete=!1,this.currentDate=new Date,this.quarts=[],this.slideQuartTimeOpts={initialSlide:0,speed:1e3,slidesPerView:3,spaceBetween:8},this.slideTimeOpts={initialSlide:0,speed:400,slidesPerView:4.8,spaceBetween:10},this.statusForFrench={equal:{message:"Votre planification a atteint la quantit\xe9 totale \xe0 retirer",isGoodPlannify:!0},superior:{message:"Votre planification a d\xe9pass\xe9 la quantit\xe9 totale \xe0 retirer veuillez r\xe9duire votre quantit\xe9",isGoodPlannify:!1},inferior:{message:"Votre planification n'a pas atteint la quantit\xe9 totale \xe0 retirer veuillez augmenter votre quantit\xe9",isGoodPlannify:!1}},this.statusForEnglish={equal:{message:"Your planning has reached the total amount to be removed",isGoodPlannify:!0},superior:{message:"Your planning has exceeded the total quantity to be removed please reduce your quantity",isGoodPlannify:!1},inferior:{message:"Your planning has not reached the total quantity to be withdrawn please increase your quantity",isGoodPlannify:!1}},this.commonSrv=(0,n.WQX)(b.h),this.productSrv=(0,n.WQX)(V.b),this.modalCtrl=(0,n.WQX)(l.W3),this.scheduleSrv=(0,n.WQX)(J.l),this.translateService=(0,n.WQX)(T.E),this.onDeleteClick=()=>this.scheduleSrv.scheduleForm.patchValue({quantity:0}),this.addNumberClick=e=>{console.log("quantity : ",e),this.addQuantity({detail:{value:+`${this.scheduleSrv.scheduleForm.value.quantity}${e}`}})}}ngOnInit(){var e=this;return(0,_.A)(function*(){e.unit=e.productSrv.unit,e.ratio=e.productSrv.unit?.ratioToTone,e.isUpdate=e.scheduleSrv?.cartItem?.quantity>0,e.status=e.translateService.currentLang===a.T.French?e.statusForFrench:e.statusForEnglish,e.quarts=e.translateService.currentLang===a.T.French?R.Mn:R.q6,e.resetData(),yield e.scheduleSrv.getRemovalsObject(),e.isUpdate?(e.activeDelete=!0,e.scheduleSrv.scheduleForm?.patchValue({tonneQuantity:e.scheduleSrv?.cartItem?.quantity/e.productSrv?.unit.ratioToTone,quantity:e.scheduleSrv?.cartItem?.quantity}),e.scheduleSrv.remainingQtyTonne=0):e.scheduleSrv.getRemainingQuantityStock()})()}resetData(){this.scheduleSrv.remainingQtyTonneInStock=0,this.scheduleSrv.currentRemoval=new Q.J7,this.scheduleSrv.removalObject=new Q.iB,this.scheduleSrv.remainingQtyTonne=0,this.scheduleSrv.scheduleForm.reset(),this.scheduleSrv.isGoodPlannify=!1}checkIfDataExist(){(!this.hour||""===this.hour)&&this.commonSrv.showToast({color:"warning",message:this.translateService.currentLang===a.T.French?"Veuillez remplir au pr\xe9alable l'heure d'enl\xe8vement":"Please pre-fill the pick-up time"})}checkNbrTruck(e){var t=this;return(0,_.A)(function*(){if(t.nbrTruck=Math.ceil(e.detail.value),t.nbrTruck<t.scheduleSrv.minCapacityToRemove&&t.nbrTruck)return t.nbrTruck=null,yield t.commonSrv.showToast({color:"warning",message:t.translateService.currentLang===a.T.French?`Veuillez renseigner une valeur sup\xe9rieure \xe0 ${t.scheduleSrv.minCapacityToRemove}`:`Please enter a value greater than ${t.scheduleSrv.minCapacityToRemove}`});if(t.nbrTonnage*t.nbrTruck>0){let o=t.scheduleSrv.getTotalTonnage(),s=t.scheduleSrv.scheduleForm.get("tonneQuantity").value;if(t.scheduleSrv.isGoodPlannify=t.status[o!==s?"superior":"equal"].isGoodPlannify,t.scheduleSrv.remainingQtyTonne<t.nbrTonnage*t.nbrTruck)return t.nbrTruck=null,yield t.commonSrv.showToast({color:"warning",message:t.status.superior.message});t.scheduleSrv.remainingQtyTonne=s-o,Math.round(100*t.scheduleSrv.remainingQtyTonne)}})()}checkNbrTonnage(e){var t=this;return(0,_.A)(function*(){let o=+e.detail.value;if(o>45)return t.nbrTonnage=null,yield t.commonSrv.showToast({color:"warning",message:t.translateService.currentLang===a.T.French?"La capacit\xe9 de tonnage maximum est de 45 tonnes":"The maximum tonnage capacity is 45 tonnes"});if(t.scheduleSrv.remainingQtyTonne<o&&t.scheduleSrv.isGoodPlannify)return t.nbrTonnage=null,yield t.commonSrv.showToast({color:"warning",message:t.translateService.currentLang===a.T.French?"Vous avez exc\xe9d\xe9 la quantit\xe9 totale":"You have exceeded the total quantity"});if(t.nbrTonnage=o,t.nbrTruck>0&&t.nbrTonnage){let s=t.scheduleSrv.scheduleForm.get("tonneQuantity").value,C=t.scheduleSrv.getTotalTonnage();t.scheduleSrv.isGoodPlannify=t.status[C!==s?"superior":"equal"].isGoodPlannify,t.scheduleSrv.remainingQtyTonne<s?(yield t.commonSrv.showToast({color:"warning",message:t.status.superior.message}),t.nbrTonnage=null):(t.scheduleSrv.remainingQtyTonne=s-C,Math.round(100*t.scheduleSrv.remainingQtyTonne))}return o<t.scheduleSrv.minCapacityToRemove&&t.nbrTonnage?(t.nbrTonnage=null,yield t.commonSrv.showToast({color:"warning",message:t.translateService.currentLang===a.T.French?`Vous ne pouvez pas faire une planification moins de ${t.scheduleSrv.minCapacityToRemove} tonne`:`You can't plan less than ${t.scheduleSrv.minCapacityToRemove} ton`})):void 0})()}selectHour(e){this.SelectedQuart&&""!=this.SelectedQuart?(this.hour=e,this.scheduleSrv.isGoodPlannify=this.hour&&this.selectedQuartHours&&this.scheduleSrv.scheduleForm.valid&&0===this.scheduleSrv.remainingQtyTonne):this.commonSrv.showToast({color:"warning",message:this.translateService.currentLang===a.T.French?"Veuillez remplir au pr\xe9alable la quart temps":"Please pre-complete the shift"})}selectQuart(e){this.scheduleSrv.scheduleForm.valid?(this.selectedQuartHours=e?.quartHours,this.SelectedQuart=e.value,this.hour="",this.scheduleSrv.isGoodPlannify=!1):this.commonSrv.showToast({color:"warning",message:this.translateService.currentLang===a.T.French?"Veuillez remplir au pr\xe9alable la date d'enl\xe8vement et la quantit\xe9 \xe0 enlever":"Please pre-fill the removal date and quantity to be removed"})}saveSchedule(){var e=this;return(0,_.A)(function*(){let t={...e.scheduleSrv.cartItem};e.isUpdate&&e.activeDelete?t.quantity=-1:(e.scheduleSrv.cartItem.quantity=e.scheduleSrv.scheduleForm.get("quantity").value,t.quantity=e.scheduleSrv.cartItem.quantity),e.scheduleSrv.scheduleForm.reset(),e.modalCtrl.dismiss(t)})()}addTruckWithTonnage(){var e=this;return(0,_.A)(function*(){if(!e.nbrTonnage||!e.nbrTruck)return void(yield e.commonSrv.showToast({color:"warning",message:e.translateService.currentLang===a.T.French?"Veuillez renseigner les quantit\xe9s":"Please fill in the quantities"}));let t=e.scheduleSrv.getTotalTonnage(),o=e.scheduleSrv.scheduleForm.get("tonneQuantity").value;e.scheduleSrv.isGoodPlannify=e.status[t!==o?"superior":"equal"].isGoodPlannify,e.scheduleSrv.remainingQtyTonne>=e.nbrTonnage*e.nbrTruck?e.saveSchedule():yield e.commonSrv.showToast({color:"warning",message:e.status[t>o?"superior":"equal"].message})})()}addQuantity(e){e?.detail?.value<0&&(this.commonSrv.showToast({color:"warning",message:this.translateService.currentLang===a.T.French?"Veuillez renseigner une valeur positive":"Please enter a positive value"}),this.scheduleSrv.scheduleForm.patchValue({quantity:null})),this.scheduleSrv.remainingQtyTonne=this.isUpdate&&0===this.scheduleSrv.remainingQtyTonne?this.scheduleSrv.remainingQtyTonne:parseFloat((e.detail.value/this.ratio).toFixed(3)),this.scheduleSrv.scheduleForm.patchValue({tonneQuantity:e.detail.value/this.ratio}),this.activeDelete=this.scheduleSrv.scheduleForm.get("quantity").value<=0||this.scheduleSrv.cartItem?.quantity===this.scheduleSrv.scheduleForm.get("quantity").value,this.scheduleSrv.verifyCanRemoval(this.scheduleSrv.scheduleForm.get("tonneQuantity").value)}addTonneQuantity(e){var t=this;return(0,_.A)(function*(){return e?.detail?.value<0?(yield t.commonSrv.showToast({color:"warning",message:t.translateService.currentLang===a.T.French?"Veuillez renseigner une valeur positive":"Please enter a positive value"}),t.scheduleSrv.scheduleForm.patchValue({quantity:null})):(t.scheduleSrv.remainingQtyTonne=t.isUpdate&&0===t.scheduleSrv.remainingQtyTonne?t.scheduleSrv.remainingQtyTonne:e.detail.value,t.scheduleSrv.scheduleForm.patchValue({quantity:e.detail.value*t.ratio}),t.commonSrv?.user?.category===u.s.EmployeeLapasta&&e?.detail?.value>t.commonSrv?.user?.tonnage?.capacityPerYear?(yield t.commonSrv.showToast({color:"warning",message:t.translateService.currentLang===a.T.French?`Vous avez exceder votre capacit\xe9 de tonnage annuelle restante (${t.commonSrv?.user?.tonnage?.capacityPerYear} T)`:`You have exceded your remaining annual tonnage capacity (${t.commonSrv?.user?.tonnage?.capacityPerYear} T)`}),t.scheduleSrv.scheduleForm.patchValue({quantity:null})):t.scheduleSrv.verifyCanRemoval(e.detail.value)?t.scheduleSrv.scheduleForm.patchValue({quantity:null}):void 0)})()}selectQuickChoice(e){this.currentChoice=e,this.scheduleSrv.scheduleForm.patchValue({quantity:e}),this.activeDelete=this.scheduleSrv.cartItem?.quantity===(this.scheduleSrv.scheduleForm.get("quantity").value||0)}static{this.\u0275fac=function(t){return new(t||r)}}static{this.\u0275cmp=n.VBU({type:r,selectors:[["app-form-per-quantity"]],viewQuery:function(t,o){if(1&t&&n.GBs(ne,5),2&t){let s;n.mGM(s=n.lsd())&&(o.inputNumber=s.first)}},inputs:{scheduleDates:"scheduleDates"},decls:33,vars:18,consts:[[1,"bottom-sheet-content"],[1,"ion-text-center","ion-padding"],["slot","end",3,"click"],["src","assets/icons/close-btn.svg"],["color","primary",1,"title"],["id","content",3,"formGroup","ngSubmit"],["class","form-group padding-horizontal last-form",4,"ngIf"],[1,"form-group","padding-horizontal"],[1,"card-quantity-title"],["src","assets/icons/card-order.svg"],[1,"mbottom","quantities"],[1,"ion-justify-content-between"],[1,"unit"],[1,"fMedium"],[1,"space-h-v"],[4,"ngFor","ngForOf"],["locale","fr-FR","min","0","formControlName","quantity","type","number","pattern","[0-9]*","step","1","placeholder","0","clearInput","",1,"input-number",3,"readonly","ionChange"],["class","btn-validate",4,"ngIf"],[1,"form-group","padding-horizontal","last-form"],[1,"title"],[1,"primary-text","fMedium","mbottom"],[3,"ngClass","click"],[1,"btn-validate"],["type","submit","color","primary","expand","block",1,"btn--meduim","btn--upper",3,"disabled"],["type","submit","color","danger","expand","block",1,"btn--meduim","btn--upper"]],template:function(t,o){1&t&&(n.j41(0,"div",0)(1,"ion-header")(2,"ion-toolbar",1)(3,"ion-thumbnail",2),n.bIt("click",function(){return o.scheduleSrv.closeForm()}),n.nrm(4,"ion-img",3),n.k0s(),n.j41(5,"ion-title",4),n.EFF(6),n.nI1(7,"translate"),n.k0s()()(),n.j41(8,"ion-content")(9,"form",5),n.bIt("ngSubmit",function(){return o.saveSchedule()}),n.DNE(10,ee,6,4,"div",6),n.j41(11,"div",7)(12,"div",8),n.nrm(13,"ion-img",9),n.j41(14,"ion-label"),n.EFF(15),n.nI1(16,"titlecase"),n.k0s()(),n.j41(17,"ion-grid",10)(18,"ion-row",11)(19,"ion-col",12)(20,"ion-label",13),n.EFF(21),n.nI1(22,"translate"),n.k0s(),n.j41(23,"div",14),n.DNE(24,oe,4,4,"div",15),n.k0s()()(),n.j41(25,"ion-row",11)(26,"ion-col",12)(27,"ion-label",13),n.EFF(28),n.nI1(29,"translate"),n.k0s(),n.j41(30,"ion-input",16),n.bIt("ionChange",function(C){return o.addQuantity(C)}),n.k0s()()()()(),n.DNE(31,re,5,4,"div",17),n.DNE(32,ie,4,1,"div",17),n.k0s()()()),2&t&&(n.R7$(6),n.SpI(" ",n.bMT(7,10,"order-new-page.planning-modal.title-quantity")," "),n.R7$(3),n.Y8G("formGroup",o.scheduleSrv.scheduleForm),n.R7$(1),n.Y8G("ngIf",null==o.scheduleSrv?null:o.scheduleSrv.remainingQtyTonneInStock),n.R7$(5),n.JRh(n.bMT(16,12,null==o.scheduleSrv.cartItem||null==o.scheduleSrv.cartItem.product?null:o.scheduleSrv.cartItem.product.label)+" ("+(null==o.scheduleSrv.cartItem.packaging?null:o.scheduleSrv.cartItem.packaging.label)+")"),n.R7$(6),n.SpI(" ",n.bMT(22,14,"order-new-page.planning-modal.quick-select"),""),n.R7$(3),n.Y8G("ngForOf",o.quickChoice),n.R7$(4),n.SpI(" ",n.bMT(29,16,"order-new-page.planning-modal.label-quantity")," "),n.R7$(2),n.Y8G("readonly",(null==o.scheduleSrv||null==o.scheduleSrv.schedules?null:o.scheduleSrv.schedules.length)>0),n.R7$(1),n.Y8G("ngIf",!(o.isUpdate&&o.activeDelete)),n.R7$(1),n.Y8G("ngIf",o.isUpdate&&o.activeDelete))},dependencies:[h.YU,h.Sq,h.bT,l.Jm,l.ZB,l.hU,l.W9,l.lO,l.eU,l.KW,l.$w,l.he,l.ln,l.IO,l.Zx,l.BC,l.ai,l.su,c.qT,c.BC,c.cb,c.R_,c.j4,c.JD,h.PV,p.D9],styles:['@charset "UTF-8";*[_ngcontent-%COMP%]{margin:0;padding:0}.header-md[_ngcontent-%COMP%]:after{background-image:none!important}.modal[_ngcontent-%COMP%]{--border-radius: calc(60 * var(--res))}.input-group[_ngcontent-%COMP%]{margin-top:calc(75 * var(--res))}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{--padding-start: 0;margin-bottom:calc(50 * var(--res));--placeholder-color: $color-six}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-input[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{font-family:Mont Regular;font-weight:400;font-size:calc(42 * var(--res));color:#1e1e1e}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{padding-top:calc(25 * var(--res));padding-bottom:calc(25 * var(--res))}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]{margin-bottom:calc(50 * var(--res));display:flex;align-items:center;justify-content:space-between;border-bottom-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-250, #c8c7cc)));border-bottom-width:.55px;border-bottom-style:solid}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{width:90%;margin-bottom:0;--border-color: transparent;--inner-border-width: 0px 0px 0px 0px;--border-style: none}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-checkbox[_ngcontent-%COMP%]{margin-top:5%}.bg-primary[_ngcontent-%COMP%]{color:#143c5d}.message[_ngcontent-%COMP%]{margin-bottom:calc(75 * var(--res))}.message[_ngcontent-%COMP%]   ion-title[_ngcontent-%COMP%]{font-weight:800;font-size:calc(70 * var(--res));padding-bottom:calc(25 * var(--res))}.message[_ngcontent-%COMP%]   ion-text[_ngcontent-%COMP%]{font-weight:400;color:#757474;display:block;line-height:1.5em!important}.btn--meduim[_ngcontent-%COMP%]{height:calc(130 * var(--res));font-size:calc(50 * var(--res));font-family:var(--mont-bold)}.btn--meduim[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:var(--mont-bold)}.btn--small[_ngcontent-%COMP%]{height:calc(100 * var(--res))}.btn--round[_ngcontent-%COMP%]{--border-radius: 50%;padding-inline-start:0;padding-inline-end:0;--padding-start: 0;--padding-end: 0}.btn--border-primary[_ngcontent-%COMP%]{--background: transparent;--background-activated: #c4e6d0;--border-color: var(--ion-color-primary);--border-style: solid;--border-width: 2px}.btn--border-secondary[_ngcontent-%COMP%]{--border-width: 2px;--background: transparent;--background-activated: #ddabb2;--border-color: var(--ion-color-secondary);--border-style: solid}.btn--upper[_ngcontent-%COMP%]{text-transform:uppercase}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]{overflow:hidden}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar{width:5px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-track{box-shadow:inset 0 0 5px #546e8d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background:#143c5d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover{background:#143c5d}.bottom-sheet-content[_ngcontent-%COMP%]{background:#fdfdfe;height:100%}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-thumbnail[_ngcontent-%COMP%]{margin-left:calc(31.25 * var(--res));background-color:#1e1e1e;--border-radius: 50%;--size: 23}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:Mont Bold;font-size:calc(45 * var(--res));text-align:center;color:#757474}.bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%], .bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]{padding:calc(75 * var(--res))}.font-size-5[_ngcontent-%COMP%]{font-size:calc(42 * var(--res))}ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]{--background: rgba(208, 227, 246, .61);--color: $color-primary;--padding-top: 1.75rem;--border-width: 0;--padding-bottom: 1rem;padding:0}ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-title[_ngcontent-%COMP%]{font-family:Mont Bold;color:#143c5d;font-size:calc(44 * var(--res))}ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-thumbnail[_ngcontent-%COMP%]{z-index:10;position:absolute;right:calc(41 * var(--res));height:max-content;width:max-content}ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-thumbnail[_ngcontent-%COMP%]   ion-img[_ngcontent-%COMP%]{width:18px;height:18px}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]{padding:calc(41 * var(--res)) 0;color:#000;display:flex;flex-direction:column;height:100%}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{font-family:Mont Bold;font-size:calc(44 * var(--res));margin-bottom:calc(31.25 * var(--res))}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]   .primary-text[_ngcontent-%COMP%]{color:#143c5d}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]   .card-quantity-title[_ngcontent-%COMP%]{display:flex;align-items:center;background-color:#e7eaef;padding-inline:calc(41 * var(--res));padding-block:4px;border-radius:12px;margin-bottom:calc(50 * var(--res))}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]   .card-quantity-title[_ngcontent-%COMP%]   ion-img[_ngcontent-%COMP%]{width:calc(110 * var(--res));margin-right:calc(25 * var(--res))}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]   .card-quantity-title[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{color:#143c5d;font-family:Mont SemiBold;font-weight:600;font-size:calc(37 * var(--res))}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]   .space-h-v[_ngcontent-%COMP%]{display:flex;white-space:nowrap;overflow-x:scroll;scrollbar-width:none;-webkit-scrollbar-width:none;margin-bottom:calc(50 * var(--res));margin-top:12px}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]   .space-h-v[_ngcontent-%COMP%]   ion-chip[_ngcontent-%COMP%]{--background: #F0F0F0;--color: var(--ion-color-primary);border:#E4F1FF .5px solid;padding-block:calc(19.3 * var(--res));padding-inline:calc(60 * var(--res));width:max-content;height:max-content;border-radius:4rem;margin-right:8px}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]   .space-h-v[_ngcontent-%COMP%]   ion-chip[_ngcontent-%COMP%]   ion-text[_ngcontent-%COMP%]{font-family:Mont Bold;font-weight:700;font-size:calc(44 * var(--res))}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]   .space-h-v[_ngcontent-%COMP%]   ion-chip.active[_ngcontent-%COMP%]{--background: var(--ion-color-primary);--color: #F0F0F0;border:none}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]   ion-input[_ngcontent-%COMP%]{--padding-start: 0;color:#143c5d;font-family:Mont Bold;font-weight:700;font-size:calc(42 * var(--res))}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]   .date-time[_ngcontent-%COMP%]   ion-input[_ngcontent-%COMP%]{--padding-start: var(--space-4) !important}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]   .quantities[_ngcontent-%COMP%]   ion-input[_ngcontent-%COMP%]{border-bottom:2px solid #dedede}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]   .quantities[_ngcontent-%COMP%]   .tonne[_ngcontent-%COMP%]{margin-left:calc(31.25 * var(--res))}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]   .quantities[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:Mont Regular!important;font-weight:400!important;font-size:calc(42 * var(--res))!important;color:#1e1e1e!important}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]   .ion-justify-content-between[_ngcontent-%COMP%]:last-child   ion-label[_ngcontent-%COMP%]:first-child{padding-top:calc(2.5 * var(--resH))}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]   .ion-justify-content-between[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{color:#0b305c!important;font-size:calc(38 * var(--res))!important}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]   .btn-schedule[_ngcontent-%COMP%]{margin-bottom:calc(31.25 * var(--res))}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]   .add-qty-btn[_ngcontent-%COMP%]{display:flex;justify-content:flex-end}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]   .add-qty-btn[_ngcontent-%COMP%]   .add-line[_ngcontent-%COMP%]{width:100%}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]   .add-qty-btn[_ngcontent-%COMP%]   .button-inner[_ngcontent-%COMP%]{gap:10px}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]   .mbottom[_ngcontent-%COMP%]{margin:calc(30 * var(--res)) 0;width:100%}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]   .select-type[_ngcontent-%COMP%]{margin-bottom:calc(37.5 * var(--res))}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]   .select-type[_ngcontent-%COMP%]   .ion-label[_ngcontent-%COMP%]{font-family:Mont Regular!important;font-weight:400!important;font-size:calc(42 * var(--res))!important;color:#0b305c!important}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]   .padding-horizontal[_ngcontent-%COMP%]{padding:0 calc(41 * var(--res))}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]   .fbold[_ngcontent-%COMP%]{font-family:Mont Bold;font-size:calc(42 * var(--res))}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]   .fMedium[_ngcontent-%COMP%]{font-family:Mont Light}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{display:block}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{--padding-start: 0}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]   .no-mbottom[_ngcontent-%COMP%]{margin-bottom:0}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]   .btn-reduce[_ngcontent-%COMP%]{width:calc(50 * var(--res));height:calc(50 * var(--res));--padding-top: 0;--padding-bottom: 0}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]   .btn-reduce[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:1em}ion-content[_ngcontent-%COMP%]   .btn-validate[_ngcontent-%COMP%]{margin-top:1em;padding:0 calc(41 * var(--res));width:100%}.numerical-keyboard[_ngcontent-%COMP%]   ion-row[_ngcontent-%COMP%]{justify-content:center;align-items:center;background-color:#fff}.numerical-keyboard[_ngcontent-%COMP%]   ion-col[_ngcontent-%COMP%]{display:flex;justify-content:center;align-items:center}.numerical-keyboard[_ngcontent-%COMP%]   ion-col[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{display:flex;flex-direction:column;justify-content:center;align-items:center;font-size:var(--fs-600);width:100%;height:calc(150 * var(--res));border:1px solid #e0e0e0}.numerical-keyboard[_ngcontent-%COMP%]   ion-col[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:var(--fs-500)}.numerical-keyboard[_ngcontent-%COMP%]   ion-col[_ngcontent-%COMP%]   div[_ngcontent-%COMP%]{font-size:calc(.7 * var(--fs-300));margin-top:calc(.5 * var(--container-padding))}']})}}return r})();var ce=i(18707),se=i(56071);function le(r,d){1&r&&n.nrm(0,"app-progress-spinner")}const W=function(r){return{active:r}};function de(r,d){if(1&r){const e=n.RV6();n.j41(0,"ion-chip",3),n.bIt("click",function(){n.eBV(e);const o=n.XpG().$implicit,s=n.XpG();return n.Njj(s.handleClick(o))}),n.j41(1,"ion-text"),n.EFF(2),n.k0s()()}if(2&r){const e=n.XpG().$implicit,t=n.XpG();n.Y8G("ngClass",n.eq3(2,W,(null==e?null:e.label)===t.selectedPackaging)),n.R7$(2),n.SpI(" ",null==e?null:e.label," ")}}function ge(r,d){if(1&r&&(n.j41(0,"div",12),n.DNE(1,de,3,4,"ion-chip",13),n.k0s()),2&r){const e=d.$implicit;n.R7$(1),n.Y8G("ngIf",e&&(null==e?null:e.label))}}function pe(r,d){if(1&r){const e=n.RV6();n.j41(0,"div",20),n.bIt("click",function(){n.eBV(e);const o=n.XpG().$implicit,s=n.XpG(2);return n.Njj(s.addQuantity(o))}),n.nrm(1,"ion-img",21),n.k0s()}}function me(r,d){if(1&r){const e=n.RV6();n.j41(0,"div",16)(1,"div",17),n.bIt("click",function(){const s=n.eBV(e).$implicit,C=n.XpG(2);return n.Njj(!(s.quantity>0)&&C.addQuantity(s))}),n.DNE(2,pe,2,0,"div",18),n.nrm(3,"app-product-card",19),n.k0s()()}if(2&r){const e=d.$implicit;n.R7$(2),n.Y8G("ngIf",e.quantity>0),n.R7$(1),n.Y8G("item",e)("isEdit",(null==e?null:e.quantity)>0)}}function ue(r,d){if(1&r&&(n.j41(0,"div",14),n.DNE(1,me,4,3,"div",15),n.k0s()),2&r){const e=n.XpG();n.R7$(1),n.Y8G("ngForOf",e.productsVariety)("ngForTrackBy",e.trackByFn)}}function _e(r,d){1&r&&(n.j41(0,"div",22),n.nrm(1,"ion-img",23),n.j41(2,"ion-label"),n.EFF(3),n.nI1(4,"translate"),n.k0s()()),2&r&&(n.R7$(3),n.SpI(" ",n.bMT(4,1,"order-new-page.second-step.empty-offer-price")," "))}const he=[{path:"",component:an,children:[{path:"first-step",component:zn},{path:"second-step",component:(()=>{class r{constructor(e,t,o,s,C,x,F,k,I,D){this.router=e,this.productSrv=t,this.modalCtrl=o,this.commonService=s,this.scheduleSrv=C,this.storageService=x,this.navController=F,this.alertController=k,this.animationCtrl=I,this.translateService=D,this.cartItems=[],this.nbrOfProductSelect=0,this.isLoading=!0,this.selectedPackaging="Tous",this.selectedProducts=[],this.enterAnimation=U=>{const K=U.shadowRoot,Pe=this.animationCtrl.create().addElement(K.querySelector("ion-backdrop")).fromTo("opacity","0.01","var(--backdrop-opacity)"),Me=this.animationCtrl.create().addElement(K.querySelector(".modal-wrapper")).keyframes([{offset:0,opacity:"0",transform:"scale(0)"},{offset:1,opacity:"0.99",transform:"scale(1)"}]);return this.animationCtrl.create().addElement(U).easing("ease-out").duration(500).addAnimation([Pe,Me])},this.leaveAnimation=U=>this.enterAnimation(U).direction("reverse")}ngOnInit(){var e=this;return(0,_.A)(function*(){e.isLoading=!0,e.storageService.remove("removals"),e.getProducts(),yield e.getPlanification()})()}getPlanification(){var e=this;return(0,_.A)(function*(){e.isLoading=!0;const t={storeId:e.scheduleSrv?.cart?.store?._id,today:(new Date).valueOf()};return e.scheduleSrv.availableRemovals=yield e.scheduleSrv.getPlanificationByStores(t),e.isLoading=!1})()}disabledCartProduct(e){return!this.scheduleSrv?.availableRemovals?.find(o=>o?.data?.product?._id===e?.product?._id)}showMessageUnAvailablePlanification(){var e=this;return(0,_.A)(function*(){return yield(yield e.alertController.create({header:e.translateService.currentLang===a.T.French?"Indisponible":"Unavailable",message:e.translateService.currentLang===a.T.French?"Aucune planification en production n'est disponible pour ce produit. ":"No production planning is available for this product.",buttons:[{text:"OK",role:"OK",handler:()=>{}}]})).present()})()}trackByFn(e,t){return e}showDetail(){this.router.navigate(["/item-detail"])}addQuantity(e){var t=this;return(0,_.A)(function*(){t.scheduleSrv.cartItem=e;const o=yield t.modalCtrl.create({component:ae,cssClass:"modal",initialBreakpoint:.6,breakpoints:[0,.75,.5,.35,.9,.95,1],mode:"ios",componentProps:{}});yield o.present();const{data:s}=yield o.onWillDismiss();if(s?.quantity>0){t.nbrOfProductSelect++;const C={...e,quantity:s.quantity};t.productsVariety=t.productsVariety.map(F=>F.product._id===C.product._id&&F?.packaging?._id===C?.packaging?._id?C:F);const x=t.selectedProducts.findIndex(F=>F?.product?._id===C?.product?._id&&F?.packaging?._id===C?.packaging?._id);-1!==x?t.selectedProducts[x]=C:t.selectedProducts.push(C)}-1===s?.quantity&&t.presentAlert(t.scheduleSrv.cartItem)})()}getProducts(){this.isLoading=!0,this.scheduleSrv.cart=JSON.parse(this.storageService.load("cart")),this.productSrv.unit=this.productSrv?.prices[0]?.packaging?.unit,this.productSrv.prices?.map(t=>{let o;o=this.scheduleSrv.cart.renderType===A.n.RENDU&&[u.s?.CompanyUser,u.s.Commercial].includes(this.commonService?.user?.category)&&"companyId"in this.scheduleSrv?.cart?.shipping?t?.shippingPrices?.find(C=>C?._id===this.scheduleSrv.cart?.shipping?._id)?.amount:t?.amount,o&&this.cartItems.push({category:t?.product?.category,quantity:0,unitPrice:o,packaging:t.packaging,product:t.product})});const e=JSON.parse(this.storageService.load("stores"));this.availableStores=e?.find(t=>t?._id==this.scheduleSrv?.cart?.store?._id),this.storesPackaging=this.availableStores?.packaging,this.storesPackaging=this.storesPackaging?.sort((t,o)=>t?.unit?.value-o?.unit?.value),this.productsVariety=this.commonService.sortPricesByLabelProduct([...this.cartItems]),this.allCartItems=[...this.productsVariety],this.commonService.cartItems=this.cartItems,this.storageService.store("priceOffers",JSON.stringify(this.cartItems)),this.productsVariety?.length||(this.cartItems=JSON.parse(this.storageService.load("priceOffers"))),this.isLoading=!1}handleClick(e){this.productsVariety=this.allCartItems.filter(t=>t.packaging?._id==e?._id),this.selectedPackaging=e?.label}allProductsDisplay(){this.productsVariety=this.allCartItems,this.selectedPackaging="Tous"}presentAlert(e){var t=this;return(0,_.A)(function*(){const o=yield t.modalCtrl.create({component:ce.A,cssClass:"modalClass",componentProps:{cart:e}});yield o.present();const{data:s}=yield o.onWillDismiss();"valider"===s?(t.removeProduct(e),yield o.onWillDismiss()):yield o.onWillDismiss()})()}removeProduct(e){let t=this.productsVariety.findIndex(o=>o===e);e.quantity=0,this.nbrOfProductSelect--,this.productsVariety.splice(t,1,e),this.selectedProducts=this.selectedProducts.filter(o=>o.product._id!==e.product._id)}nextStep(){this.scheduleSrv.cart.items=this.selectedProducts.filter(e=>e.quantity&&e.quantity>0),this.storageService.store("cart",JSON.stringify(this.scheduleSrv.cart)),this.router.navigate(["/order/new/third-step"])}handleInput(e){const t=`${e.target.value}`.toLowerCase();return this.productsVariety=this.allCartItems.filter(o=>o?.product?.label?.toLowerCase().includes(t))?.sort((o,s)=>o.toString().toLowerCase().indexOf(t.toLowerCase())-s.toString().toLowerCase().indexOf(t.toLowerCase()))}openModalQuantity(e){this.addQuantity(e),console.log(e)}static{this.\u0275fac=function(t){return new(t||r)(n.rXU(P.Ix),n.rXU(V.b),n.rXU(l.W3),n.rXU(b.h),n.rXU(J.l),n.rXU($.n),n.rXU(l.q9),n.rXU(l.hG),n.rXU(l.Hx),n.rXU(T.E))}}static{this.\u0275cmp=n.VBU({type:r,selectors:[["app-second-step"]],decls:21,vars:19,consts:[[4,"ngIf"],[1,"part-search-chip"],[1,"space-h-v","space-all"],[3,"ngClass","click"],["class","space-h-v",4,"ngFor","ngForOf","ngForTrackBy"],[1,"header"],[1,"title"],["id","container",1,"scroller-container"],["class","products",4,"ngIf"],["class","empty-list",4,"ngIf"],[1,"btn-validate"],["color","primary","expand","block",1,"btn--meduim","btn--upper",3,"readonly","disabled","click"],[1,"space-h-v"],[3,"ngClass","click",4,"ngIf"],[1,"products"],["class","item",4,"ngFor","ngForOf","ngForTrackBy"],[1,"item"],[1,"item-block",3,"click"],["class","icon-container",3,"click",4,"ngIf"],[3,"item","isEdit"],[1,"icon-container",3,"click"],["color","primary","src","/assets/icons/edit-white.svg","size","large"],[1,"empty-list"],["src","/assets/icons/Research paper-amico.svg"]],template:function(t,o){1&t&&(n.DNE(0,le,1,0,"app-progress-spinner",0),n.j41(1,"section")(2,"div",1)(3,"div",2)(4,"ion-chip",3),n.bIt("click",function(){return o.allProductsDisplay()}),n.j41(5,"ion-text"),n.EFF(6),n.nI1(7,"translate"),n.k0s()()(),n.DNE(8,ge,2,1,"div",4),n.k0s(),n.j41(9,"div",5)(10,"ion-label",6),n.EFF(11),n.nI1(12,"translate"),n.k0s()()(),n.j41(13,"section",7),n.DNE(14,ue,2,2,"div",8),n.DNE(15,_e,5,3,"div",9),n.j41(16,"div",10)(17,"ion-button",11),n.bIt("click",function(){return o.nextStep()}),n.j41(18,"ion-label"),n.EFF(19),n.nI1(20,"translate"),n.k0s()()()()),2&t&&(n.Y8G("ngIf",o.isLoading),n.R7$(4),n.Y8G("ngClass",n.eq3(17,W,"Tous"===o.selectedPackaging)),n.R7$(2),n.SpI(" ",n.bMT(7,11,"order-new-page.second-step.all")," "),n.R7$(2),n.Y8G("ngForOf",o.storesPackaging)("ngForTrackBy",o.trackByFn),n.R7$(3),n.SpI(" ",n.bMT(12,13,"order-new-page.second-step.title")," "),n.R7$(3),n.Y8G("ngIf",null==o.productsVariety?null:o.productsVariety.length),n.R7$(1),n.Y8G("ngIf",!(null!=o.cartItems&&o.cartItems.length)),n.R7$(2),n.Y8G("readonly",o.nbrOfProductSelect<=0)("disabled",o.nbrOfProductSelect<=0),n.R7$(2),n.SpI(" ",n.bMT(20,15,"order-new-page.second-step.next-button-label")," "))},dependencies:[l.Jm,l.ZB,l.KW,l.he,l.IO,h.YU,h.Sq,h.bT,se.V,L._,p.D9],styles:['@charset "UTF-8";*[_ngcontent-%COMP%]{margin:0;padding:0}.header-md[_ngcontent-%COMP%]:after{background-image:none!important}.modal[_ngcontent-%COMP%]{--border-radius: calc(60 * var(--res))}.input-group[_ngcontent-%COMP%]{margin-top:calc(75 * var(--res))}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{--padding-start: 0;margin-bottom:calc(50 * var(--res));--placeholder-color: $color-six}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-input[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{font-family:Mont Regular;font-weight:400;font-size:calc(42 * var(--res));color:#1e1e1e}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{padding-top:calc(25 * var(--res));padding-bottom:calc(25 * var(--res))}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]{margin-bottom:calc(50 * var(--res));display:flex;align-items:center;justify-content:space-between;border-bottom-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-250, #c8c7cc)));border-bottom-width:.55px;border-bottom-style:solid}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{width:90%;margin-bottom:0;--border-color: transparent;--inner-border-width: 0px 0px 0px 0px;--border-style: none}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-checkbox[_ngcontent-%COMP%]{margin-top:5%}.bg-primary[_ngcontent-%COMP%]{color:#143c5d}.message[_ngcontent-%COMP%]{margin-bottom:calc(75 * var(--res))}.message[_ngcontent-%COMP%]   ion-title[_ngcontent-%COMP%]{font-weight:800;font-size:calc(70 * var(--res));padding-bottom:calc(25 * var(--res))}.message[_ngcontent-%COMP%]   ion-text[_ngcontent-%COMP%]{font-weight:400;color:#757474;display:block;line-height:1.5em!important}.btn--meduim[_ngcontent-%COMP%]{height:calc(130 * var(--res));font-size:calc(50 * var(--res));font-family:var(--mont-bold)}.btn--meduim[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:var(--mont-bold)}.btn--small[_ngcontent-%COMP%]{height:calc(100 * var(--res))}.btn--round[_ngcontent-%COMP%]{--border-radius: 50%;padding-inline-start:0;padding-inline-end:0;--padding-start: 0;--padding-end: 0}.btn--border-primary[_ngcontent-%COMP%]{--background: transparent;--background-activated: #c4e6d0;--border-color: var(--ion-color-primary);--border-style: solid;--border-width: 2px}.btn--border-secondary[_ngcontent-%COMP%]{--border-width: 2px;--background: transparent;--background-activated: #ddabb2;--border-color: var(--ion-color-secondary);--border-style: solid}.btn--upper[_ngcontent-%COMP%]{text-transform:uppercase}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]{overflow:hidden}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar{width:5px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-track{box-shadow:inset 0 0 5px #546e8d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background:#143c5d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover{background:#143c5d}.bottom-sheet-content[_ngcontent-%COMP%]{background:#fdfdfe;height:100%}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-thumbnail[_ngcontent-%COMP%]{margin-left:calc(31.25 * var(--res));background-color:#1e1e1e;--border-radius: 50%;--size: 23}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:Mont Bold;font-size:calc(45 * var(--res));text-align:center;color:#757474}.bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%], .bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]{padding:calc(75 * var(--res))}.font-size-5[_ngcontent-%COMP%]{font-size:calc(42 * var(--res))}#container[_ngcontent-%COMP%]{padding:calc(41 * var(--res)) 0;padding-top:0;height:100%;background-color:transparent;border-top-left-radius:calc(50 * var(--res));border-top-right-radius:calc(50 * var(--res));overflow-x:hidden;overflow-y:auto}#container[_ngcontent-%COMP%]   .searchbar[_ngcontent-%COMP%]{margin-bottom:5px}#container[_ngcontent-%COMP%]   .products[_ngcontent-%COMP%]{padding:0 calc(41 * var(--res));height:max-content;overflow:hidden;display:flex;flex-wrap:wrap;gap:30px;justify-content:center;width:100%}#container[_ngcontent-%COMP%]   .products[_ngcontent-%COMP%]   .item[_ngcontent-%COMP%]   .item-block[_ngcontent-%COMP%]{width:max-content;position:relative}#container[_ngcontent-%COMP%]   .products[_ngcontent-%COMP%]   .item[_ngcontent-%COMP%]   .item-block[_ngcontent-%COMP%]   .icon-container[_ngcontent-%COMP%]{position:absolute;z-index:10;right:5px;top:5px;height:calc(75 * var(--res));width:calc(75 * var(--res));padding:4px;border-radius:50%;display:flex;justify-content:center;align-self:flex-start;background:#419CFB}#container[_ngcontent-%COMP%]   .empty-list[_ngcontent-%COMP%]{display:flex;align-items:center;flex-direction:column;justify-content:center;height:100%}#container[_ngcontent-%COMP%]   .empty-list[_ngcontent-%COMP%]   ion-img[_ngcontent-%COMP%]{width:50%;padding:1rem 0}.header[_ngcontent-%COMP%]{padding:calc(41 * var(--res))}.header[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{color:#143c5d;font-family:Mont Bold;font-weight:700;font-size:calc(45 * var(--res));margin-bottom:13%}.btn-validate[_ngcontent-%COMP%]{padding:0 calc(41 * var(--res));position:fixed;bottom:6%;width:calc(100% - 10 * var(--res))}.part-search[_ngcontent-%COMP%]   .space-h-v[_ngcontent-%COMP%]{padding-top:5px;margin-bottom:20px}.part-search[_ngcontent-%COMP%]   .searchbar[_ngcontent-%COMP%]{--background: transparent;height:calc(5 * var(--resH));border:none;--box-shadow: none;border-bottom:1px solid #1e1e1e}.part-search-chip[_ngcontent-%COMP%]{display:flex;white-space:nowrap;overflow-x:scroll;scrollbar-width:none;-webkit-scrollbar-width:none}.part-search-chip[_ngcontent-%COMP%]   .space-all[_ngcontent-%COMP%]{padding:0 0 0 calc(50 * var(--res))}.part-search-chip[_ngcontent-%COMP%]   .space-h-v[_ngcontent-%COMP%]   ion-chip[_ngcontent-%COMP%]{--background: #fafafa;--color: var(--ion-color-primary);border:#E4F1FF 1px solid;margin-right:8px;padding:0 12px;width:max-content;height:max-content;border-radius:25px}.part-search-chip[_ngcontent-%COMP%]   .space-h-v[_ngcontent-%COMP%]   ion-chip[_ngcontent-%COMP%]   ion-text[_ngcontent-%COMP%]{font-family:var(--mont-bold);font-weight:700;padding-block:.5rem;font-size:11px}.part-search-chip[_ngcontent-%COMP%]   .space-h-v[_ngcontent-%COMP%]   ion-chip.active[_ngcontent-%COMP%]{color:#f0f0f0!important;background:var(--ion-color-primary)!important}']})}}return r})()},{path:"third-step",component:Zn},{path:"four-step",component:cn},{path:"schedule",loadChildren:()=>i.e(8110).then(i.bind(i,58110)).then(r=>r.SchedulePageModule)},{path:"",redirectTo:"first-step",pathMatch:"full"},{path:"**",redirectTo:"first-step"}]},{path:"**",redirectTo:""}];let ve=(()=>{class r{static{this.\u0275fac=function(t){return new(t||r)}}static{this.\u0275mod=n.$C({type:r})}static{this.\u0275inj=n.G2t({imports:[P.iI.forChild(he),P.iI]})}}return r})(),Ce=(()=>{class r{static{this.\u0275fac=function(t){return new(t||r)}}static{this.\u0275mod=n.$C({type:r})}static{this.\u0275inj=n.G2t({providers:[l.W3],imports:[c.YN,l.bv,h.MD,M.vj,f.G,p.h,c.X1,ve]})}}return r})()},38112:(E,y,i)=>{i.d(y,{A:()=>v});var l=i(73308),M=i(94934),h=i(45312),p=i(2978),f=i(26409),c=i(82571),_=i(14599),O=i(33607);let v=(()=>{class S{constructor(a,g,n,P){this.http=a,this.commonSrv=g,this.storageService=n,this.baseUrlService=P,this.url=this.baseUrlService.getOrigin()+h.c.basePath+"compute-price",this.user=JSON.parse(this.storageService.load("USER_INFO"))}getDetailOfCart(a){var g=this;return(0,l.A)(function*(){try{return yield(0,M.s)(g.http.post(`${g.url}`,a,{headers:{Authorization:`Bearer ${g.user?.accessToken}`}}))}catch(n){const b={message:g.commonSrv.getError("",n).message,color:"danger"};return yield g.commonSrv.showToast(b),n}})()}static{this.\u0275fac=function(g){return new(g||S)(p.KVO(f.Qq),p.KVO(c.h),p.KVO(_.n),p.KVO(O.K))}}static{this.\u0275prov=p.jDH({token:S,factory:S.\u0275fac,providedIn:"root"})}}return S})()},61955:(E,y,i)=>{i.d(y,{o:()=>O});var l=i(73308),M=i(26409),h=i(94934),p=i(45312),f=i(2978),c=i(82571),_=i(33607);let O=(()=>{class v{constructor(m,a,g){this.http=m,this.commonSrv=a,this.baseUrlService=g,this.url=this.baseUrlService.getOrigin()+p.c.basePath+"shippings"}getShippingAddress(m){var a=this;return(0,l.A)(function*(){try{let g=new M.Nl;return m?.startRef&&(g=g.append("startRef.storeRef",m?.startRef)),void 0!==m?.category&&(g=g.append("category",m?.category)),void 0!==m?.companyId&&(g=g.append("companyId",m?.companyId)),yield(0,h.s)(a.http.get(`${a.url}`,{params:g}))}catch(g){const P={message:a.commonSrv.getError("",g).message,color:"danger"};return yield a.commonSrv.showToast(P),g}})()}getDefaultShippingAddress(m){var a=this;return(0,l.A)(function*(){try{let g=new M.Nl;return m?.startRef&&(g=g.append("startRef.storeRef",m?.startRef)),void 0!==m?.category&&(g=g.append("category",m?.category)),void 0!==m?.commercialRegion&&(g=g.append("address.commercialRegion",m?.commercialRegion)),yield(0,h.s)(a.http.get(`${a.url}/default-shipping`,{params:g}))}catch(g){const P={message:a.commonSrv.getError("",g).message,color:"danger"};return yield a.commonSrv.showToast(P),g}})()}getCategoriesShippingAddress(m){var a=this;return(0,l.A)(function*(){try{let g=new M.Nl;return m?.startRef&&(g=g.append("startRef.storeRef",m?.startRef)),void 0!==m?.category&&(g=g.append("category",m?.category)),yield(0,h.s)(a.http.get(`${a.url}/categories`,{params:g}))}catch(g){const P={message:a.commonSrv.getError("",g).message,color:"danger"};return yield a.commonSrv.showToast(P),g}})()}static{this.\u0275fac=function(a){return new(a||v)(f.KVO(M.Qq),f.KVO(c.h),f.KVO(_.K))}}static{this.\u0275prov=f.jDH({token:v,factory:v.\u0275fac,providedIn:"root"})}}return v})()},97130:(E,y,i)=>{i.d(y,{I:()=>O});var l=i(73308),M=i(45312),h=i(2978),p=i(77897),f=i(49957),c=i(82571),_=i(14599);let O=(()=>{class v{constructor(m,a,g,n){this.platform=m,this.appVersion=a,this.commonService=g,this.storageService=n}isUpToDate(){var m=this;return(0,l.A)(function*(){let a;try{a=yield m.getAppVersion()}catch(g){"cordova_not_available"===g&&(a=m.platform.is("android")?M.c?.appVersionAndroid:M.c?.appVersionIos)}finally{return m.isLatestVersion(a)}})()}isLatestVersion(m){var a=this;return(0,l.A)(function*(){let g;console.log("appVersion:",m);try{g=yield a.getMinimalVersion()}catch{g=a.platform.is("android")?M.c?.appVersionAndroid:M.c?.appVersionIos}finally{return console.log(g,m),a.isNewerVersion(g,m)}})()}getMinimalVersion(){var m=this;return(0,l.A)(function*(){return m.currentPlatform=m.platform.is("android")?"android":"ios",yield m.commonService.getMinimalAppVersion(m.currentPlatform)})()}getAppVersion(){var m=this;return(0,l.A)(function*(){return yield m.appVersion.getVersionNumber()})()}isNewerVersion(m,a){const g=m?.split("."),n=a?.split("."),P=Math?.max(g?.length,n?.length);for(let b=0;b<P;b++){let j=parseInt(g[b]||"0",10),T=parseInt(n[b]||"0",10);if(T>j)return!0;if(T<j)return!1}return!0}static{this.\u0275fac=function(a){return new(a||v)(h.KVO(p.OD),h.KVO(f.U),h.KVO(c.h),h.KVO(_.n))}}static{this.\u0275prov=h.jDH({token:v,factory:v.\u0275fac,providedIn:"root"})}}return v})()}}]);