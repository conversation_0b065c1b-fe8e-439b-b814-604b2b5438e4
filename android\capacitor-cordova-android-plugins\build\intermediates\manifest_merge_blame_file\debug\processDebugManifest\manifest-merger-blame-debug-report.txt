1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:amazon="http://schemas.amazon.com/apk/res/android"
3    xmlns:android="http://schemas.android.com/apk/res/android"
4    package="capacitor.cordova.android.plugins" >
5
6    <uses-sdk android:minSdkVersion="23" />
7
8    <uses-feature
8-->C:\Users\<USER>\Desktop\Projet LONDO TECH\LA PASTA\la-pasta-mobile\android\capacitor-cordova-android-plugins\src\main\AndroidManifest.xml:11:1-83
9        android:name="android.hardware.telephony"
9-->C:\Users\<USER>\Desktop\Projet LONDO TECH\LA PASTA\la-pasta-mobile\android\capacitor-cordova-android-plugins\src\main\AndroidManifest.xml:11:15-56
10        android:required="false" />
10-->C:\Users\<USER>\Desktop\Projet LONDO TECH\LA PASTA\la-pasta-mobile\android\capacitor-cordova-android-plugins\src\main\AndroidManifest.xml:11:57-81
11
12    <application>
12-->C:\Users\<USER>\Desktop\Projet LONDO TECH\LA PASTA\la-pasta-mobile\android\capacitor-cordova-android-plugins\src\main\AndroidManifest.xml:4:1-10:15
13        <receiver
13-->C:\Users\<USER>\Desktop\Projet LONDO TECH\LA PASTA\la-pasta-mobile\android\capacitor-cordova-android-plugins\src\main\AndroidManifest.xml:5:1-9:12
14            android:name="com.andreszs.smsretriever.SMSBroadcastReceiver"
14-->C:\Users\<USER>\Desktop\Projet LONDO TECH\LA PASTA\la-pasta-mobile\android\capacitor-cordova-android-plugins\src\main\AndroidManifest.xml:5:35-96
15            android:exported="true"
15-->C:\Users\<USER>\Desktop\Projet LONDO TECH\LA PASTA\la-pasta-mobile\android\capacitor-cordova-android-plugins\src\main\AndroidManifest.xml:5:11-34
16            android:permission="com.google.android.gms.auth.api.phone.permission.SEND" >
16-->C:\Users\<USER>\Desktop\Projet LONDO TECH\LA PASTA\la-pasta-mobile\android\capacitor-cordova-android-plugins\src\main\AndroidManifest.xml:5:97-171
17            <intent-filter>
17-->C:\Users\<USER>\Desktop\Projet LONDO TECH\LA PASTA\la-pasta-mobile\android\capacitor-cordova-android-plugins\src\main\AndroidManifest.xml:6:3-8:19
18                <action android:name="com.google.android.gms.auth.api.phone.SMS_RETRIEVED" />
18-->C:\Users\<USER>\Desktop\Projet LONDO TECH\LA PASTA\la-pasta-mobile\android\capacitor-cordova-android-plugins\src\main\AndroidManifest.xml:7:5-81
18-->C:\Users\<USER>\Desktop\Projet LONDO TECH\LA PASTA\la-pasta-mobile\android\capacitor-cordova-android-plugins\src\main\AndroidManifest.xml:7:13-79
19            </intent-filter>
20        </receiver>
21    </application>
22
23</manifest>
